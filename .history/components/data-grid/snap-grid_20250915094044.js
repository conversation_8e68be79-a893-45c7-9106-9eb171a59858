/**
 * SnapGrid - Advanced Data Grid Engine
 * A comprehensive client-side data grid inspired by AG Grid
 * Following the single-file architecture pattern of snap-charts.js
 *
 * Features:
 * - Virtual scrolling for performance with large datasets
 * - Column menus with sorting, filtering, grouping
 * - Client-side data operations
 * - Cell editing and custom renderers
 * - Performance monitoring integration
 * - Modern UI design matching Snap Dashboard
 *
 * @version 1.0.0
 * <AUTHOR> Dashboard Team
 */

class SnapGrid {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        if (!this.container) {
            throw new Error('SnapGrid: Container element not found');
        }

        // Core configuration
        this.options = {
            // Data options
            data: options.data || [],
            columns: options.columns || [],

            // Performance options
            virtualScrolling: options.virtualScrolling !== false,
            rowHeight: options.rowHeight || 40,
            headerHeight: options.headerHeight || 48,
            bufferSize: options.bufferSize || 10,

            // Feature options
            sortable: options.sortable !== false,
            filterable: options.filterable !== false,
            resizable: options.resizable !== false,
            selectable: options.selectable !== false,
            editable: options.editable || false,

            // Query/worker options
            enableWorkerQuery: options.enableWorkerQuery === true,
            workerThreshold: options.workerThreshold || 50000,

            // Checkbox and selection options
            checkboxSelection: options.checkboxSelection !== false,
            headerCheckboxSelection: options.headerCheckboxSelection !== false,

            // Column options
            columnDragging: options.columnDragging !== false,
            pinnedColumns: options.pinnedColumns || ['checkbox'], // Default pinned columns

            // UI options
            theme: options.theme || 'default', // default, compact, dense, comfortable
            showHeader: options.showHeader !== false,
            showFooter: options.showFooter || false,

            // Callbacks
            onRowClick: options.onRowClick || null,
            onCellEdit: options.onCellEdit || null,
            onSort: options.onSort || null,
            onFilter: options.onFilter || null,
            onSelectionChanged: options.onSelectionChanged || null,
            onColumnMoved: options.onColumnMoved || null,

            ...options,
            // Enforce non-editable grid and footer visible by default
            editable: false,
            showFooter: options.showFooter !== false
        };

        // Internal state
        this.data = Array.isArray(this.options.data) ? this.options.data.slice() : [];
        // Index-based row model
        this.filteredIdx = this.data.map((_, i) => i);
        this.sortedIdx = this.filteredIdx.slice();
        this.selectedRows = new Set();
        this.sortState = {};
        this.filterState = {};
        this.columnState = {};

        // Selection state
        this.allRowsSelected = false;
        this.indeterminateSelection = false;

        // Chunked loading state
        this.expectedTotalRecords = null; // Track expected total during chunked loading

        // Ensure pinned offset maps always exist
        // Some environments reported an error when accessing index 0
        // on undefined maps during initial render. Initialize them here
        // and recompute later via computePinnedOffsets().
        this.pinnedLeftOffsets = {};
        this.pinnedRightOffsets = {};

        // Column dragging state
        this.isDraggingColumn = false;
        this.draggedColumn = null;
        this.dragStartX = 0;
        this.dragStartIndex = 0;

        // Column menu state
        this.currentColumnMenu = null;

        // Prepare columns with checkbox column if enabled
        this.prepareColumns();

        // Virtual scrolling state
        this.scrollTop = 0;
        this.visibleStartIndex = 0;
        this.visibleEndIndex = 0;
        this.totalHeight = 0;
        this._scrollScale = 1;
        this._MAX_SCROLL_PX = 33500000; // ~33.5M px safe cap

        // DOM elements
        this.gridElement = null;
        this.headerElement = null;
        this.bodyElement = null;
        this.viewportElement = null;
        this.scrollbarElement = null;

        // Performance monitoring
        this.renderStartTime = 0;
        this.lastRenderDuration = 0;
        this._scrollRaf = null;

        // Worker query adapter (optional)
        this._queryAdapter = (window.GridQueryAdapter && this.options.enableWorkerQuery) ? new window.GridQueryAdapter('performance-optimizations/grid-query-worker.js') : null;
        this._lastQueryId = 0;
        // Unique values cache
        this._uniqueCache = Object.create(null);

        // Filter debouncing
        this.filterDebounceTimer = null;
        this.filterDebounceDelay = 300; // 300ms delay

        // Expose AG-style APIs
        this.buildApis();

        this.init();
    }

    /**
     * Normalize pinned value to AG semantics: 'left' | 'right' | null
     */
    sanitizePinned(pinned) {
        if (pinned === 'left' || pinned === 'right') return pinned;
        if (pinned === true) return 'left';
        return null;
    }

    /**
     * Prepare columns with checkbox column if enabled
     */
    prepareColumns() {
        this.processedColumns = [...this.options.columns];

        // Enforce AG-style pinning semantics on provided columns
        this.processedColumns.forEach(col => {
            col.pinned = this.sanitizePinned(col.pinned);
            // Support AG-style hide/visible flags without breaking existing code
            if (col.visible === false) col.hide = true;
            if (col.hide === false) delete col.hide;
        });

        // Add checkbox column if enabled
        if (this.options.checkboxSelection) {
            const checkboxColumn = {
                field: 'checkbox',
                headerName: '',
                width: 46, // Will be set properly by calculateMinHeaderWidth
                pinned: 'left',
                sortable: false,
                filterable: false,
                resizable: false,
                editable: false,
                cellRenderer: this.renderCheckboxCell.bind(this),
                headerRenderer: this.renderCheckboxHeader.bind(this)
            };

            this.processedColumns.unshift(checkboxColumn);

            // Insert Preview column immediately after checkbox if not provided
            if (!this.processedColumns.find(c => c.field === 'preview')) {
                const previewColumn = {
                    field: 'preview',
                    headerName: 'Preview',
                    width: 60, // Will be set properly by calculateMinHeaderWidth
                    pinned: 'left',
                    sortable: false,
                    filterable: false,
                    resizable: false,
                    editable: false,
                    cellRenderer: () => '<div class="preview-square" title="Preview"></div>'
                };
                this.processedColumns.splice(1, 0, previewColumn);
            }
        }

        // Ensure Actions column exists and is pinned-right (enforce even if provided by user)
        const existingActions = this.processedColumns.find(c => c.field === 'actions');
        if (!existingActions) {
            const actionsColumn = {
                field: 'actions',
                headerName: 'Actions',
                width: 96, // Will be set properly by calculateMinHeaderWidth
                pinned: 'right',
                sortable: false,
                filterable: false,
                resizable: false,
                editable: false,
                cellRenderer: () => `
                    <div class="actions-container">
                        <span class="listing-edit-ic" title="Edit"><img src="assets/edit-ic.svg" alt="Edit" width="14" height="14"></span>
                        <span class="listing-analyse-ic" title="Analyse"><img src="assets/analyse-ic.svg" alt="Analyse" width="16" height="16"></span>
                    </div>
                `
            };
            this.processedColumns.push(actionsColumn);
        } else {
            existingActions.pinned = 'right';
            existingActions.sortable = false;
            existingActions.filterable = false;
            existingActions.resizable = false;
            existingActions.editable = false;
            if (!existingActions.width) existingActions.width = 96;
        }

        // Sort columns by pinned status
        this.sortColumnsByPinned();

        // Ensure every column has a default width for horizontal scrolling
        this.processedColumns.forEach(col => {
            if (!col.width) {
                // Calculate minimum width based on header text
                const minWidth = this.calculateMinHeaderWidth(col);
                const defaultWidth = 150; // Default fallback width
                col.width = Math.max(minWidth, defaultWidth);
            } else {
                // Even if width is provided, ensure it meets minimum requirements
                const minWidth = this.calculateMinHeaderWidth(col);
                col.width = Math.max(minWidth, col.width);
            }
        });
    }

    /**
     * Helper to sort processedColumns by pinned status (left -> center -> right)
     */
    sortColumnsByPinned() {
        this.processedColumns.sort((a, b) => {
            const aPinned = a.pinned === 'left' ? 0 : a.pinned === 'right' ? 2 : 1;
            const bPinned = b.pinned === 'left' ? 0 : b.pinned === 'right' ? 2 : 1;
            return aPinned - bPinned;
        });
    }

    /**
     * Build gridApi and columnApi with AG-style methods
     */
    buildApis() {
        const self = this;

        // Column API
        this.columnApi = {
            // Pin/unpin a column
            setColumnPinned(field, pinned) {
                return self.setColumnPinned(field, pinned);
            },
            // Apply an array of column state entries
            applyColumnState(params = {}) {
                return self.applyColumnState(params);
            },
            // Get current column state
            getColumnState() {
                return self.getColumnState();
            },
            // Set column width
            setColumnWidth(field, width) {
                return self.setColumnWidth(field, width);
            },
            // Move column to target visible index (center block)
            moveColumnByField(field, toIndex) {
                return self.moveColumnByField(field, toIndex);
            },
            // Show/hide column
            setColumnVisible(field, visible) {
                return self.setColumnVisible(field, visible);
            },
            // Accessors
            getAllColumns() { return [...self.processedColumns]; },
            getColumn(field) { return self.getColumn(field); }
        };

        // Grid API
        this.gridApi = {
            setRowData(data) { return self.setRowData(data); },
            refreshCells(params = {}) { return self.refreshCells(params); },
            redrawRows(params = {}) { return self.redrawRows(params); },
            ensureColumnVisible(field) { return self.ensureColumnVisible(field); },
            ensureIndexVisible(index) { return self.ensureIndexVisible(index); },
            sizeColumnsToFit() { return self.sizeColumnsToFit(); },
            getDisplayedRowCount() { return self.sortedIdx.length; },
            getDisplayedRowAtIndex(i) { const idx = self.sortedIdx[i]; return (idx != null) ? self.data[idx] : null; }
        };
    }

    /**
     * Render checkbox cell
     */
    renderCheckboxCell(value, column, rowData, rowIndex) {
        const isSelected = this.selectedRows.has(rowIndex);
        const iconName = isSelected ? 'checkbox-ic.svg' : 'uncheckedbox-ic.svg';

        return `
            <div class="snap-grid-checkbox-cell" data-row-index="${rowIndex}">
                <img src="assets/${iconName}"
                     alt="${isSelected ? 'Selected' : 'Not selected'}"
                     class="snap-grid-checkbox-icon">
            </div>
        `;
    }

    /**
     * Render checkbox header
     */
    renderCheckboxHeader() {
        let iconName = 'uncheckedbox-ic.svg';

        if (this.allRowsSelected) {
            iconName = 'checkbox-ic.svg';
        } else if (this.indeterminateSelection) {
            iconName = 'indeterminate-ic.svg';
        }

        return `
            <div class="snap-grid-checkbox-header">
                <img src="assets/${iconName}"
                     alt="Select all"
                     class="snap-grid-checkbox-icon">
            </div>
        `;
    }

    /**
     * Initialize the grid
     */
    init() {
        if (window.SnapLogger) window.SnapLogger.info('Initializing SnapGrid');
        this.renderStartTime = performance.now();

        this.setupDOM();
        this.setupEventListeners();
        this.processData();
        this.render();

        const initDuration = performance.now() - this.renderStartTime;
        if (window.SnapLogger) window.SnapLogger.info(`SnapGrid initialized in ${initDuration.toFixed(2)}ms`);
    }

    /**
     * Set up the DOM structure
     */
    setupDOM() {
        // Clear container
        this.container.innerHTML = '';
        this.container.className = `snap-grid ${this.options.theme}`;

        // Add ARIA attributes for accessibility
        this.container.setAttribute('role', 'grid');
        this.container.setAttribute('aria-label', 'Data grid');
        this.container.setAttribute('tabindex', '0');

        // Create main grid structure
        this.gridElement = document.createElement('div');
        this.gridElement.className = 'snap-grid-container';

        // Create controls header (styled, no legacy logic)
        this.createControlsHeader();

        // Create header
        if (this.options.showHeader) {
            this.headerElement = document.createElement('div');
            this.headerElement.className = 'snap-grid-header';
            this.headerElement.setAttribute('role', 'rowgroup');
            this.gridElement.appendChild(this.headerElement);
        }

        // Create body with viewport for virtual scrolling
        this.bodyElement = document.createElement('div');
        this.bodyElement.className = 'snap-grid-body';
        this.bodyElement.setAttribute('role', 'rowgroup');

        this.viewportElement = document.createElement('div');
        this.viewportElement.className = 'snap-grid-viewport';
        this.bodyElement.appendChild(this.viewportElement);

        this.gridElement.appendChild(this.bodyElement);

        // Use body's native horizontal scrollbar only

        // Create footer stats bar
        this.createFooter();
        this.container.appendChild(this.gridElement);

        // Set up virtual scrolling container
        this.setupVirtualScrolling();

        // Add screen reader announcements
        this.createAriaLiveRegion();
    }

    /**
     * Create the styled controls header (filters, layout, actions, info)
     */
    createControlsHeader() {
        const controls = document.createElement('div');
        controls.className = 'snap-grid-controls-header';

        // Left group: Filters dropdown + Layout dropdown + Clear Filters
        const left = document.createElement('div');
        left.className = 'snap-grid-controls-left';

        // Filters dropdown
        const filtersDropdown = this.createFiltersDropdown();
        const filtersWrapper = document.createElement('div');
        filtersWrapper.className = 'snap-grid-filters-dropdown';
        filtersWrapper.appendChild(filtersDropdown);
        left.appendChild(filtersWrapper);

        // Layout dropdown
        const layoutDropdown = this.createLayoutDropdown();
        const layoutWrapper = document.createElement('div');
        layoutWrapper.className = 'snap-grid-layout-dropdown';
        layoutWrapper.appendChild(layoutDropdown);
        left.appendChild(layoutWrapper);

        // Clear filters button
        const clearBtn = document.createElement('button');
        clearBtn.className = 'snap-grid-clear-filters-btn';
        clearBtn.textContent = 'Clear Filters';
        clearBtn.addEventListener('click', () => {
            this.clearFilters();
            if (typeof this.options.onClearFilters === 'function') {
                this.options.onClearFilters();
            }
        });
        left.appendChild(clearBtn);

        // Center group: Delete + Export
        const center = document.createElement('div');
        center.className = 'snap-grid-controls-center';

        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'snap-grid-delete-btn';
        deleteBtn.title = 'Delete Selected';
        deleteBtn.innerHTML = '<img src="assets/delete-ic.svg" alt="Delete" />';
        deleteBtn.addEventListener('click', () => {
            if (typeof this.options.onDelete === 'function') {
                this.options.onDelete(this.getSelectedData());
            }
        });
        center.appendChild(deleteBtn);

        const exportBtn = document.createElement('button');
        exportBtn.className = 'snap-grid-export-btn';
        exportBtn.title = 'Export Data';
        exportBtn.innerHTML = '<img src="assets/export-ic.svg" alt="Export" />';
        exportBtn.addEventListener('click', () => this.exportToCsv());
        center.appendChild(exportBtn);

        // Right group: Loaded info pill
        const right = document.createElement('div');
        right.className = 'snap-grid-controls-right';
        const loadedInfo = document.createElement('div');
        loadedInfo.className = 'snap-grid-loaded-info';
        right.appendChild(loadedInfo);

        controls.appendChild(left);
        controls.appendChild(center);
        controls.appendChild(right);

        this.gridElement.appendChild(controls);

        // Cache references
        this.controlsHeaderElement = controls;
        this.controls = {
            clearBtn,
            deleteBtn,
            exportBtn,
            loadedInfo,
            filtersDropdown,
            layoutDropdown
        };
    }

    /**
     * Create footer stats bar
     */
    createFooter() {
        if (!this.options.showFooter) return;

        const footer = document.createElement('div');
        footer.className = 'snap-grid-footer';

        const stats = document.createElement('div');
        stats.className = 'snap-grid-footer-stats';

        const makeStat = (label) => {
            const item = document.createElement('div');
            item.className = 'snap-grid-stat-item';
            const lab = document.createElement('div');
            lab.className = 'snap-grid-stat-label';
            lab.textContent = label;
            const val = document.createElement('div');
            val.className = 'snap-grid-stat-value';
            val.textContent = '0';
            item.appendChild(lab);
            item.appendChild(val);
            return { item, val };
        };

        const rows = makeStat('ROWS');
        const filtered = makeStat('FILTERED');
        const selected = makeStat('SELECTED');

        stats.appendChild(rows.item);
        stats.appendChild(filtered.item);
        stats.appendChild(selected.item);
        footer.appendChild(stats);
        this.gridElement.appendChild(footer);

        this.footerElement = footer;
        this.footer = { rows: rows.val, filtered: filtered.val, selected: selected.val };
    }

    /**
     * Generic dropdown builder with styles compatible to .snap-dropdown
     */
    createDropdown({ className = '', items = [], selectedValue = null, onChange = null } = {}) {
        const dropdown = document.createElement('div');
        dropdown.className = `snap-dropdown ${className}`.trim();

        const header = document.createElement('div');
        header.className = 'dropdown-header';
        const title = document.createElement('span');
        title.textContent = '';
        const caret = document.createElement('img');
        caret.src = 'assets/dropdown-ic.svg';
        caret.alt = 'Open';
        header.appendChild(title);
        header.appendChild(caret);

        const menu = document.createElement('div');
        menu.className = 'dropdown-menu hidden';

        const setSelected = (value, text) => {
            title.textContent = text;
            selectedValue = value;
            // Update selected class
            menu.querySelectorAll('.dropdown-item').forEach(el => {
                if (el.dataset.value === String(value)) el.classList.add('selected');
                else el.classList.remove('selected');
            });
        };

        items.forEach(item => {
            if (item.divider) {
                const div = document.createElement('div');
                div.className = 'dropdown-divider';
                menu.appendChild(div);
                return;
            }
            const opt = document.createElement('div');
            opt.className = 'dropdown-item';
            opt.dataset.value = String(item.value);
            if (item.icon) {
                const icon = document.createElement('span');
                icon.className = 'item-icon';
                icon.innerHTML = `<img src="assets/${item.icon}" alt="" />`;
                opt.appendChild(icon);
            }
            const text = document.createElement('span');
            text.className = 'item-text';
            text.textContent = item.text;
            opt.appendChild(text);
            opt.addEventListener('click', (e) => {
                e.stopPropagation();
                setSelected(item.value, item.text);
                menu.classList.add('hidden');
                dropdown.classList.remove('focused');
                if (typeof onChange === 'function') onChange(item.value, item);
                // Dispatch event for external listeners
                dropdown.dispatchEvent(new CustomEvent('change', { detail: { value: item.value, text: item.text, item } }));
            });
            menu.appendChild(opt);
        });

        header.addEventListener('click', (e) => {
            e.stopPropagation();
            const open = !menu.classList.contains('hidden');
            // Close others
            document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                if (d !== dropdown) {
                    d.classList.remove('focused');
                    const m = d.querySelector('.dropdown-menu');
                    if (m) m.classList.add('hidden');
                }
            });
            if (!open) {
                dropdown.classList.add('focused');
                menu.classList.remove('hidden');
            } else {
                dropdown.classList.remove('focused');
                menu.classList.add('hidden');
            }
        });

        document.addEventListener('click', () => {
            dropdown.classList.remove('focused');
            menu.classList.add('hidden');
        });

        dropdown.appendChild(header);
        dropdown.appendChild(menu);

        // Initialize selection
        const initItem = items.find(i => i.value === selectedValue) || items.find(i => !i.divider) || null;
        if (initItem) setSelected(initItem.value, initItem.text);

        return dropdown;
    }

    /**
     * Filters dropdown: includes "All" and any provided presets
     */
    createFiltersDropdown() {
        const presets = Array.isArray(this.options.filterPresets) ? this.options.filterPresets : [];
        const items = [
            { value: 'all', text: 'All' },
            ...(presets.length ? [{ divider: true }] : []),
            ...presets.map(p => ({ value: p.value ?? p, text: p.text ?? String(p), icon: p.icon }))
        ];
        const dropdown = this.createDropdown({ className: '', items, selectedValue: 'all', onChange: (val) => {
            if (val === 'all') {
                this.clearFilters();
                if (typeof this.options.onFilterPresetSelected === 'function') this.options.onFilterPresetSelected(val);
                return;
            }
            if (typeof this.options.onFilterPresetSelected === 'function') {
                this.options.onFilterPresetSelected(val);
            }
        } });
        return dropdown;
    }

    /**
     * Layout dropdown: Default, Save, Delete + optional saved layouts (callback-based)
     */
    createLayoutDropdown() {
        const baseItems = [
            { value: 'default', text: 'Default Layout', icon: 'checked-option-ic.svg' },
            { divider: true },
            { value: 'save', text: 'Save Layout', icon: 'save-layout-ic.svg' },
            { value: 'delete', text: 'Delete Selected', icon: 'delete-ic.svg' }
        ];
        const savedLayouts = Array.isArray(this.options.savedLayouts) ? this.options.savedLayouts : [];
        if (savedLayouts.length) {
            baseItems.splice(2, 0, ...savedLayouts.map(l => ({ value: l.id || l.value, text: l.name || l.text, icon: 'checked-option-ic.svg' })));
        }

        const dropdown = this.createDropdown({ className: 'layout-dropdown', items: baseItems, selectedValue: 'default', onChange: (val, item) => {
            if (val === 'default') {
                if (typeof this.options.onApplyDefaultLayout === 'function') this.options.onApplyDefaultLayout();
            } else if (val === 'save') {
                if (typeof this.options.onSaveLayout === 'function') this.options.onSaveLayout(this.getCurrentLayout());
            } else if (val === 'delete') {
                if (typeof this.options.onDeleteLayout === 'function') this.options.onDeleteLayout(this.currentLayoutId || null);
            } else {
                this.currentLayoutId = val;
                if (typeof this.options.onApplyLayout === 'function') this.options.onApplyLayout(val);
            }
        } });
        return dropdown;
    }

    /**
     * Capture current layout (order and widths)
     */
    getCurrentLayout() {
        return {
            columns: this.processedColumns.map(c => ({ field: c.field, width: c.width || null, pinned: c.pinned || null }))
        };
    }

    /**
     * Set up virtual scrolling
     */
    setupVirtualScrolling() {
        if (!this.options.virtualScrolling) return;

        // Create scrollable container
        this.scrollContainer = document.createElement('div');
        this.scrollContainer.className = 'snap-grid-scroll-container';
        this.bodyElement.appendChild(this.scrollContainer);

        // Calculate total height
        this.updateVirtualScrolling();
    }

    /**
     * Update virtual scrolling calculations
     */
    updateVirtualScrolling() {
        if (!this.options.virtualScrolling) return;

        const rowCount = this.sortedIdx.length;
        this.totalHeight = rowCount * this.options.rowHeight;
        const capped = Math.min(this.totalHeight, this._MAX_SCROLL_PX);
        this._scrollScale = (capped > 0) ? (this.totalHeight / capped) : 1;

        if (this.scrollContainer) {
            this.scrollContainer.style.height = `${capped}px`;
        }

        this.calculateVisibleRange();
    }

    /**
     * Calculate which rows should be visible
     */
    calculateVisibleRange() {
        if (!this.options.virtualScrolling) {
            this.visibleStartIndex = 0;
            this.visibleEndIndex = this.sortedIdx.length;
            return;
        }

        // Check if bodyElement is initialized (similar to AG Grid's null checks)
        if (!this.bodyElement) {
            this.visibleStartIndex = 0;
            this.visibleEndIndex = this.sortedIdx.length;
            return;
        }

        const containerHeight = this.bodyElement.clientHeight;
        const rowHeight = this.options.rowHeight;
        const bufferSize = this.options.bufferSize;
        const scale = this._scrollScale || 1;
        const virtualTop = this.scrollTop * scale;
        const virtualBottom = (this.scrollTop + containerHeight) * scale;

        this.visibleStartIndex = Math.max(0, Math.floor(virtualTop / rowHeight) - bufferSize);
        this.visibleEndIndex = Math.min(
            this.sortedIdx.length,
            Math.ceil(virtualBottom / rowHeight) + bufferSize
        );
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Scroll sync and virtualization
        if (this.bodyElement) {
            try {
                this.bodyElement.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
            } catch (e) {
                // Fallback for older browsers
                this.bodyElement.addEventListener('scroll', this.handleScroll.bind(this));
            }
        }

        // No dedicated horizontal scrollbar - use body scrollbar only

        // Window resize
        window.addEventListener('resize', this.handleResize.bind(this));

        // Click events (delegated)
        this.container.addEventListener('click', this.handleClick.bind(this));

        // Keyboard events
        this.container.addEventListener('keydown', this.handleKeydown.bind(this));

        // Column resize events
        if (this.options.resizable) {
            this.setupColumnResize();
        }

        // Column dragging events
        if (this.options.columnDragging) {
            this.container.addEventListener('dragstart', this.handleDragStart.bind(this));
            this.container.addEventListener('dragover', this.handleDragOver.bind(this));
            this.container.addEventListener('drop', this.handleDrop.bind(this));
            this.container.addEventListener('dragend', this.handleDragEnd.bind(this));
        }
    }

    /**
     * Handle scroll events for virtual scrolling and menu tracking - exact copy from old grid
     */
    handleScroll(event) {
        const target = event.target;
        const newLeft = target.scrollLeft || 0;
        const newTop = target.scrollTop || 0;

        // Horizontal: keep header in sync
        if (newLeft !== this.scrollLeft) {
            this.scrollLeft = newLeft;
            this.syncHorizontalScroll();
        }

        // Vertical: only update virtualization if enabled and changed
        if (this.options.virtualScrolling && newTop !== this.scrollTop) {
            this.scrollTop = newTop;
            if (!this._scrollRaf) {
                this._scrollRaf = requestAnimationFrame(() => {
                    this._scrollRaf = null;
                    this.calculateVisibleRange();
                    this.renderRows();
                    this.updateHeaderFooterStats();
                });
            }
        }

        // Update active menu position if it exists, with collision detection - exact copy from old grid
        if (this.activeMenu && this.activeMenuTarget) {
            try {
                this.positionMenuWithCollisionDetection(this.activeMenu, this.activeMenuTarget);
            } catch (error) {
                console.warn('Error repositioning menu during scroll:', error);
                // Don't close the menu, just skip repositioning this time
            }
        }
    }

    // Removed handleHScroll - using body scrollbar only

    /**
     * Handle window resize
     */
    handleResize() {
        this.calculateVisibleRange();
        this.render();
        this.updateHorizontalMetrics();
        this.syncHorizontalScroll();
    }

    /**
     * Handle click events
     */
    handleClick(event) {
        const target = event.target;

        // Column menu button click
        if (target.closest('.snap-grid-column-menu-btn')) {
            // Already handled in the button's event listener
            return;
        }

        // Checkbox click handling
        if (target.closest('.snap-grid-checkbox-cell')) {
            this.handleCheckboxClick(event);
            return;
        }

        // Header checkbox click
        if (target.closest('.snap-grid-checkbox-header')) {
            this.handleHeaderCheckboxClick(event);
            return;
        }

        // Header click for sorting
        if (target.closest('.snap-grid-header-cell')) {
            this.handleHeaderClick(event);
        }

        // Row click for selection
        if (target.closest('.snap-grid-row')) {
            this.handleRowClick(event);
        }

        // Cell click for editing
        if (target.closest('.snap-grid-cell')) {
            this.handleCellClick(event);
        }
    }

    /**
     * Handle keyboard events
     */
    handleKeydown(event) {
        // Arrow key navigation
        if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
            this.handleArrowKeys(event);
        }

        // Enter for editing
        if (event.key === 'Enter') {
            this.handleEnterKey(event);
        }

        // Escape to cancel editing
        if (event.key === 'Escape') {
            this.handleEscapeKey(event);
        }

        // Ctrl/Cmd + A: Select all
        if ((event.ctrlKey || event.metaKey) && event.key === 'a') {
            event.preventDefault();
            this.toggleAllRowsSelection();
        }
    }

    /**
     * Handle drag start for column dragging
     */
    handleDragStart(event) {
        const headerText = event.target.closest('.snap-grid-header-text');
        if (!headerText || !headerText.draggable) return;
        
        const headerCell = headerText.closest('.snap-grid-header-cell');
        if (!headerCell) return;

        const columnIndex = parseInt(headerCell.dataset.index);
        const column = this.processedColumns[columnIndex];

        // Don't allow dragging pinned columns
        if (column.pinned) {
            event.preventDefault();
            return;
        }

        this.isDraggingColumn = true;
        this.draggedColumn = columnIndex;
        this.dragStartX = event.clientX;
        this.dragStartIndex = columnIndex;

        headerCell.classList.add('dragging');

        // Set drag data
        event.dataTransfer.effectAllowed = 'move';
        event.dataTransfer.setData('text/plain', columnIndex.toString());
        
        // Set the entire header cell as the drag image instead of just the text
        event.dataTransfer.setDragImage(headerCell, event.offsetX, event.offsetY);

        this.announceToScreenReader(`Started dragging column ${column.headerName || column.field}`);
    }

    /**
     * Handle drag over for column dragging
     */
    handleDragOver(event) {
        if (!this.isDraggingColumn) return;

        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';

        const headerCell = event.target.closest('.snap-grid-header-cell');
        if (!headerCell) return;

        const targetIndex = parseInt(headerCell.dataset.index);
        const targetColumn = this.processedColumns[targetIndex];

        // Don't allow dropping on pinned columns
        if (targetColumn.pinned) return;

        // Remove previous drag-over indicators
        document.querySelectorAll('.snap-grid-header-cell.drag-over').forEach(cell => {
            cell.classList.remove('drag-over');
        });

        // Add drag-over indicator
        if (targetIndex !== this.draggedColumn) {
            headerCell.classList.add('drag-over');
        }
    }

    /**
     * Handle drop for column dragging
     */
    handleDrop(event) {
        if (!this.isDraggingColumn) return;

        event.preventDefault();

        const headerCell = event.target.closest('.snap-grid-header-cell');
        if (!headerCell) return;

        const targetIndex = parseInt(headerCell.dataset.index);
        const targetColumn = this.processedColumns[targetIndex];

        // Don't allow dropping on pinned columns
        if (targetColumn.pinned || targetIndex === this.draggedColumn) return;

        // Move column
        this.moveColumn(this.draggedColumn, targetIndex);

        // Trigger callback
        if (this.options.onColumnMoved) {
            this.options.onColumnMoved(this.draggedColumn, targetIndex);
        }
    }

    /**
     * Handle drag end for column dragging
     */
    handleDragEnd(event) {
        this.isDraggingColumn = false;

        // Remove all drag-related classes
        document.querySelectorAll('.snap-grid-header-cell.dragging').forEach(cell => {
            cell.classList.remove('dragging');
        });

        document.querySelectorAll('.snap-grid-header-cell.drag-over').forEach(cell => {
            cell.classList.remove('drag-over');
        });

        this.draggedColumn = null;
        this.dragStartX = 0;
        this.dragStartIndex = 0;
    }

    /**
     * Move column from one index to another
     */
    moveColumn(fromIndex, toIndex) {
        if (fromIndex === toIndex) return;

        // Store current scroll position to maintain it after reordering
        const currentScrollLeft = this.bodyElement?.scrollLeft || 0;

        // Move column in processedColumns array
        const column = this.processedColumns.splice(fromIndex, 1)[0];
        this.processedColumns.splice(toIndex, 0, column);

        // Update pinned offsets after column reordering
        this.computePinnedOffsets();

        // Re-render the grid
        this.render();

        // Restore horizontal scroll position
        if (this.bodyElement && this.bodyElement.scrollLeft !== currentScrollLeft) {
            this.bodyElement.scrollLeft = currentScrollLeft;
        }

        // Sync horizontal scroll to ensure proper alignment
        this.syncHorizontalScroll();

        const columnName = column.headerName || column.field;
        this.announceToScreenReader(`Column ${columnName} moved from position ${fromIndex + 1} to position ${toIndex + 1}`);
    }

    /**
     * Process and prepare data
     */
    processData() {
        // Invalidate unique-values cache on every data/filters recompute
        this._uniqueCache = Object.create(null);
        // Apply filters using index arrays
        this.applyFilters();
        // Apply sorting using index arrays
        this.applySorting();
        // Update virtual scrolling metrics
        this.updateVirtualScrolling();
    }

    /**
     * Apply current filters to data
     */
    applyFilters() {
        const filters = Object.entries(this.filterState);
        if (!filters.length) {
            // No filters: identity mapping
            this.filteredIdx = this.data.map((_, i) => i);
            return;
        }
        const out = [];
        const len = this.data.length;
        for (let i = 0; i < len; i++) {
            const row = this.data[i];
            let pass = true;
            for (let j = 0; j < filters.length; j++) {
                const [field, filter] = filters[j];
                if (!this.matchesFilter(row[field], filter)) { pass = false; break; }
            }
            if (pass) out.push(i);
        }
        this.filteredIdx = out;
    }

    /**
     * Debounced filter application for performance
     */
    applyFiltersDebounced(immediate = false) {
        // Clear existing timer
        if (this.filterDebounceTimer) {
            clearTimeout(this.filterDebounceTimer);
            this.filterDebounceTimer = null;
        }

        if (immediate) {
            // Apply immediately
            this.processData();
            this.render();
        } else {
            // Apply after delay
            this.filterDebounceTimer = setTimeout(() => {
                this.processData();
                this.render();
                this.filterDebounceTimer = null;
            }, this.filterDebounceDelay);
        }
    }

    /**
     * Debounced filter application for single filter changes (like old grid)
     */
    applyFilterDebounced() {
        // Clear existing timer
        if (this.filterDebounceTimer) {
            clearTimeout(this.filterDebounceTimer);
        }

        // Set new timer
        this.filterDebounceTimer = setTimeout(() => {
            this.processData();
            this.render();
            this.filterDebounceTimer = null;
        }, this.filterDebounceDelay);
    }

    /**
     * Refresh active menu target reference after re-rendering
     * This fixes the issue where filtering causes menu positioning to break
     */
    refreshActiveMenuTarget() {
        if (!this.activeMenu || !this.activeMenuTarget) {
            return; // No active menu to refresh
        }

        // Get the field name from the menu
        const fieldName = this.activeMenu.getAttribute('data-field');
        if (!fieldName) {
            console.warn('⚠️ Active menu has no field name, cannot refresh target');
            return;
        }

        // Find the new header cell for this field after re-rendering
        const newHeaderCell = this.container.querySelector(`.snap-grid-header-cell[data-field="${fieldName}"]`);
        if (newHeaderCell) {
            this.activeMenuTarget = newHeaderCell;
            console.log('🔄 Refreshed active menu target for field:', fieldName);

            // Reposition the menu with the new target
            try {
                this.positionMenuWithCollisionDetection(this.activeMenu, this.activeMenuTarget);
            } catch (error) {
                console.warn('Error repositioning menu after target refresh:', error);
            }
        } else {
            console.warn('⚠️ Could not find new header cell for field:', fieldName);
            // If target column is hidden/not found, position menu on the left side
            this.positionMenuOnLeftSide();
        }
    }

    /**
     * Check if value matches filter criteria - comprehensive implementation with AND/OR logic
     */
    matchesFilter(value, filter) {
        if (!filter) return true;

        const {
            type,
            operator,
            value: filterValue,
            secondOperator,
            secondValue,
            logicOperator,
            checkedValues
        } = filter;

        /* debug: matchesFilter params */

        // Handle checkbox filters first
        let passesCheckboxFilter = true; // Default to true if no checkbox filter

        if (checkedValues && Array.isArray(checkedValues)) {
            const stringValue = String(value ?? '');

            /* debug: checkbox filter processing */

            // Empty checkedValues array means "hide all rows"
            if (!checkedValues || checkedValues.length === 0) {
                window.SnapLogger?.debug('Empty checkedValues - hiding all rows');
                passesCheckboxFilter = false;
            } else {
                // Check if this value is in the allowed list
                const isValueAllowed = checkedValues.includes(stringValue);
                passesCheckboxFilter = isValueAllowed;

                // For checkbox-only filters (type='checkbox' or no text operators), return checkbox result immediately
                if (type === 'checkbox' || !operator || operator === 'pleaseSelect' || operator === 'in') {
                    window.SnapLogger?.debug('Checkbox-only result', isValueAllowed);
                    return isValueAllowed;
                }

                // For mixed filters, checkbox is a prerequisite - value must be in allowed list
                if (!isValueAllowed) {
                    window.SnapLogger?.debug('Value not in checkbox allowlist, blocking');
                    return false;
                }

                window.SnapLogger?.debug('Value passes checkbox filter, checking text filter');
            }
        }

        // Handle text/number/date filtering
        let textFilterResult = true;

        // Apply first condition if operator is valid
        if (operator && operator !== 'pleaseSelect') {
            /* debug: applying single condition */
            // For date filters, predefined ranges (like 'lastYear') have null filterValue but should still be processed
            if (type === 'date' || (filterValue !== undefined && filterValue !== null)) {
                textFilterResult = this.applySingleCondition(value, type, operator, filterValue);
                /* debug: single condition result */
            } else {
                /* debug: skipping single condition - no valid filterValue */
                textFilterResult = true;
            }
        }

        // Apply second condition if exists
        let secondResult = true;
        let hasSecondCondition = false;

        if (secondOperator && secondOperator !== 'pleaseSelect') {
            if (secondValue !== undefined && secondValue !== null && secondValue.length > 0) {
                secondResult = this.applySingleCondition(value, type, secondOperator, secondValue);
                hasSecondCondition = true;
            }
        }

        // Only combine results if we actually have a second condition
        if (hasSecondCondition) {
            /* debug: applying logic operator */

            if (logicOperator === 'AND') {
                textFilterResult = textFilterResult && secondResult;
                /* debug: AND logic applied */
            } else if (logicOperator === 'OR') {
                textFilterResult = textFilterResult || secondResult;
                /* debug: OR logic applied */
            }

            /* debug: combined filter result */
        } else {
            /* debug: no second condition */
        }

        // Combine checkbox and text filter results
        // For mixed filters, BOTH checkbox and text filters must pass
        const finalResult = passesCheckboxFilter && textFilterResult;

        /* debug: final filter result */

        return finalResult;
    }

    /**
     * Apply single condition to value
     */
    applySingleCondition(value, type, operator, filterValue) {
        switch (type) {
            case 'text':
                return this.applyTextFilter(value, operator, filterValue);
            case 'number':
                return this.applyNumberFilter(value, operator, filterValue);
            case 'date':
                return this.applyDateFilter(value, operator, filterValue);
            case 'boolean':
                return this.applyBooleanFilter(value, operator, filterValue);
            default:
                return true;
        }
    }

    /**
     * Apply text filter
     */
    applyTextFilter(value, operator, filterValue) {
        const str = String(value ?? '').toLowerCase();
        const filter = String(filterValue || '').toLowerCase();

        /* debug: applyTextFilter */

        let result;
        switch (operator) {
            case 'contains':
                result = str.includes(filter);
                break;
            case 'notContains':
                result = !str.includes(filter);
                break;
            case 'equals':
                result = str === filter;
                break;
            case 'notEquals':
                result = str !== filter;
                break;
            case 'startsWith':
                result = str.startsWith(filter);
                break;
            case 'endsWith':
                result = str.endsWith(filter);
                break;
            case 'blank':
                result = !str || str.trim() === '';
                break;
            case 'notBlank':
                result = str && str.trim() !== '';
                break;
            default:
                window.SnapLogger?.warn('Unknown text filter operator', operator);
                result = true;
        }

        /* debug: text filter result */
        return result;
    }

    /**
     * Apply number filter
     */
    applyNumberFilter(value, operator, filterValue) {
        // Helper: robust numeric parsing (handles currency strings like "$19.34", commas, etc.)
        const toNum = (v) => {
            if (v === null || v === undefined || v === '') return NaN;
            if (typeof v === 'number') return v;
            if (typeof v === 'string') {
                const cleaned = v.replace(/[^0-9.-]/g, '');
                return cleaned === '' || cleaned === '-' || cleaned === '.' ? NaN : Number(cleaned);
            }
            return Number(v);
        };

        // Handle blank/not blank first
        if (operator === 'blank') {
            return value === null || value === undefined || value === '';
        }
        if (operator === 'notBlank') {
            return value !== null && value !== undefined && value !== '';
        }

        const num = toNum(value);
        if (isNaN(num)) {
            // If cell value cannot be parsed as number, it cannot satisfy numeric filters
            return false;
        }

        // In-range is special: filterValue is an object, so don't coerce it to Number
        if (operator === 'inRange') {
            let from = NaN, to = NaN;
            if (filterValue && typeof filterValue === 'object') {
                from = toNum(filterValue.fromValue);
                to = toNum(filterValue.toValue);
            }
            // Both bounds provided
            if (!isNaN(from) && !isNaN(to)) return num >= from && num <= to;
            // Only lower bound
            if (!isNaN(from)) return num >= from;
            // Only upper bound
            if (!isNaN(to)) return num <= to;
            // No valid bounds -> do not filter out
            return true;
        }

        // For other operators, parse the filterValue as a number
        const filter = toNum(filterValue);
        /* debug: applyNumberFilter */

        if (isNaN(filter)) {
            window.SnapLogger?.warn('Invalid numeric filter value', filterValue);
            return true; // Don't filter out if filter value is invalid
        }

        switch (operator) {
            case 'equals':
                return num === filter;
            case 'notEquals':
                return num !== filter;
            case 'lessThan':
                return num < filter;
            case 'lessThanOrEqual':
                return num <= filter;
            case 'greaterThan':
                return num > filter;
            case 'greaterThanOrEqual':
                return num >= filter;
            default:
                window.SnapLogger?.warn('Unknown number filter operator', operator);
                return true;
        }
    }

    /**
     * Apply date filter
     */
    applyDateFilter(value, operator, filterValue) {
        // For predefined date ranges, filterValue might be null
        if (!filterValue && ['today', 'yesterday', 'thisWeek', 'lastWeek', 'thisMonth', 'lastMonth', 'thisYear', 'lastYear'].includes(operator)) {
            return this.applyPredefinedDateFilter(value, operator);
        }

        // For custom date comparisons
        if (!filterValue) return true;

        const cellDate = new Date(value);
        const filterDate = new Date(filterValue);

        if (isNaN(cellDate.getTime()) || isNaN(filterDate.getTime())) {
            return false; // Invalid dates don't match
        }

        switch (operator) {
            case 'equals':
                return cellDate.toDateString() === filterDate.toDateString();
            case 'notEquals':
                return cellDate.toDateString() !== filterDate.toDateString();
            case 'lessThan':
                return cellDate < filterDate;
            case 'greaterThan':
                return cellDate > filterDate;
            case 'inRange':
                if (filterValue && typeof filterValue === 'object') {
                    const fromDate = filterValue.fromValue ? new Date(filterValue.fromValue) : null;
                    const toDate = filterValue.toValue ? new Date(filterValue.toValue) : null;

                    if (fromDate && toDate) {
                        return cellDate >= fromDate && cellDate <= toDate;
                    } else if (fromDate) {
                        return cellDate >= fromDate;
                    } else if (toDate) {
                        return cellDate <= toDate;
                    }
                }
                return true;
            default:
                console.warn('⚠️ Unknown date filter operator:', operator);
                return true;
        }
    }

    /**
     * Apply predefined date filter (today, yesterday, etc.)
     */
    applyPredefinedDateFilter(value, operator) {
        const cellDate = new Date(value);
        if (isNaN(cellDate.getTime())) return false;

        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        switch (operator) {
            case 'today':
                return cellDate.toDateString() === today.toDateString();
            case 'yesterday':
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                return cellDate.toDateString() === yesterday.toDateString();
            case 'thisWeek':
                const startOfWeek = new Date(today);
                startOfWeek.setDate(today.getDate() - today.getDay());
                return cellDate >= startOfWeek && cellDate <= today;
            case 'lastWeek':
                const lastWeekStart = new Date(today);
                lastWeekStart.setDate(today.getDate() - today.getDay() - 7);
                const lastWeekEnd = new Date(lastWeekStart);
                lastWeekEnd.setDate(lastWeekStart.getDate() + 6);
                return cellDate >= lastWeekStart && cellDate <= lastWeekEnd;
            case 'thisMonth':
                return cellDate.getMonth() === today.getMonth() && cellDate.getFullYear() === today.getFullYear();
            case 'lastMonth':
                const lastMonth = new Date(today);
                lastMonth.setMonth(today.getMonth() - 1);
                return cellDate.getMonth() === lastMonth.getMonth() && cellDate.getFullYear() === lastMonth.getFullYear();
            case 'thisYear':
                return cellDate.getFullYear() === today.getFullYear();
            case 'lastYear':
                return cellDate.getFullYear() === today.getFullYear() - 1;
            default:
                return true;
        }
    }

    /**
     * Apply boolean filter
     */
    applyBooleanFilter(value, operator, filterValue) {
        const boolValue = Boolean(value);
        const filterBool = Boolean(filterValue);

        switch (operator) {
            case 'equals':
                return boolValue === filterBool;
            case 'notEquals':
                return boolValue !== filterBool;
            default:
                return true;
        }
    }

    /**
     * Apply current sorting to filtered data
     */
    applySorting() {
        // Start from filtered index array
        const sortEntries = Object.entries(this.sortState);
        if (!sortEntries.length) {
            this.sortedIdx = this.filteredIdx.slice();
            return;
        }
        const data = this.data;
        const idx = this.filteredIdx.slice();
        const comparator = (ia, ib) => {
            const a = data[ia];
            const b = data[ib];
            for (let k = 0; k < sortEntries.length; k++) {
                const [field, direction] = sortEntries[k];
                const aVal = a[field];
                const bVal = b[field];
                let cmp = 0;
                if (aVal === bVal) cmp = 0;
                else if (aVal == null) cmp = 1; // nulls last
                else if (bVal == null) cmp = -1;
                else if (typeof aVal === 'number' && typeof bVal === 'number') cmp = aVal - bVal;
                else cmp = String(aVal).localeCompare(String(bVal));
                if (cmp !== 0) {
                    return direction === 'desc' ? -cmp : cmp;
                }
            }
            return 0;
        };
        idx.sort(comparator);
        this.sortedIdx = idx;
    }

    /**
     * Main render method
     */
    render() {
        this.renderStartTime = performance.now();

        // Keep Actions column always pinned to right edge
        // Never auto-fit columns - preserve original widths
        // This ensures Actions stays at right: 0 regardless of column count

        if (this.options.showHeader) {
            this.renderHeader();
        }

        this.renderRows();

        // Update header/footer stats if present
        this.updateHeaderFooterStats();

        // Refresh active menu target after any render to prevent positioning issues
        // This ensures menu positioning works correctly after DOM recreation
        this.refreshActiveMenuTarget();

        // Keep header horizontally aligned with body after any re-render (e.g., sort/filter)
        this.syncHorizontalScroll();

        this.lastRenderDuration = performance.now() - this.renderStartTime;
        if (this.lastRenderDuration > 16) { // More than one frame
            console.warn(`⚠️ SnapGrid render took ${this.lastRenderDuration.toFixed(2)}ms`);
        }
    }

    /**
     * If center columns are narrower than the space between pinned columns,
     * expand them proportionally to fill the gap. Returns true if a re-render
     * was triggered by sizing.
     */
    autoFitColumnsIfNeeded() {
        if (!this.bodyElement || !Array.isArray(this.processedColumns)) return false;

        const viewportW = this.bodyElement.clientWidth || 0;
        if (viewportW <= 0) return false;

        let leftPinned = 0, rightPinned = 0, centerWidth = 0, centerCount = 0;
        this.processedColumns.forEach(c => {
            const w = c.hide ? 0 : (c.width || 0);
            if (c.pinned === 'left') leftPinned += w;
            else if (c.pinned === 'right') rightPinned += w;
            else { centerWidth += w; if (!c.hide) centerCount++; }
        });

        // Do not auto-fit when 0 or 1 center column is visible.
        // With a single center column, preserve its natural/original width
        // and leave remaining space empty between left pinned and right pinned.
        if (centerCount <= 1) return false;

        const available = Math.max(0, viewportW - leftPinned - rightPinned - this.getScrollbarWidth());
        if (available > centerWidth + 1) {
            this._autoFittingColumns = true;
            const fitted = this.sizeColumnsToFit();
            this._autoFittingColumns = false;
            return !!fitted; // sizeColumnsToFit triggers render
        }
        return false;
    }

    /**
     * Render the header row
     */
    renderHeader() {
        if (!this.headerElement) return;

        this.headerElement.innerHTML = '';
        const scroller = document.createElement('div');
        scroller.className = 'snap-grid-header-scroller';
        const headerRow = document.createElement('div');
        headerRow.className = 'snap-grid-header-row';
        headerRow.setAttribute('role', 'row');

        // Compute pinned offsets (left/right) for header rendering
        this.computePinnedOffsets();

        this.processedColumns.forEach((column, index) => {
            const headerCell = document.createElement('div');
            headerCell.className = 'snap-grid-header-cell';
            headerCell.dataset.field = column.field;
            headerCell.dataset.index = index;

            // Add pinned class
            if (column.pinned) {
                headerCell.classList.add(`pinned-${column.pinned}`);
                // Apply offset so pinned columns don't overlap
                if (column.pinned === 'left') {
                    // Guard against undefined maps during early render
                    const off = (this.pinnedLeftOffsets && this.pinnedLeftOffsets[index]) || 0;
                    headerCell.style.left = off + 'px';
                } else if (column.pinned === 'right') {
                    const off = (this.pinnedRightOffsets && this.pinnedRightOffsets[index]) || 0;
                    // For Actions column, always position flush against right edge
                    if (column.field === 'actions') {
                        headerCell.style.right = '0px';
                    } else {
                        // For other pinned-right columns, apply exact offset without extra gap
                        headerCell.style.right = `${off}px`;
                    }
                }
            }

            // Add draggable attributes for non-pinned columns
            if (this.options.columnDragging && !column.pinned) {
                headerCell.classList.add('draggable');
            }

            // Add accessibility attributes
            this.addHeaderAccessibility(headerCell, column, index);

            // Set width
            if (column.width) {
                const w = (column.hide ? 0 : column.width);
                headerCell.style.width = `${w}px`;
                headerCell.style.minWidth = `${w}px`;
            }

            // Hidden columns: keep DOM for indexing/drag, but not visible
            if (column.hide) {
                headerCell.style.display = 'none';
            }

            // Header content
            const headerContent = document.createElement('div');
            headerContent.className = 'snap-grid-header-content';

            // Use custom header renderer if available
            if (column.headerRenderer && typeof column.headerRenderer === 'function') {
                const customHeader = column.headerRenderer();
                if (typeof customHeader === 'string') {
                    headerContent.innerHTML = customHeader;
                } else {
                    headerContent.appendChild(customHeader);
                }
            } else {
                const headerText = document.createElement('span');
                headerText.className = 'snap-grid-header-text';
                headerText.textContent = column.headerName || column.field;
                
                // Add draggable attribute to header text for non-pinned columns
                if (this.options.columnDragging && !column.pinned) {
                    headerText.draggable = true;
                    headerText.classList.add('draggable');
                }
                
                headerContent.appendChild(headerText);
            }

            // Sort indicator (skip for checkbox/preview/actions)
            if (this.options.sortable && column.sortable !== false && !['checkbox','preview','actions'].includes(column.field)) {
                const sortIcon = document.createElement('span');
                sortIcon.className = 'snap-grid-sort-icon';

                const sortDirection = this.sortState[column.field];
                if (sortDirection) {
                    sortIcon.classList.add(`sort-${sortDirection}`);
                    const iconName = sortDirection === 'asc' ? 'ascending-ic.svg' : 'descending-ic.svg';
                    sortIcon.innerHTML = `<img src="assets/${iconName}" alt="${sortDirection === 'asc' ? 'Ascending' : 'Descending'}" class="sort-icon-img">`;
                }

                headerContent.appendChild(sortIcon);
            }

            // Column menu button (only if column allows filtering/sorting and not special fields)
            if (((this.options.filterable && column.filterable !== false) || (this.options.sortable && column.sortable !== false))
                && !['checkbox','preview','actions'].includes(column.field)) {
                const menuButton = document.createElement('button');
                menuButton.className = 'snap-grid-column-menu-btn';
                menuButton.innerHTML = '<img src="assets/menu-dots-ic.svg" alt="Menu" width="10" height="10">';
                menuButton.setAttribute('aria-label', `Column menu for ${column.headerName || column.field}`);
                menuButton.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.showColumnMenu(headerCell, column);
                });
                headerContent.appendChild(menuButton);
            }

            headerCell.appendChild(headerContent);

            // Resize handle
            if (this.options.resizable && column.resizable !== false) {
                const resizeHandle = document.createElement('div');
                resizeHandle.className = 'snap-grid-resize-handle';
                headerCell.appendChild(resizeHandle);
            }

            headerRow.appendChild(headerCell);
        });

        // Always use viewport width to keep Actions pinned to right edge
        // Columns use their original widths, empty space fills the gap
        const viewportWidth = (this.bodyElement?.clientWidth || this.container?.clientWidth || 1200);
        const totalColumnsWidth = this.getTotalColumnsWidth();
        headerRow.style.width = `${Math.max(totalColumnsWidth, viewportWidth)}px`;
        // Ensure the header scroller itself stretches to container width so pinned-right remains at edge
        scroller.style.minWidth = `${viewportWidth}px`;
        scroller.appendChild(headerRow);
        this.headerElement.appendChild(scroller);
    }

    /**
     * Render visible rows
     */
    renderRows() {
        if (!this.viewportElement) return;
        // Compute pinned and visible column ranges once per render
        this.computePinnedOffsets();
        this.computeVisibleColumns();

        this.viewportElement.innerHTML = '';

        const fragment = document.createDocumentFragment();

        for (let i = this.visibleStartIndex; i < this.visibleEndIndex; i++) {
            const dataIndex = this.sortedIdx[i];
            const rowData = this.data[dataIndex];
            if (!rowData) continue;

            const row = this.createRow(rowData, i);
            fragment.appendChild(row);
        }

        this.viewportElement.appendChild(fragment);

        // Update viewport position for virtual scrolling without transforms
        if (this.options.virtualScrolling) {
            const scale = this._scrollScale || 1;
            const offsetVirtual = this.visibleStartIndex * this.options.rowHeight;
            const offsetY = Math.floor(offsetVirtual / scale);
            this.viewportElement.style.transform = '';
            this.viewportElement.style.paddingTop = `${offsetY}px`;
        } else {
            this.viewportElement.style.paddingTop = '';
        }

        // Ensure viewport content width and scrollbar widths are accurate
        this.updateHorizontalMetrics();
    }

    /**
     * Update counts and info pills in header/footer
     */
    updateHeaderFooterStats() {
        // Header loaded info
        if (this.controls?.loadedInfo) {
            const shown = this.sortedIdx.length;
            // Use expected total during chunked loading, otherwise use current data length
            const total = this.expectedTotalRecords !== null ? this.expectedTotalRecords : this.data.length;
            this.controls.loadedInfo.textContent = `${shown.toLocaleString()} / ${total.toLocaleString()}`;
        }

        // Footer stats
        if (this.footer) {
            const total = this.data.length;
            const filtered = this.filteredIdx.length;
            const selected = this.selectedRows.size;
            this.footer.rows.textContent = total.toLocaleString();
            this.footer.filtered.textContent = filtered.toLocaleString();
            this.footer.selected.textContent = selected.toLocaleString();
        }
    }

    /**
     * Create a single row element
     */
    createRow(rowData, rowIndex) {

        const row = document.createElement('div');
        row.className = 'snap-grid-row';
        row.dataset.index = rowIndex;
        row.setAttribute('role', 'row');
        row.setAttribute('aria-rowindex', rowIndex + 2); // +2 because header is row 1

        // Row selection state
        if (this.selectedRows.has(rowIndex)) {
            row.classList.add('selected');
            row.setAttribute('aria-selected', 'true');
        } else {
            row.setAttribute('aria-selected', 'false');
        }

        // Row height and width - match header width calculation
        row.style.height = `${this.options.rowHeight}px`;
        const viewportWidth = (this.bodyElement?.clientWidth || this.container?.clientWidth || 1200);
        const totalRowWidth = this.getTotalColumnsWidth();
        row.style.width = `${Math.max(totalRowWidth, viewportWidth)}px`;

        this.processedColumns.forEach((column, columnIndex) => {
            // Skip non-pinned columns that are outside the horizontal viewport
            if (!column.pinned && this._colVisibleFlags && this._colVisibleFlags[columnIndex] === false) {
                return; // skip creating this cell
            }

            const cell = document.createElement('div');
            cell.className = 'snap-grid-cell';
            cell.dataset.field = column.field;
            cell.dataset.row = rowIndex;
            cell.dataset.column = columnIndex;

            // Add pinned class
            // Hard-enforce Actions as pinned-right to avoid any drift
            if (column.field === 'actions') {
                cell.classList.add('pinned-right');
                cell.style.right = '0px';
            } else if (column.pinned) {
                cell.classList.add(`pinned-${column.pinned}`);
                if (column.pinned === 'left') {
                    const off = (this.pinnedLeftOffsets && this.pinnedLeftOffsets[columnIndex]) || 0;
                    cell.style.left = off + 'px';
                } else if (column.pinned === 'right') {
                    const off = (this.pinnedRightOffsets && this.pinnedRightOffsets[columnIndex]) || 0;
                    // For other pinned-right columns, apply exact offset without extra gap
                    cell.style.right = `${off}px`;
                }
            }

            // Add accessibility attributes
            this.addCellAccessibility(cell, column, rowData, rowIndex, columnIndex);

            // Set width to match header
            const w = (column.hide ? 0 : column.width);
            cell.style.width = `${w}px`;
            cell.style.minWidth = `${w}px`;

            // Hidden columns: keep DOM for indexing, but not visible
            if (column.hide) {
                cell.style.display = 'none';
            }

            // Cell content
            const cellValue = rowData[column.field];
            const cellContent = this.renderCellContent(cellValue, column, rowData, rowIndex);

            // Create cell content wrapper for proper ellipsis handling
            const cellContentWrapper = document.createElement('div');
            cellContentWrapper.className = 'snap-grid-cell-content';

            if (typeof cellContent === 'string') {
                cellContentWrapper.innerHTML = cellContent;
            } else {
                cellContentWrapper.appendChild(cellContent);
            }

            cell.appendChild(cellContentWrapper);

            // Cell editing
            if (this.options.editable && column.editable !== false) {
                cell.classList.add('editable');
            }

            row.appendChild(cell);
        });

        return row;
    }

    /**
     * Sum of column widths for horizontal overflow
     */
    getTotalColumnsWidth() {
        return this.processedColumns.reduce((sum, col) => sum + (col.hide ? 0 : (col.width || 0)), 0);
    }

    /**
     * Keep header horizontally synced with body scroll
     */
    syncHorizontalScroll() {
        if (!this.headerElement || !this.bodyElement) return;
        const scroller = this.headerElement.querySelector('.snap-grid-header-scroller');
        if (scroller) {
            const x = this.bodyElement?.scrollLeft || 0;

            // DON'T apply transform to the entire scroller - this moves pinned columns too
            // Instead, apply transform only to non-pinned header cells
            const allHeaderCells = this.headerElement.querySelectorAll('.snap-grid-header-cell');
            allHeaderCells.forEach(el => {
                if (el.classList.contains('pinned-left') || el.classList.contains('pinned-right')) {
                    // Pinned columns should stay fixed - no transform
                    el.style.transform = '';
                } else {
                    // Non-pinned columns should scroll with content
                    el.style.transform = `translateX(${-x}px)`;
                }
            });
        }
    }
    /**
     * Compute which columns are visible in the current horizontal viewport
     * Always include pinned columns. Add small buffer columns around edges.
     */
    computeVisibleColumns() {
        if (!this.bodyElement || !this.processedColumns) return;
        // Simplify for correctness: render all non-hidden columns; pinned columns remain sticky via CSS
        // This avoids any header/body desync while we stabilize column virtualization later.
        this._colVisibleFlags = this.processedColumns.map(col => !col.hide);
    }


    /**
     * Reconcile horizontal sizes: compute total width, compare to measured header width,
     * and apply the larger value to viewport and rows to guarantee full scroll range.
     */
    updateHorizontalMetrics() {
        if (!this.viewportElement) return;
        const rawTotal = this.getTotalColumnsWidth();
        const minViewportWidth = (this.bodyElement?.clientWidth || this.container?.clientWidth || rawTotal);
        const total = Math.max(rawTotal, minViewportWidth);
        // Also enforce min width on header scroller for consistency
        const scroller = this.headerElement?.querySelector?.('.snap-grid-header-scroller');
        if (scroller) scroller.style.minWidth = `${minViewportWidth}px`;

        // Apply to viewport
        this.viewportElement.style.width = `${total}px`;

        // No dedicated horizontal scrollbar to update

        // Apply to existing rows
        const rows = this.viewportElement.querySelectorAll('.snap-grid-row');
        rows.forEach(r => { r.style.width = `${total}px`; });

        this.totalColumnsWidth = total;

        // Update vertical scrollbar gutter offset (so pinned-right is never overlapped)
        const sbw = this.getScrollbarWidth();
        if (this.container) {
            this.container.style.setProperty('--vscroll-offset', `${sbw}px`);
        }
    }

    /**
     * Compute sticky offsets for pinned columns
     */
    computePinnedOffsets() {
        this.pinnedLeftOffsets = {};
        this.pinnedRightOffsets = {};

        let left = 0;
        for (let i = 0; i < this.processedColumns.length; i++) {
            const col = this.processedColumns[i];
            if (col.pinned === 'left') {
                this.pinnedLeftOffsets[i] = left;
                if (!col.hide) left += (col.width || 0);
            }
        }

        let right = 0;
        for (let i = this.processedColumns.length - 1; i >= 0; i--) {
            const col = this.processedColumns[i];
            if (col.pinned === 'right') {
                this.pinnedRightOffsets[i] = right;
                if (!col.hide) right += (col.width || 0);
            }
        }
    }

    /**
     * Column helpers and API methods
     */
    getColumn(field) {
        return this.processedColumns.find(c => c.field === field) || null;
    }

    /**
     * Calculate minimum width needed for header text
     */
    calculateMinHeaderWidth(column) {
        // Special handling for checkbox column - just enough for checkbox + padding
        if (column.field === 'checkbox') {
            return 46; // 16px checkbox + 15px padding each side
        }

        // Special handling for preview column - fixed square size
        if (column.field === 'preview') {
            return 60; // 32px square + 14px padding each side
        }

        // Special handling for actions column - fixed width for action buttons
        if (column.field === 'actions') {
            return 96; // Fixed width for action buttons
        }

        // Create a temporary element to measure text width
        const tempElement = document.createElement('div');
        tempElement.style.position = 'absolute';
        tempElement.style.visibility = 'hidden';
        tempElement.style.whiteSpace = 'nowrap';
        tempElement.style.fontFamily = 'Amazon Ember, Arial, sans-serif';
        tempElement.style.fontSize = '14px';
        tempElement.style.fontWeight = '500'; // Medium weight for headers
        tempElement.style.padding = '0 12px'; // Account for cell padding

        // Set the header text
        const headerText = column.headerName || column.field || '';
        tempElement.textContent = headerText;

        // Add to DOM to measure
        document.body.appendChild(tempElement);
        const textWidth = tempElement.offsetWidth;
        document.body.removeChild(tempElement);

        // Add extra space for sort indicators, resize handles, gap, etc.
        const extraSpace = 48; // Space for sort icon + resize handle + 8px gap + margins
        const minWidth = textWidth + extraSpace;

        // Set reasonable bounds
        const absoluteMin = 80; // Never go below this
        const absoluteMax = 300; // Don't auto-expand beyond this

        return Math.max(absoluteMin, Math.min(minWidth, absoluteMax));
    }

    getColumnIndex(field) {
        return this.processedColumns.findIndex(c => c.field === field);
    }

    setColumnPinned(field, pinned) {
        const col = this.getColumn(field);
        if (!col) return false;
        // Actions column must remain pinned-right per UX spec
        if (field === 'actions') {
            col.pinned = 'right';
            this.sortColumnsByPinned();
            this.render();
            return true;
        }
        col.pinned = this.sanitizePinned(pinned);
        this.sortColumnsByPinned();
        this.render();
        return true;
    }

    setColumnWidth(field, width) {
        const col = this.getColumn(field);
        if (!col) return false;
        
        // Calculate minimum width based on header text
        const minWidth = this.calculateMinHeaderWidth(col);
        const w = Math.max(minWidth, Math.floor(Number(width) || 0));
        col.width = w;
        this.render();
        return true;
    }

    moveColumnByField(field, toIndex) {
        const fromIndex = this.getColumnIndex(field);
        if (fromIndex < 0) return false;
        // Prevent moving into pinned area implicitly; keep within same pinned group
        const source = this.processedColumns[fromIndex];
        // Build list of indices for same pinned group
        const group = this.processedColumns
            .map((c, i) => ({ c, i }))
            .filter(x => (x.c.pinned || null) === (source.pinned || null))
            .map(x => x.i);
        const min = Math.min(...group);
        const max = Math.max(...group);
        const clamped = Math.max(min, Math.min(max, toIndex));
        this.moveColumn(fromIndex, clamped);
        return true;
    }

    setColumnVisible(field, visible) {
        const col = this.getColumn(field);
        if (!col) return false;
        col.hide = visible === false;
        this.render();
        return true;
    }

    applyColumnState(params = {}) {
        const { state = [], defaultState = {} } = params;
        const byId = new Map(this.processedColumns.map(c => [c.field, c]));
        state.forEach(s => {
            const col = byId.get(s.colId || s.field || s.colKey);
            if (!col) return;
            if (s.pinned !== undefined) col.pinned = this.sanitizePinned(s.pinned);
            if (s.width != null) col.width = Math.max(20, Number(s.width) || col.width || 100);
            if (s.hide !== undefined) col.hide = !!s.hide;
        });
        // Apply defaults for any missing
        this.processedColumns.forEach(col => {
            if (!(state || []).some(s => (s.colId || s.field || s.colKey) === col.field)) {
                if (defaultState.pinned !== undefined) col.pinned = this.sanitizePinned(defaultState.pinned);
                if (defaultState.width != null) col.width = Math.max(20, Number(defaultState.width) || col.width || 100);
                if (defaultState.hide !== undefined) col.hide = !!defaultState.hide;
            }
        });
        // Enforce Actions pinned-right regardless of incoming state
        const actionsCol = byId.get('actions') || this.getColumn('actions');
        if (actionsCol) actionsCol.pinned = 'right';
        this.sortColumnsByPinned();
        this.render();
        return true;
    }

    getColumnState() {
        return this.processedColumns.map(c => ({
            colId: c.field,
            width: c.width,
            pinned: c.pinned || null,
            hide: !!c.hide
        }));
    }

    /**
     * Grid API methods
     */
    setRowData(data) {
        this.data = Array.isArray(data) ? [...data] : [];
        this.processData();
        this.render();
    }

    /**
     * Load data in chunks for large datasets
     * @param {number} totalRecords - Total number of records to load
     * @param {Function} dataGenerator - Function to generate data chunks
     * @param {Object} options - Loading options
     * @returns {Promise} Loading promise
     */
    async loadDataInChunks(totalRecords, dataGenerator, options = {}) {
        // Import ChunkedDataLoader if not already available
        if (typeof ChunkedDataLoader === 'undefined') {
            throw new Error('ChunkedDataLoader not available. Please include chunked-data-loader.js');
        }

        const loader = new ChunkedDataLoader({
            chunkSize: options.chunkSize || 10000,
            delayBetweenChunks: options.delayBetweenChunks || 16,
            maxMemoryChunks: options.maxMemoryChunks || 5,
            enableMemoryManagement: options.enableMemoryManagement || false // Disable by default for large datasets
        });

        // Set expected total for display
        this.expectedTotalRecords = totalRecords;

        // Show loading state
        this.showLoadingState(true);

        try {
            const data = await loader.loadData(totalRecords, dataGenerator, {
                onProgress: (progress, totalRecords, loadedRecords, currentChunk, totalChunks) => {
                    this.updateLoadingProgress(progress, totalRecords, loadedRecords, currentChunk, totalChunks);
                    options.onProgress?.(progress, totalRecords, loadedRecords, currentChunk, totalChunks);
                },
                onChunkLoaded: (chunkData, chunkIndex, allData) => {
                    // Update grid with current data
                    this.data = [...allData];
                    this.processData();
                    this.render();
                    options.onChunkLoaded?.(chunkData, chunkIndex, allData);
                },
                onComplete: (finalData) => {
                    this.hideLoadingState();
                    // Clear expected total since loading is complete
                    this.expectedTotalRecords = null;
                    options.onComplete?.(finalData);
                },
                onError: (error) => {
                    this.hideLoadingState();
                    // Clear expected total on error
                    this.expectedTotalRecords = null;
                    options.onError?.(error);
                },
                onCancel: (data) => {
                    this.hideLoadingState();
                    // Clear expected total on cancel
                    this.expectedTotalRecords = null;
                    options.onCancel?.(data);
                }
            });

            return data;
        } catch (error) {
            this.hideLoadingState();
            // Clear expected total on error
            this.expectedTotalRecords = null;
            throw error;
        }
    }

    /**
     * Show loading state with progress indicator
     * @private
     */
    showLoadingState(show = true) {
        if (!this.container) return;

        if (show) {
            // Create loading overlay if it doesn't exist
            if (!this.loadingOverlay) {
                this.loadingOverlay = document.createElement('div');
                this.loadingOverlay.className = 'snap-grid-loading-overlay';
                this.loadingOverlay.innerHTML = `
                    <div class="snap-grid-loading-content">
                        <div class="snap-grid-loading-spinner"></div>
                        <div class="snap-grid-loading-text">Loading data...</div>
                        <div class="snap-grid-loading-progress">
                            <div class="snap-grid-progress-bar">
                                <div class="snap-grid-progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="snap-grid-progress-text">0% (0 / 0 records)</div>
                        </div>
                        <button class="snap-grid-cancel-loading" onclick="this.closest('.snap-grid-loading-overlay').dataset.cancelled='true'">
                            Cancel Loading
                        </button>
                    </div>
                `;
            }

            this.container.style.position = 'relative';
            this.container.appendChild(this.loadingOverlay);
            this.loadingOverlay.style.display = 'flex';
        } else if (this.loadingOverlay) {
            this.loadingOverlay.style.display = 'none';
        }
    }

    /**
     * Hide loading state
     * @private
     */
    hideLoadingState() {
        if (this.loadingOverlay) {
            this.loadingOverlay.style.display = 'none';
        }
    }

    /**
     * Update loading progress
     * @private
     */
    updateLoadingProgress(progress, totalRecords, loadedRecords, currentChunk, totalChunks) {
        if (!this.loadingOverlay) return;

        const progressFill = this.loadingOverlay.querySelector('.snap-grid-progress-fill');
        const progressText = this.loadingOverlay.querySelector('.snap-grid-progress-text');
        const loadingText = this.loadingOverlay.querySelector('.snap-grid-loading-text');

        if (progressFill) {
            progressFill.style.width = `${progress}%`;
        }

        if (progressText) {
            progressText.textContent = `${progress.toFixed(1)}% (${loadedRecords.toLocaleString()} / ${totalRecords.toLocaleString()} records)`;
        }

        if (loadingText) {
            loadingText.textContent = `Loading data... (Chunk ${currentChunk}/${totalChunks})`;
        }
    }

    /**
     * Check if loading is cancelled
     * @private
     */
    isLoadingCancelled() {
        return this.loadingOverlay?.dataset.cancelled === 'true';
    }

    refreshCells(params = {}) {
        // Minimal: re-render rows (fast with virtualization)
        this.renderRows();
        return true;
    }

    redrawRows(params = {}) {
        // Minimal: re-render rows
        this.renderRows();
        return true;
    }

    ensureColumnVisible(field) {
        const index = this.getColumnIndex(field);
        if (index < 0 || !this.bodyElement) return false;
        const col = this.processedColumns[index];
        if (col.hide) return false;
        if (col.pinned) return true; // already visible

        // Compute cumulative x of the target column (excluding hidden columns)
        let x = 0;
        let leftPinnedWidth = 0;
        let rightPinnedWidth = 0;
        for (let i = 0; i < this.processedColumns.length; i++) {
            const c = this.processedColumns[i];
            const w = c.hide ? 0 : (c.width || 0);
            if (c.pinned === 'left') leftPinnedWidth += w;
            if (c.pinned === 'right') rightPinnedWidth += w;
        }
        for (let i = 0; i < this.processedColumns.length; i++) {
            const c = this.processedColumns[i];
            if (c.pinned) continue; // center scroll only accounts for non-pinned
            if (i === index) break;
            x += (c.hide ? 0 : (c.width || 0));
        }

        const viewportW = this.bodyElement.clientWidth;
        const current = this.bodyElement.scrollLeft || 0;
        const colWidth = col.width || 0;

        const viewLeft = current + leftPinnedWidth;
        const viewRight = current + viewportW - rightPinnedWidth;
        const colLeft = leftPinnedWidth + x;
        const colRight = colLeft + colWidth;

        if (colLeft < viewLeft) {
            // scroll so left edge aligns
            const newLeft = colLeft - leftPinnedWidth;
            this.bodyElement.scrollLeft = Math.max(0, newLeft);
        } else if (colRight > viewRight) {
            // scroll so right edge is visible
            const newLeft = (colRight - viewportW) + rightPinnedWidth;
            this.bodyElement.scrollLeft = Math.max(0, newLeft);
        }
        return true;
    }

    ensureIndexVisible(index) {
        if (!this.bodyElement) return false;
        const rowHeight = this.options.rowHeight;
        const viewportH = this.bodyElement.clientHeight;
        const scale = this._scrollScale || 1;
        const currentTop = this.bodyElement.scrollTop || 0;
        const virtualCurrentTop = currentTop * scale;
        const rowTopVirtual = index * rowHeight;
        const rowBottomVirtual = rowTopVirtual + rowHeight;
        if (rowTopVirtual < virtualCurrentTop) {
            this.bodyElement.scrollTop = Math.floor(rowTopVirtual / scale);
        } else if (rowBottomVirtual > virtualCurrentTop + viewportH * scale) {
            this.bodyElement.scrollTop = Math.floor((rowBottomVirtual - viewportH * scale) / scale);
        }
        return true;
    }

    sizeColumnsToFit() {
        if (!this.bodyElement) return false;
        const viewportW = this.bodyElement.clientWidth;
        // Calculate total pinned widths
        let leftPinned = 0, rightPinned = 0;
        const centerCols = [];
        this.processedColumns.forEach(c => {
            const w = c.hide ? 0 : (c.width || 0);
            if (c.pinned === 'left') leftPinned += w;
            else if (c.pinned === 'right') rightPinned += w;
            else centerCols.push(c);
        });
        const available = Math.max(0, viewportW - leftPinned - rightPinned - this.getScrollbarWidth());
        const current = centerCols.reduce((s, c) => s + (c.hide ? 0 : (c.width || 0)), 0);
        if (available <= 0 || current <= 0 || !centerCols.length) return false;
        const ratio = available / current;
        centerCols.forEach(c => { if (!c.hide) c.width = Math.max(20, Math.floor((c.width || 0) * ratio)); });
        this.render();
        return true;
    }

    /**
     * Measure vertical scrollbar width in body viewport
     */
    getScrollbarWidth() {
        if (!this.bodyElement) return 0;
        return Math.max(0, this.bodyElement.offsetWidth - this.bodyElement.clientWidth);
    }

    /**
     * Render cell content with custom renderers
     */
    renderCellContent(value, column, rowData, rowIndex) {
        // Custom cell renderer
        if (column.cellRenderer && typeof column.cellRenderer === 'function') {
            return column.cellRenderer(value, column, rowData, rowIndex);
        }

        // Type-based rendering
        switch (column.type) {
            case 'number':
                return this.formatNumber(value);
            case 'currency':
                return this.formatCurrency(value);
            case 'date':
                return this.formatDate(value);
            case 'boolean':
                return this.formatBoolean(value);
            case 'status':
                return this.renderStatusCell(value);
            default:
                return this.escapeHtml(String(value || ''));
        }
    }

    /**
     * Render status with colored dot + same-colored text
     */
    renderStatusCell(value) {
        const { className, label } = this.getStatusStyle(value);
        const safe = this.escapeHtml(String(label || ''));
        return `<span class="snap-grid-status ${className}"><span class="dot"></span><span class="text">${safe}</span></span>`;
    }

    /**
     * Map raw status value to a color class
     */
    getStatusStyle(value) {
        const raw = String(value || '').trim();
        const v = raw.toLowerCase();
        // Status mapping as confirmed by user
        const blue = ['processing', 'auto-uploaded', 'translating'];
        const amber = ['under review', 'timed out', 'locked'];
        const red = ['declined', 'rejected', 'removed'];
        const green = ['live'];
        const gray = ['draft'];

        let className = 'status-gray';
        if (blue.includes(v)) className = 'status-blue';
        else if (amber.includes(v)) className = 'status-amber';
        else if (red.includes(v)) className = 'status-red';
        else if (green.includes(v)) className = 'status-green';
        else if (gray.includes(v)) className = 'status-gray';

        return { className: className, label: raw };
    }

    /**
     * Format number values
     */
    formatNumber(value) {
        if (value == null || value === '') return '';
        const num = Number(value);
        return isNaN(num) ? String(value) : num.toLocaleString();
    }

    /**
     * Format currency values
     */
    formatCurrency(value) {
        if (value == null || value === '') return '';
        const num = Number(value);
        return isNaN(num) ? String(value) : new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(num);
    }

    /**
     * Format date values
     */
    formatDate(value) {
        if (!value) return '';
        const date = new Date(value);
        return isNaN(date.getTime()) ? String(value) : date.toLocaleDateString();
    }

    /**
     * Format boolean values
     */
    formatBoolean(value) {
        if (value == null) return '';
        return value ? '✓' : '✗';
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Handle checkbox cell clicks
     */
    handleCheckboxClick(event) {
        const checkboxCell = event.target.closest('.snap-grid-checkbox-cell');
        if (!checkboxCell) return;

        const rowIndex = parseInt(checkboxCell.dataset.rowIndex);
        this.toggleRowSelection(rowIndex);
        event.stopPropagation();
    }

    /**
     * Handle header checkbox clicks
     */
    handleHeaderCheckboxClick(event) {
        this.toggleAllRowsSelection();
        event.stopPropagation();
    }

    /**
     * Toggle row selection
     */
    toggleRowSelection(rowIndex) {
        if (this.selectedRows.has(rowIndex)) {
            this.selectedRows.delete(rowIndex);
        } else {
            this.selectedRows.add(rowIndex);
        }

        this.updateSelectionState();
        this.render();

        if (this.options.onSelectionChanged) {
            this.options.onSelectionChanged(this.getSelectedData());
        }
    }

    /**
     * Toggle all rows selection
     */
    toggleAllRowsSelection() {
        if (this.allRowsSelected) {
            this.selectedRows.clear();
        } else {
            this.selectedRows.clear();
            for (let i = 0; i < this.sortedIdx.length; i++) {
                this.selectedRows.add(i);
            }
        }

        this.updateSelectionState();
        this.render();

        if (this.options.onSelectionChanged) {
            this.options.onSelectionChanged(this.getSelectedData());
        }
    }

    /**
     * Update selection state
     */
    updateSelectionState() {
        const totalRows = this.sortedIdx.length;
        const selectedCount = this.selectedRows.size;

        this.allRowsSelected = selectedCount === totalRows && totalRows > 0;
        this.indeterminateSelection = selectedCount > 0 && selectedCount < totalRows;
    }

    /**
     * Handle header cell clicks for sorting and menu
     */
    handleHeaderClick(event) {
        const headerCell = event.target.closest('.snap-grid-header-cell');
        if (!headerCell) return;

        const field = headerCell.dataset.field;
        const column = this.processedColumns.find(col => col.field === field);

        if (!column) return;

        // Skip non-interactive columns
        if (['checkbox','preview','actions'].includes(column.field)) return;

        // Check if menu button was clicked
        if (event.target.closest('.snap-grid-column-menu-btn')) {
            this.showColumnMenu(headerCell, column);
            return;
        }

        // Handle sorting on header text click
        if (column.sortable === false) return;

        // Toggle sort direction
        const currentSort = this.sortState[field];
        if (currentSort === 'asc') {
            this.sortState[field] = 'desc';
        } else if (currentSort === 'desc') {
            delete this.sortState[field];
        } else {
            this.sortState[field] = 'asc';
        }

        // Clear other sorts (single column sort for now)
        Object.keys(this.sortState).forEach(key => {
            if (key !== field) {
                delete this.sortState[key];
            }
        });

        this.processData();
        this.render();

        // Callback
        if (this.options.onSort) {
            this.options.onSort(field, this.sortState[field]);
        }
    }

    /**
     * Handle row clicks for selection
     */
    handleRowClick(event) {
        const row = event.target.closest('.snap-grid-row');
        if (!row) return;

        const rowIndex = parseInt(row.dataset.index);

        if (this.options.selectable) {
            // Toggle selection via the central method so checkbox UI updates
            this.toggleRowSelection(rowIndex);
        }

        // Callback
        if (this.options.onRowClick) {
            const rowData = this.data[this.sortedIdx[rowIndex]];
            this.options.onRowClick(rowData, rowIndex, event);
        }
    }

    /**
     * Handle cell clicks for editing
     */
    handleCellClick(event) {
        const cell = event.target.closest('.snap-grid-cell');
        if (!cell || !cell.classList.contains('editable')) return;

        const field = cell.dataset.field;
        const rowIndex = parseInt(cell.dataset.row);
        const column = this.options.columns.find(col => col.field === field);

        if (!column || column.editable === false) return;

        this.startCellEdit(cell, field, rowIndex);
    }

    /**
     * Start editing a cell
     */
    startCellEdit(cell, field, rowIndex) {
        const currentValue = this.data[this.sortedIdx[rowIndex]][field];

        // Create input element
        const input = document.createElement('input');
        input.type = 'text';
        input.value = currentValue || '';
        input.className = 'snap-grid-cell-editor';

        // Replace cell content
        cell.innerHTML = '';
        cell.appendChild(input);
        cell.classList.add('editing');

        // Focus and select
        input.focus();
        input.select();

        // Save on blur or enter
        const saveEdit = () => {
            const newValue = input.value;
            this.finishCellEdit(cell, field, rowIndex, newValue);
        };

        // Cancel on escape
        const cancelEdit = () => {
            this.cancelCellEdit(cell, field, rowIndex);
        };

        input.addEventListener('blur', saveEdit);
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveEdit();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });
    }

    /**
     * Finish cell editing
     */
    finishCellEdit(cell, field, rowIndex, newValue) {
        const oldValue = this.data[this.sortedIdx[rowIndex]][field];

        // Update data
        this.data[this.sortedIdx[rowIndex]][field] = newValue;

        // Update original data array
        const originalIndex = this.sortedIdx[rowIndex];
        if (originalIndex !== -1) {
            this.data[originalIndex][field] = newValue;
        }

        // Re-render cell
        const column = this.options.columns.find(col => col.field === field);
        const cellContent = this.renderCellContent(newValue, column, this.data[this.sortedIdx[rowIndex]], rowIndex);

        cell.innerHTML = '';
        if (typeof cellContent === 'string') {
            cell.innerHTML = cellContent;
        } else {
            cell.appendChild(cellContent);
        }

        cell.classList.remove('editing');

        // Callback
        if (this.options.onCellEdit) {
            this.options.onCellEdit(field, newValue, oldValue, this.data[this.sortedIdx[rowIndex]], rowIndex);
        }
    }

    /**
     * Cancel cell editing
     */
    cancelCellEdit(cell, field, rowIndex) {
        const column = this.options.columns.find(col => col.field === field);
        const currentValue = this.data[this.sortedIdx[rowIndex]][field];
        const cellContent = this.renderCellContent(currentValue, column, this.data[this.sortedIdx[rowIndex]], rowIndex);

        cell.innerHTML = '';
        if (typeof cellContent === 'string') {
            cell.innerHTML = cellContent;
        } else {
            cell.appendChild(cellContent);
        }

        cell.classList.remove('editing');
    }

    /**
     * Handle arrow key navigation
     */
    handleArrowKeys(event) {
        const focusedCell = document.activeElement.closest('.snap-grid-cell');
        if (!focusedCell) return;

        const row = focusedCell.closest('.snap-grid-row');
        const rowIndex = parseInt(row.dataset.index);
        const columnIndex = parseInt(focusedCell.dataset.column);

        let newRowIndex = rowIndex;
        let newColumnIndex = columnIndex;

        switch (event.key) {
            case 'ArrowUp':
                newRowIndex = Math.max(0, rowIndex - 1);
                break;
            case 'ArrowDown':
                newRowIndex = Math.min(this.sortedIdx.length - 1, rowIndex + 1);
                break;
            case 'ArrowLeft':
                newColumnIndex = Math.max(0, columnIndex - 1);
                break;
            case 'ArrowRight':
                newColumnIndex = Math.min(this.options.columns.length - 1, columnIndex + 1);
                break;
        }

        // Find and focus the new cell
        const newCell = document.querySelector(
            `.snap-grid-cell[data-row="${newRowIndex}"][data-column="${newColumnIndex}"]`
        );

        if (newCell) {
            newCell.focus();
            newCell.scrollIntoView({ block: 'nearest', inline: 'nearest' });
        }

        event.preventDefault();
    }

    /**
     * Handle enter key
     */
    handleEnterKey(event) {
        const focusedCell = document.activeElement.closest('.snap-grid-cell');
        if (focusedCell && focusedCell.classList.contains('editable')) {
            const field = focusedCell.dataset.field;
            const rowIndex = parseInt(focusedCell.dataset.row);
            this.startCellEdit(focusedCell, field, rowIndex);
            event.preventDefault();
        }
    }

    /**
     * Handle escape key
     */
    handleEscapeKey(event) {
        const editingCell = document.querySelector('.snap-grid-cell.editing');
        if (editingCell) {
            const field = editingCell.dataset.field;
            const rowIndex = parseInt(editingCell.dataset.row);
            this.cancelCellEdit(editingCell, field, rowIndex);
            event.preventDefault();
        }
    }

    /**
     * Set up column resizing
     */
    setupColumnResize() {
        let isResizing = false;
        let currentColumn = null;
        let startX = 0;
        let startWidth = 0;
        let currentField = null;
        this._resizeRaf = null;

        this.container.addEventListener('mousedown', (e) => {
            const resizeHandle = e.target.closest('.snap-grid-resize-handle');
            if (!resizeHandle) return;

            isResizing = true;
            currentColumn = resizeHandle.closest('.snap-grid-header-cell');
            currentField = currentColumn?.dataset?.field || null;
            startX = e.clientX;
            startWidth = currentColumn.offsetWidth;

            document.body.style.cursor = 'col-resize';
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isResizing || !currentColumn) return;

            const diff = e.clientX - startX;
            
            // Calculate minimum width based on header text
            const field = currentField;
            const column = this.getColumn(field);
            const minWidth = column ? this.calculateMinHeaderWidth(column) : 80;
            const newWidth = Math.max(minWidth, startWidth + diff);

            // Update column width on the live DOM and state
            if (column) {
                column.width = newWidth;
                this.updateColumnWidthDOM(field, newWidth);
                // Throttle expensive metrics updates to animation frames
                if (!this._resizeRaf) {
                    this._resizeRaf = requestAnimationFrame(() => {
                        this._resizeRaf = null;
                        this.updateHorizontalMetrics();
                        this.syncHorizontalScroll();
                    });
                }
            }
        });

        document.addEventListener('mouseup', () => {
            if (isResizing) {
                isResizing = false;
                const savedLeft = this.scrollLeft || 0;
                currentColumn = null;
                currentField = null;
                document.body.style.cursor = '';
                // Re-render to recompute pinned offsets and ensure full consistency
                this.render();
                // Restore horizontal scroll position to body
                if (this.bodyElement && this.bodyElement.scrollLeft !== savedLeft) {
                    this.bodyElement.scrollLeft = savedLeft;
                }
                this.syncHorizontalScroll();
            }
        });
    }

    /**
     * Update header and visible body cells for a given column field to a new width.
     * Avoids full re-render during drag for smooth UX.
     */
    updateColumnWidthDOM(field, width) {
        // Get the column to calculate minimum width
        const column = this.getColumn(field);
        const minWidth = column ? this.calculateMinHeaderWidth(column) : 80;
        const w = Math.max(minWidth, Math.floor(Number(width) || 0));
        
        // Header cell
        const headerCell = this.headerElement?.querySelector(`.snap-grid-header-cell[data-field="${field}"]`);
        if (headerCell) {
            headerCell.style.width = `${w}px`;
            headerCell.style.minWidth = `${w}px`;
        }
        // Body cells (only visible rows)
        if (this.viewportElement) {
            const cells = this.viewportElement.querySelectorAll(`.snap-grid-cell[data-field="${field}"]`);
            cells.forEach(cell => {
                cell.style.width = `${w}px`;
                cell.style.minWidth = `${w}px`;
            });
        }
        // No extra pinned offset recalculation needed; full render handles consistency
    }

    /**
     * Show column menu
     */
    showColumnMenu(headerCell, column) {
        // Remove existing menus
        document.querySelectorAll('.snap-grid-column-menu').forEach(menu => menu.remove());

        const menu = document.createElement('div');
        menu.className = 'snap-grid-column-menu';

        // Sort options
        if (this.options.sortable && column.sortable !== false) {
            const sortAsc = document.createElement('div');
            sortAsc.className = 'snap-grid-menu-item';
            sortAsc.innerHTML = '<span class="snap-grid-menu-item-icon"><img src="assets/ascending-ic.svg" alt="Ascending" class="menu-icon-img"></span>Sort Ascending';
            sortAsc.addEventListener('click', () => {
                this.setSort(column.field, 'asc');
                menu.remove();
            });
            menu.appendChild(sortAsc);

            const sortDesc = document.createElement('div');
            sortDesc.className = 'snap-grid-menu-item';
            sortDesc.innerHTML = '<span class="snap-grid-menu-item-icon"><img src="assets/descending-ic.svg" alt="Descending" class="menu-icon-img"></span>Sort Descending';
            sortDesc.addEventListener('click', () => {
                this.setSort(column.field, 'desc');
                menu.remove();
            });
            menu.appendChild(sortDesc);

            const clearSort = document.createElement('div');
            clearSort.className = 'snap-grid-menu-item';
            clearSort.innerHTML = '<span class="snap-grid-menu-item-icon">✕</span>Clear Sort';
            clearSort.addEventListener('click', () => {
                this.setSort(column.field, null);
                menu.remove();
            });
            menu.appendChild(clearSort);

            const divider = document.createElement('div');
            divider.className = 'snap-grid-menu-divider';
            menu.appendChild(divider);
        }

        // Filter options
        if (this.options.filterable && column.filterable !== false) {
            const filterContainer = document.createElement('div');
            filterContainer.style.padding = '8px 16px';

            const filterInput = document.createElement('input');
            filterInput.type = 'text';
            filterInput.className = 'snap-grid-filter-input';
            filterInput.placeholder = `Filter ${column.headerName || column.field}...`;
            filterInput.value = this.filterState[column.field]?.value || '';

            filterInput.addEventListener('keyup', (e) => {
                if (e.key === 'Enter') {
                    this.setFilter(column.field, filterInput.value);
                    menu.remove();
                }
            });

            filterContainer.appendChild(filterInput);
            menu.appendChild(filterContainer);

            const clearFilter = document.createElement('div');
            clearFilter.className = 'snap-grid-menu-item';
            clearFilter.innerHTML = '<span class="snap-grid-menu-item-icon">✕</span>Clear Filter';
            clearFilter.addEventListener('click', () => {
                this.setFilter(column.field, '');
                menu.remove();
            });
            menu.appendChild(clearFilter);
        }

        // Position and show menu
        headerCell.style.position = 'relative';
        headerCell.appendChild(menu);

        // Close menu when clicking outside
        setTimeout(() => {
            document.addEventListener('click', function closeMenu(e) {
                if (!menu.contains(e.target)) {
                    menu.remove();
                    document.removeEventListener('click', closeMenu);
                }
            });
        }, 0);
    }

    /**
     * Create ARIA live region for screen reader announcements
     */
    createAriaLiveRegion() {
        this.ariaLiveRegion = document.createElement('div');
        this.ariaLiveRegion.className = 'snap-grid-sr-only';
        this.ariaLiveRegion.setAttribute('aria-live', 'polite');
        this.ariaLiveRegion.setAttribute('aria-atomic', 'true');
        this.container.appendChild(this.ariaLiveRegion);
    }

    /**
     * Announce message to screen readers
     */
    announceToScreenReader(message) {
        if (this.ariaLiveRegion) {
            this.ariaLiveRegion.textContent = message;

            // Clear after announcement
            setTimeout(() => {
                this.ariaLiveRegion.textContent = '';
            }, 1000);
        }
    }

    /**
     * Add accessibility attributes to header cells
     */
    addHeaderAccessibility(headerCell, column, columnIndex) {
        headerCell.setAttribute('role', 'columnheader');
        headerCell.setAttribute('tabindex', '0');
        headerCell.setAttribute('aria-sort', 'none');

        if (this.sortState[column.field]) {
            const direction = this.sortState[column.field] === 'asc' ? 'ascending' : 'descending';
            headerCell.setAttribute('aria-sort', direction);
        }

        // Add column index for screen readers
        headerCell.setAttribute('aria-colindex', columnIndex + 1);

        // Add keyboard instructions
        if (this.options.sortable && column.sortable !== false) {
            headerCell.setAttribute('aria-label',
                `${column.headerName || column.field}, column ${columnIndex + 1}. Click to sort.`);
        }
    }

    /**
     * Add accessibility attributes to data cells
     */
    addCellAccessibility(cell, column, rowData, rowIndex, columnIndex) {
        cell.setAttribute('role', 'gridcell');
        cell.setAttribute('tabindex', '-1');
        cell.setAttribute('aria-colindex', columnIndex + 1);
        cell.setAttribute('aria-rowindex', rowIndex + 2); // +2 because header is row 1

        // Add cell description for screen readers
        const value = rowData[column.field];
        const description = `${column.headerName || column.field}: ${this.formatValueForScreenReader(value, column)}`;
        cell.setAttribute('aria-label', description);

        // Mark editable cells
        if (this.options.editable && column.editable !== false) {
            cell.setAttribute('aria-describedby', 'grid-edit-instructions');
        }
    }

    /**
     * Format value for screen reader announcement
     */
    formatValueForScreenReader(value, column) {
        if (value == null || value === '') return 'empty';

        switch (column.type) {
            case 'currency':
                return `${value} dollars`;
            case 'date':
                const date = new Date(value);
                return isNaN(date.getTime()) ? String(value) : date.toLocaleDateString();
            case 'boolean':
                return value ? 'yes' : 'no';
            default:
                return String(value);
        }
    }

    // ========================================================================
    // PUBLIC API METHODS
    // ========================================================================

    /**
     * Update grid data
     */
    updateData(newData) {
        this.data = [...newData];
        this.processData();
        this.render();

        // Announce data update to screen readers
        this.announceToScreenReader(`Grid updated with ${newData.length} rows`);
    }

    /**
     * Get all data
     */
    getData() {
        return [...this.data];
    }

    /**
     * Get filtered and sorted data
     */
    getDisplayedData() {
        return this.sortedIdx.map(i => this.data[i]);
    }

    /**
     * Get selected row data
     */
    getSelectedData() {
        return Array.from(this.selectedRows).map(index => this.data[this.sortedIdx[index]]);
    }

    /**
     * Clear all selections
     */
    clearSelection() {
        this.selectedRows.clear();
        document.querySelectorAll('.snap-grid-row.selected').forEach(row => {
            row.classList.remove('selected');
        });
    }

    /**
     * Set column definitions
     */
    setColumns(columns) {
        this.options.columns = columns;
        this.render();
    }

    /**
     * Get column definitions
     */
    getColumns() {
        return [...this.options.columns];
    }

    // REMOVED: Duplicate setFilter function - consolidated into single implementation at line 4445

    // REMOVED: setFilterDebounced - redundant with main setFilter function
    // Use setFilter() directly - it already handles immediate application efficiently

    /**
     * Clear all filters - comprehensive state cleanup
     */
    clearFilters() {
        // Clear all filter-related state objects
        this.filterState = {};
        this.checkboxState = {};

        // Clear temporary state if it exists
        if (this.tempCheckboxState) {
            this.tempCheckboxState = {};
        }

        // Apply changes immediately
        this.processData();
        this.render();

        // Update any open filter menus to reflect cleared state
        this._updateOpenFilterMenusAfterClear();
    }

    /**
     * Update open filter menus after clearing filters (internal helper)
     */
    _updateOpenFilterMenusAfterClear() {
        // Find all open column menus and reset their checkbox states
        const openMenus = document.querySelectorAll('.snap-grid-column-menu');
        openMenus.forEach(menu => {
            const checkboxLists = menu.querySelectorAll('.filter-checkbox-list');
            checkboxLists.forEach(list => {
                // Reset all checkboxes to checked state (default state)
                const checkboxIcons = list.querySelectorAll('.checkbox-icon');
                checkboxIcons.forEach(icon => {
                    icon.src = './assets/checkbox-ic.svg';
                    icon.alt = 'Checked';
                });

                // Reset any filter inputs in the menu
                const filterInputs = menu.querySelectorAll('.filter-input-with-icon, input[type="text"], input[type="date"]');
                filterInputs.forEach(input => {
                    input.value = '';
                });

                // Reset dropdowns to default state
                const dropdowns = menu.querySelectorAll('.custom-dropdown');
                dropdowns.forEach(dropdown => {
                    const firstOption = dropdown.querySelector('.dropdown-option');
                    if (firstOption) {
                        const defaultValue = firstOption.getAttribute('data-value') || 'pleaseSelect';
                        this.setCustomDropdownValue(dropdown, defaultValue);
                    }
                });
            });
        });
    }

    /**
     * Set sort for a column
     */
    setSort(field, direction) {
        if (direction && ['asc', 'desc'].includes(direction)) {
            this.sortState[field] = direction;
        } else {
            delete this.sortState[field];
        }

        this.processData();
        this.render();

        if (this.options.onSort) {
            this.options.onSort(field, direction);
        }
    }

    /**
     * Clear all sorting
     */
    clearSort() {
        this.sortState = {};
        this.processData();
        this.render();
    }

    /**
     * Resize the grid (call after container size changes)
     */
    resize() {
        this.calculateVisibleRange();
        this.render();
    }

    /**
     * Refresh the grid
     */
    refresh() {
        this.processData();
        this.render();
    }

    /**
     * Destroy the grid and clean up
     */
    destroy() {
        // Remove event listeners
        window.removeEventListener('resize', this.handleResize.bind(this));

        // Clear container
        if (this.container) {
            this.container.innerHTML = '';
            this.container.className = '';
        }

        // Clear references
        this.container = null;
        this.gridElement = null;
        this.headerElement = null;
        this.bodyElement = null;
        this.viewportElement = null;
        this.scrollContainer = null;

        console.log('🗑️ SnapGrid destroyed');
    }

    /**
     * Export data to CSV
     */
    exportToCsv(filename = 'grid-data.csv') {
        const headers = this.options.columns.map(col => col.headerName || col.field);
        const rows = this.sortedIdx.map(i => this.options.columns.map(col => (this.data[i][col.field] || '')));

        const csvContent = [headers, ...rows]
            .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
            .join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
        URL.revokeObjectURL(link.href);
    }

    /**
     * Get grid statistics
     */
    getStats() {
        return {
            totalRows: this.data.length,
            filteredRows: this.filteredIdx.length,
            displayedRows: this.sortedIdx.length,
            selectedRows: this.selectedRows.size,
            lastRenderDuration: this.lastRenderDuration,
            visibleRange: {
                start: this.visibleStartIndex,
                end: this.visibleEndIndex
            }
        };
    }

    // ==========================================================================
    // Column Menu Functionality
    // ==========================================================================

    /**
     * Show column menu with tabbed interface
     */
    showColumnMenu(headerCell, column) {
        // Add error checking
        if (!headerCell) {
            console.error('showColumnMenu: headerCell parameter is undefined');
            return;
        }
        if (!column) {
            console.error('showColumnMenu: column parameter is undefined');
            return;
        }

        // Remove existing menu
        this.hideColumnMenu();

        const menu = document.createElement('div');
        menu.className = 'snap-grid-column-menu tabbed-menu';
        menu.setAttribute('role', 'menu');
        menu.setAttribute('aria-label', `Column menu for ${column.headerName || column.field}`);
        menu.setAttribute('data-field', column.field);

        // Create tab header
        const tabHeader = document.createElement('div');
        tabHeader.className = 'menu-tab-header';

        // Tab buttons with icons
        const filterTab = this.createTabButton('filter', 'filters', 'Filter');
        const managementTab = this.createTabButton('management', 'column-man', 'Column Management');
        const visibilityTab = this.createTabButton('visibility', 'show-hide-col', 'Show/Hide Columns');

        tabHeader.appendChild(filterTab);
        tabHeader.appendChild(managementTab);
        tabHeader.appendChild(visibilityTab);

        // Create tab content container
        const tabContent = document.createElement('div');
        tabContent.className = 'menu-tab-content';

        // Create tab panels
        const filterPanel = this.createFilterTab(column);
        const managementPanel = this.createManagementTab(column);
        const visibilityPanel = this.createVisibilityTab();

        tabContent.appendChild(filterPanel);
        tabContent.appendChild(managementPanel);
        tabContent.appendChild(visibilityPanel);

        menu.appendChild(tabHeader);
        menu.appendChild(tabContent);

        // Prevent menu from closing when clicking inside it
        menu.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Track active menu for scroll repositioning
        const targetElement = headerCell;

        this.activeMenu = menu;
        this.activeMenuTarget = targetElement;

        // Append to grid container for proper relative positioning
        this.container.appendChild(menu);
        this.currentColumnMenu = menu;
        this.activeDialogs = this.activeDialogs || new Set();
        this.activeDialogs.add(menu);

        // Position menu after it's added to DOM for accurate positioning
        try {
            this.positionMenuWithCollisionDetection(menu, targetElement);
        } catch (error) {
            console.error('Error positioning menu:', error);
            // Fallback positioning
            menu.style.position = 'fixed';
            menu.style.top = '100px';
            menu.style.left = '100px';
            menu.style.zIndex = '1001';
        }

        // Ensure menu is properly positioned after being added to DOM
        // This helps with accurate positioning calculations
        requestAnimationFrame(() => {
            if (this.activeMenu === menu && this.activeMenuTarget) {
                this.positionMenuWithCollisionDetection(menu, this.activeMenuTarget);
            }
        });

        // Set up tab switching
        this.setupTabSwitching(menu);

        // Set default active tab
        this.setActiveTab(menu, 'filter');

        // Add menu-active class to header cell
        headerCell.classList.add('menu-active');

        // Close menu when clicking outside (with small delay to prevent immediate closure)
        setTimeout(() => {
            const handleOutsideClick = (e) => {
                // Don't close if clicking on the menu itself or the menu button
                if (!menu.contains(e.target) && !headerCell.contains(e.target)) {
                    this.hideColumnMenu();
                    document.removeEventListener('click', handleOutsideClick);
                    document.removeEventListener('keydown', handleEscapeKey);
                }
            };

            const handleEscapeKey = (e) => {
                if (e.key === 'Escape') {
                    this.hideColumnMenu();
                    document.removeEventListener('click', handleOutsideClick);
                    document.removeEventListener('keydown', handleEscapeKey);
                }
            };

            document.addEventListener('click', handleOutsideClick);
            document.addEventListener('keydown', handleEscapeKey);
        }, 100);
    }

    /**
     * Hide column menu - exact copy from old grid
     */
    hideColumnMenu() {
        if (this.currentColumnMenu) {
            if (this.activeDialogs) {
                this.activeDialogs.delete(this.currentColumnMenu);
            }
            this.currentColumnMenu.remove();
            this.currentColumnMenu = null;
        }

        // Clear active menu tracking
        this.activeMenu = null;
        this.activeMenuTarget = null;

        // Remove menu-active class from all header cells
        const activeHeaders = this.container.querySelectorAll('.snap-grid-header-cell.menu-active');
        activeHeaders.forEach(header => header.classList.remove('menu-active'));
    }

    /**
     * Position menu with collision detection during scrolling - exact copy from old grid
     */
    positionMenuWithCollisionDetection(menu, targetElement) {
        // Ensure menu has proper positioning styles
        menu.style.position = 'absolute';
        menu.style.zIndex = '1001';

        // Check if target element is valid and visible
        if (!targetElement || !targetElement.getBoundingClientRect) {
            console.warn('⚠️ Invalid target element for menu positioning, falling back to left side');
            this.positionMenuOnLeftSide();
            return;
        }

        const rect = targetElement.getBoundingClientRect();
        
        // Check if target element is not visible (hidden column)
        if (rect.width === 0 && rect.height === 0) {
            console.warn('⚠️ Target element is not visible (hidden column), falling back to left side');
            this.positionMenuOnLeftSide();
            return;
        }
        const containerRect = this.container.getBoundingClientRect();
        const menuRect = menu.getBoundingClientRect();
        const menuWidth = menuRect.width || 281; // Fallback to CSS width if not rendered yet
        const menuHeight = menuRect.height || 400; // Fallback to CSS height if not rendered yet
        const padding = 8;

        // Get pinned column boundaries in viewport coordinates
        const pinnedLeftWidth = this.calculateTotalPinnedWidth ? this.calculateTotalPinnedWidth('left') : 0;
        const pinnedRightWidth = this.calculateTotalPinnedWidth ? this.calculateTotalPinnedWidth('right') : 0;
        const containerWidth = this.container.offsetWidth;

        // Calculate pinned boundaries in viewport coordinates
        const leftPinnedEnd = containerRect.left + pinnedLeftWidth;
        const rightPinnedStart = containerRect.right - pinnedRightWidth;

        // Calculate scroll-adjusted positions
        const scrollLeft = this.container.scrollLeft || 0;
        const scrollTop = this.container.scrollTop || 0;

        const targetRelativeToContainer = {
            left: rect.left - containerRect.left + scrollLeft,
            right: rect.right - containerRect.left + scrollLeft,
            top: rect.top - containerRect.top + scrollTop,
            bottom: rect.bottom - containerRect.top + scrollTop,
            width: rect.width
        };

        // Determine original menu position preference
        const containerCenter = containerWidth / 2;
        const columnCenter = targetRelativeToContainer.left + (targetRelativeToContainer.width / 2);
        const isLeftSide = columnCenter < containerCenter;

        let left;

        if (isLeftSide) {
            // Prefer right side of column
            left = targetRelativeToContainer.right;

            // Check collision with right pinned columns using viewport coordinates
            const menuRightEdge = containerRect.left + left + menuWidth - scrollLeft;
            if (menuRightEdge > rightPinnedStart && pinnedRightWidth > 0) {
                // Stop at right pinned boundary (convert back to container coordinates)
                left = rightPinnedStart - containerRect.left + scrollLeft - menuWidth;
            }
        } else {
            // Prefer left side of column (account for 1.5px border)
            left = targetRelativeToContainer.left - menuWidth + 1.5;

            // Check collision with left pinned columns using viewport coordinates
            const menuLeftEdge = containerRect.left + left - scrollLeft;
            if (menuLeftEdge < leftPinnedEnd && pinnedLeftWidth > 0) {
                // Stop at left pinned boundary (convert back to container coordinates)
                left = leftPinnedEnd - containerRect.left + scrollLeft;
            }
        }

        // Ensure menu stays within container bounds
        if (left < 0) left = 0;
        if (left + menuWidth > containerWidth) left = containerWidth - menuWidth;

        // Vertical positioning
        let top = targetRelativeToContainer.bottom + 2;

        if (top + menuHeight > this.container.offsetHeight - padding) {
            top = targetRelativeToContainer.top - menuHeight - 2;
            if (top < padding) {
                top = padding;
            }
        }

        // Apply positioning
        menu.style.left = `${left}px`;
        menu.style.top = `${top}px`;
    }

    /**
     * Position menu on the left side when target column is not found
     */
    positionMenuOnLeftSide() {
        if (!this.activeMenu) {
            return;
        }

        // Ensure menu has proper positioning styles
        this.activeMenu.style.position = 'absolute';
        this.activeMenu.style.zIndex = '1001';

        const containerRect = this.container.getBoundingClientRect();
        const menuRect = this.activeMenu.getBoundingClientRect();
        const menuWidth = menuRect.width || 281; // Fallback to CSS width if not rendered yet
        const menuHeight = menuRect.height || 400; // Fallback to CSS height if not rendered yet
        const padding = 8;

        // Get pinned column boundaries
        const pinnedLeftWidth = this.calculateTotalPinnedWidth ? this.calculateTotalPinnedWidth('left') : 0;
        const containerWidth = this.container.offsetWidth;

        // Calculate scroll-adjusted positions
        const scrollLeft = this.container.scrollLeft || 0;
        const scrollTop = this.container.scrollTop || 0;

        // Position on the left side, respecting pinned columns
        const leftPinnedEnd = containerRect.left + pinnedLeftWidth;
        let left = Math.max(0, leftPinnedEnd - containerRect.left + scrollLeft);

        // Ensure menu stays within container bounds
        if (left + menuWidth > containerWidth) {
            left = containerWidth - menuWidth;
        }

        // Position vertically in the middle of the visible area
        const top = Math.max(padding, (this.container.offsetHeight - menuHeight) / 2);

        // Apply positioning
        this.activeMenu.style.left = `${left}px`;
        this.activeMenu.style.top = `${top}px`;

        console.log('📍 Positioned menu on left side due to missing target column');
    }

    /**
     * Create tab button for menu
     */
    createTabButton(tabId, iconName, label) {
        const button = document.createElement('button');
        button.className = 'menu-tab-btn';
        button.setAttribute('data-tab', tabId);
        button.setAttribute('aria-label', label);

        // Create icon element
        const icon = document.createElement('img');
        icon.className = 'tab-icon';
        icon.src = `assets/${iconName}-inactive-ic.svg`;
        icon.alt = label;
        icon.draggable = false;

        button.appendChild(icon);
        return button;
    }

    /**
     * Create filter tab content - exact copy from old grid
     */
    createFilterTab(column) {
        const panel = document.createElement('div');
        panel.className = 'menu-tab-panel';
        panel.setAttribute('data-tab-panel', 'filter');

        // Get existing filter state for restoration
        const existingFilter = this.filterState[column.field];

        // Get column type using the new type system
        const columnType = this.getColumnType(column.field);

        // Declare applyFilter function early so it can be used by createLogicToggle
        let applyFilter;

        // Determine UI layout based on column type
        const isTextSpecial = columnType === 'text-special';
        const isDateOnly = columnType === 'date';
        const isNumericOnly = columnType === 'numeric';
        const isFixed = columnType === 'fixed';

        // First dropdown (full width) - hidden for checkbox-only columns, shown for date-only columns
        const filterOptions = this.getFilterTypeOptions(column);

        // Get the first option value correctly from the options array
        const defaultOperator = filterOptions.length > 0 ? filterOptions[0].value : 'pleaseSelect';

        const firstDropdown = this.createCustomDropdown(
            filterOptions,
            defaultOperator
        );
        firstDropdown.classList.add('filter-type-dropdown');
        if (isTextSpecial || isFixed) {
            firstDropdown.style.display = 'none';
        }

        // Restore first dropdown state if it exists
        if (existingFilter && existingFilter.operator) {
            this.setCustomDropdownValue(firstDropdown, existingFilter.operator);
        }

        // First filter input (full width with filter icon) - hidden for text-special, date-only, and fixed columns
        const firstFilterWrapper = document.createElement('div');
        firstFilterWrapper.className = 'filter-input-wrapper';
        if (isTextSpecial || isDateOnly || isFixed) {
            firstFilterWrapper.style.display = 'none';
        }

        const firstFilterIcon = document.createElement('img');
        firstFilterIcon.src = 'assets/filters-active-ic.svg';
        firstFilterIcon.className = 'filter-icon';
        firstFilterIcon.alt = 'Filter';

        const firstInput = document.createElement('input');
        firstInput.type = 'text';
        firstInput.className = 'filter-input-with-icon';
        firstInput.placeholder = 'Filter..';

        firstFilterWrapper.appendChild(firstFilterIcon);
        firstFilterWrapper.appendChild(firstInput);

        // Restore first filter state if it exists
        if (existingFilter && existingFilter.value) {
            firstInput.value = existingFilter.value;
        }

        // AND/OR Logic toggles (initially hidden, always hidden for checkbox-only columns)
        const logicSection = document.createElement('div');
        logicSection.className = 'filter-logic-section';
        logicSection.style.display = 'none';

        // Second dropdown (initially hidden, always hidden for checkbox-only columns)
        const secondDropdown = this.createCustomDropdown(
            filterOptions,
            defaultOperator
        );
        secondDropdown.classList.add('filter-type-dropdown');
        secondDropdown.style.display = 'none';

        // Restore second dropdown state if it exists
        if (existingFilter && existingFilter.secondOperator) {
            this.setCustomDropdownValue(secondDropdown, existingFilter.secondOperator);
        }

        // Second filter input (initially hidden, always hidden for checkbox-only columns)
        const secondFilterWrapper = document.createElement('div');
        secondFilterWrapper.className = 'filter-input-wrapper';
        secondFilterWrapper.style.display = 'none';

        const secondFilterIcon = document.createElement('img');
        secondFilterIcon.src = 'assets/filters-active-ic.svg';
        secondFilterIcon.className = 'filter-icon';
        secondFilterIcon.alt = 'Filter';

        const secondInput = document.createElement('input');
        secondInput.type = 'text';
        secondInput.className = 'filter-input-with-icon';
        secondInput.placeholder = 'Filter..';

        secondFilterWrapper.appendChild(secondFilterIcon);
        secondFilterWrapper.appendChild(secondInput);

        // Restore second filter state if it exists
        if (existingFilter && existingFilter.secondValue) {
            secondInput.value = existingFilter.secondValue;
        }

        // Show second filter and logic section if there's existing data
        if (existingFilter && (existingFilter.secondValue || existingFilter.secondOperator)) {
            logicSection.style.display = 'flex';
            secondDropdown.style.display = 'block';
            secondFilterWrapper.style.display = 'flex';
        }

        // Define applyFilter function here so it can be used by createLogicToggle
        applyFilter = () => {
            const firstOperator = this.getCustomDropdownValue(firstDropdown);

            if (columnType === 'date') {
                // For date-only columns, handle dropdown selection
                if (firstOperator === 'inRange') {
                    // Date range: use From/To date inputs
                    const fromDate = firstInput.value;
                    const toDate = secondInput.value;

                    if (fromDate || toDate) {
                        this.setFilter(column.field, {
                            type: this.getFilterType(column),
                            operator: 'inRange',
                            value: { fromValue: fromDate, toValue: toDate }
                        }, 'contains');
                    } else {
                        this.clearFilter(column.field);
                    }
                } else if (['equals', 'notEquals', 'lessThan', 'greaterThan'].includes(firstOperator)) {
                    // Date comparison operators: use single date input
                    const dateValue = firstInput.value;

                    if (dateValue) {
                        this.setFilter(column.field, {
                            type: this.getFilterType(column),
                            operator: firstOperator,
                            value: dateValue
                        }, 'contains');
                    } else {
                        this.clearFilter(column.field);
                    }
                } else if (firstOperator && firstOperator !== 'pleaseSelect') {
                    // Predefined date range (Today, Yesterday, etc.)
                    this.setFilter(column.field, {
                        type: this.getFilterType(column),
                        operator: firstOperator,
                        value: null // No input value needed for preset date ranges
                    }, 'contains');
                } else {
                    this.clearFilter(column.field);
                }
                return;
            }

            // For other column types, use the existing logic
            const firstValue = firstInput.value;
            const secondOperator = this.getCustomDropdownValue(secondDropdown);
            const secondValue = secondInput.value;

            // Get logic operator (AND/OR)
            const andToggle = logicSection.querySelector('[data-logic="AND"] .logic-checkbox');
            const orToggle = logicSection.querySelector('[data-logic="OR"] .logic-checkbox');

            // Check which toggle is actually checked
            const isAndChecked = andToggle && andToggle.src.includes('checkbox-ic.svg');
            const isOrChecked = orToggle && orToggle.src.includes('checkbox-ic.svg');

            // Default to AND if neither is checked
            const logicOperator = isOrChecked ? 'OR' : 'AND';

            if (firstValue || secondValue) {
                const filterConfig = {
                    type: this.getFilterType(column),
                    operator: firstOperator,
                    value: firstValue,
                    logicOperator: logicOperator
                };

                if (secondValue) {
                    filterConfig.secondOperator = secondOperator;
                    filterConfig.secondValue = secondValue;
                }

                this.setFilter(column.field, filterConfig, 'contains');
            } else {
                this.clearFilter(column.field);
            }

            // Immediate checkbox regeneration like the old grid
            // This ensures checkbox lists show only values available in current filtered data
            this.regenerateAllCheckboxLists(column.field);
        };

        // Create AND/OR toggles after all inputs and applyFilter are defined
        const andToggle = this.createLogicToggle('AND', true, logicSection, applyFilter, firstInput, secondInput, firstDropdown, secondDropdown);
        const orToggle = this.createLogicToggle('OR', false, logicSection, applyFilter, firstInput, secondInput, firstDropdown, secondDropdown);
        const separator = document.createElement('span');
        separator.textContent = '/';
        separator.className = 'logic-separator';

        logicSection.appendChild(andToggle);
        logicSection.appendChild(separator);
        logicSection.appendChild(orToggle);

        // Restore logic operator state if it exists
        if (existingFilter && existingFilter.logicOperator) {
            const andCheckbox = andToggle.querySelector('.logic-checkbox');
            const orCheckbox = orToggle.querySelector('.logic-checkbox');

            if (existingFilter.logicOperator === 'OR') {
                andCheckbox.src = 'assets/uncheckedbox-ic.svg';
                andCheckbox.alt = 'Unchecked';
                orCheckbox.src = 'assets/checkbox-ic.svg';
                orCheckbox.alt = 'Checked';
            } else {
                andCheckbox.src = 'assets/checkbox-ic.svg';
                andCheckbox.alt = 'Checked';
                orCheckbox.src = 'assets/uncheckedbox-ic.svg';
                orCheckbox.alt = 'Unchecked';
            }
        }

        // Divider
        const divider = document.createElement('div');
        divider.className = 'filter-divider';

        // Search input with icon - need wrapper for proper positioning
        const searchWrapper = document.createElement('div');
        searchWrapper.className = 'search-input-wrapper';

        const searchIcon = document.createElement('img');
        searchIcon.src = 'assets/search-ic.svg';
        searchIcon.className = 'search-icon';
        searchIcon.alt = 'Search';

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'search-input';
        searchInput.placeholder = 'Search..';

        searchWrapper.appendChild(searchIcon);
        searchWrapper.appendChild(searchInput);

        // Checkbox list container
        const checkboxList = document.createElement('div');
        checkboxList.className = 'filter-checkbox-list';

        // Assemble panel components based on column type (matching old version logic)
        if (columnType === 'text-special') {
            // text-special: Only search input and checkbox list
            panel.appendChild(searchWrapper);
            panel.appendChild(checkboxList);

            // Get unique values and create checkbox list (async for performance)
            let regenerateCheckboxList = () => {};
            this.createCheckboxList(column, checkboxList, searchInput).then(fn => {
                regenerateCheckboxList = fn;
            }).catch(error => {
                console.error('Error creating checkbox list:', error);
            });
        } else if (columnType === 'fixed') {
            // fixed: No filter components at all
            const noFilterMessage = document.createElement('div');
            noFilterMessage.className = 'no-filter-message';
            noFilterMessage.textContent = 'No filtering available for this column';
            panel.appendChild(noFilterMessage);
        } else if (columnType === 'date') {
            // date: Dropdown + conditional inputs (no checkbox list)
            panel.appendChild(firstDropdown);
            panel.appendChild(firstFilterWrapper);
            panel.appendChild(secondFilterWrapper);
        } else {
            // text, numeric, text-numeric: Full filter UI
            panel.appendChild(firstDropdown);
            panel.appendChild(firstFilterWrapper);
            panel.appendChild(logicSection);
            panel.appendChild(secondDropdown);
            panel.appendChild(secondFilterWrapper);
            panel.appendChild(divider);
            panel.appendChild(searchWrapper);
            panel.appendChild(checkboxList);

            // Get unique values and create checkbox list
            let regenerateCheckboxList = () => {};
            this.createCheckboxList(column, checkboxList, searchInput).then(fn => { regenerateCheckboxList = fn; }).catch(err => console.error('Error creating checkbox list:', err));

            // Handle dropdown changes for special cases
            firstDropdown.addEventListener('change', (e) => {
                const operator = e.detail.value;

                if (isNumericOnly && operator === 'inRange') {
                    // For numeric range, hide logic section and show second input
                    logicSection.style.display = 'none';
                    secondDropdown.style.display = 'none';
                    secondFilterWrapper.style.display = 'flex';
                    firstInput.placeholder = 'From (min value)';
                    secondInput.placeholder = 'To (max value)';
                } else if (isDateOnly && operator === 'inRange') {
                    // For date range, show both date inputs
                    logicSection.style.display = 'none';
                    secondDropdown.style.display = 'none';
                    firstFilterWrapper.style.display = 'flex';
                    secondFilterWrapper.style.display = 'flex';
                    firstInput.type = 'date';
                    secondInput.type = 'date';
                    firstInput.placeholder = 'From date';
                    secondInput.placeholder = 'To date';
                } else if (isDateOnly && ['equals', 'notEquals', 'lessThan', 'greaterThan'].includes(operator)) {
                    // For date comparison, show single date input
                    firstFilterWrapper.style.display = 'flex';
                    secondFilterWrapper.style.display = 'none';
                    logicSection.style.display = 'none';
                    secondDropdown.style.display = 'none';
                    firstInput.type = 'date';
                } else if (isDateOnly) {
                    // For predefined date ranges, hide all inputs
                    firstFilterWrapper.style.display = 'none';
                    secondFilterWrapper.style.display = 'none';
                    logicSection.style.display = 'none';
                    secondDropdown.style.display = 'none';
                }

                applyFilter();
            });

            // Show second filter when first filter has content (for non-date columns)
            if (!isDateOnly) {
                firstInput.addEventListener('input', () => {
                    if (firstInput.value.trim() && !isNumericOnly) {
                        logicSection.style.display = 'flex';
                        secondDropdown.style.display = 'block';
                        secondFilterWrapper.style.display = 'flex';
                    } else if (!firstInput.value.trim()) {
                        logicSection.style.display = 'none';
                        secondDropdown.style.display = 'none';
                        secondFilterWrapper.style.display = 'none';
                        secondInput.value = '';
                    }
                });
            }

            // Apply filter on Enter key (immediate)
            firstInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    applyFilter(); // Apply immediately on Enter
                }
            });

            secondInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    applyFilter(); // Apply immediately on Enter
                }
            });

            // Apply filter with debouncing for large datasets performance
            let filterTimeout;
            const debouncedApplyFilter = () => {
                clearTimeout(filterTimeout);
                filterTimeout = setTimeout(() => {
                    applyFilter();
                    // Regenerate current checkbox list to reflect filtered data (like old grid)
                    if (typeof regenerateCheckboxList === 'function') {
                        setTimeout(() => regenerateCheckboxList(), 100);
                    }
                }, 300); // 300ms debounce for performance
            };

            firstInput.addEventListener('input', debouncedApplyFilter);
            secondInput.addEventListener('input', debouncedApplyFilter);

            // Add event listeners for dropdown changes to trigger filter application
            firstDropdown.addEventListener('change', debouncedApplyFilter);
            secondDropdown.addEventListener('change', debouncedApplyFilter);
        }

        return panel;
    }

    /**
     * Create column management tab content
     */
    createManagementTab(column) {
        const panel = document.createElement('div');
        panel.className = 'menu-tab-panel';
        panel.setAttribute('data-tab-panel', 'management');

        // Sort Ascending option
        const sortAscOption = this.createMenuOptionWithIcon('Sort Ascending', 'assets/ascending-ic.svg', () => {
            this.sortColumn(column.field, 'asc');
        });
        panel.appendChild(sortAscOption);

        // Sort Descending option
        const sortDescOption = this.createMenuOptionWithIcon('Sort Descending', 'assets/descending-ic.svg', () => {
            this.sortColumn(column.field, 'desc');
        });
        panel.appendChild(sortDescOption);

        // First divider
        const divider1 = document.createElement('div');
        divider1.className = 'column-management-divider';
        panel.appendChild(divider1);

        // Pin Column option with submenu
        const pinContainer = document.createElement('div');
        pinContainer.className = 'pin-column-container';

        const pinOption = document.createElement('div');
        pinOption.className = 'menu-option pin-column-option';

        const pinMain = document.createElement('div');
        pinMain.className = 'pin-column-main';

        const pinIcon = document.createElement('img');
        pinIcon.src = 'assets/pin-col-ic.svg';
        pinIcon.alt = 'Pin';
        pinIcon.className = 'menu-option-icon';

        const pinText = document.createElement('span');
        pinText.textContent = 'Pin Column';

        const pinArrow = document.createElement('img');
        pinArrow.src = 'assets/arrow-ic.svg';
        pinArrow.alt = 'Arrow';
        pinArrow.className = 'pin-arrow';

        pinMain.appendChild(pinIcon);
        pinMain.appendChild(pinText);
        pinOption.appendChild(pinMain);
        pinOption.appendChild(pinArrow);

        // Pin submenu
        const pinSubmenu = document.createElement('div');
        pinSubmenu.className = 'pin-submenu hidden';

        const currentPinState = this.getColumnPinState(column.field);

        // Pin Left option
        const pinLeftOption = document.createElement('div');
        pinLeftOption.className = 'pin-option';
        const pinLeftCheck = document.createElement('img');
        pinLeftCheck.src = 'assets/checked-option-ic.svg';
        pinLeftCheck.className = `check-icon ${currentPinState === 'left' ? '' : 'hidden'}`;
        const pinLeftText = document.createElement('span');
        pinLeftText.textContent = 'Pin Left';
        pinLeftOption.appendChild(pinLeftCheck);
        pinLeftOption.appendChild(pinLeftText);
        pinLeftOption.addEventListener('click', () => {
            this.pinColumn(column.field, 'left');
            this.updatePinSubmenu(column.field, pinSubmenu);
        });

        // Pin Right option
        const pinRightOption = document.createElement('div');
        pinRightOption.className = 'pin-option';
        const pinRightCheck = document.createElement('img');
        pinRightCheck.src = 'assets/checked-option-ic.svg';
        pinRightCheck.className = `check-icon ${currentPinState === 'right' ? '' : 'hidden'}`;
        const pinRightText = document.createElement('span');
        pinRightText.textContent = 'Pin Right';
        pinRightOption.appendChild(pinRightCheck);
        pinRightOption.appendChild(pinRightText);
        pinRightOption.addEventListener('click', () => {
            this.pinColumn(column.field, 'right');
            this.updatePinSubmenu(column.field, pinSubmenu);
        });

        // Don't Pin option
        const dontPinOption = document.createElement('div');
        dontPinOption.className = 'pin-option';
        const dontPinCheck = document.createElement('img');
        dontPinCheck.src = 'assets/checked-option-ic.svg';
        dontPinCheck.className = `check-icon ${currentPinState === 'none' ? '' : 'hidden'}`;
        const dontPinText = document.createElement('span');
        dontPinText.textContent = "Don't Pin";
        dontPinOption.appendChild(dontPinCheck);
        dontPinOption.appendChild(dontPinText);
        dontPinOption.addEventListener('click', () => {
            this.unpinColumn(column.field);
            this.updatePinSubmenu(column.field, pinSubmenu);
        });

        pinSubmenu.appendChild(pinLeftOption);
        pinSubmenu.appendChild(pinRightOption);
        pinSubmenu.appendChild(dontPinOption);

        // Pin option hover/click behavior
        pinMain.addEventListener('mouseenter', () => {
            pinSubmenu.classList.remove('hidden');
        });
        pinContainer.addEventListener('mouseleave', () => {
            pinSubmenu.classList.add('hidden');
        });

        pinContainer.appendChild(pinOption);
        pinContainer.appendChild(pinSubmenu);
        panel.appendChild(pinContainer);

        // Second divider
        const divider2 = document.createElement('div');
        divider2.className = 'column-management-divider';
        panel.appendChild(divider2);

        // Autosize This Column option
        const autosizeThisOption = this.createMenuOptionWithIcon('Autosize This Column', 'assets/autoresize-ic.svg', () => {
            this.autosizeColumn(column.field);
        });
        panel.appendChild(autosizeThisOption);

        // Autosize All Columns option
        const autosizeAllOption = this.createMenuOptionWithIcon('Autosize All Columns', 'assets/autoresize-ic.svg', () => {
            this.autosizeAllColumns();
        });
        panel.appendChild(autosizeAllOption);

        // Third divider
        const divider3 = document.createElement('div');
        divider3.className = 'column-management-divider';
        panel.appendChild(divider3);

        // Reset Columns option (no icon)
        const resetOption = document.createElement('div');
        resetOption.className = 'menu-option reset-option';
        const resetText = document.createElement('span');
        resetText.textContent = 'Reset Columns';
        resetOption.appendChild(resetText);
        resetOption.addEventListener('click', () => {
            this.resetColumns();
        });
        panel.appendChild(resetOption);

        return panel;
    }

    /**
     * Sort column
     */
    sortColumn(field, direction) {
        // Clear existing sort state
        this.sortState = {};

        // Set new sort state
        this.sortState[field] = direction;

        // Process and render data
        this.processData();
        this.render();
    }

    /**
     * Create column visibility tab content
     */
    createVisibilityTab() {
        const panel = document.createElement('div');
        panel.className = 'menu-tab-panel';
        panel.setAttribute('data-tab-panel', 'visibility');

        // Search input with icon (matching Filter tab style) - need wrapper for proper positioning
        const searchWrapper = document.createElement('div');
        searchWrapper.className = 'search-input-wrapper';

        const searchIcon = document.createElement('img');
        searchIcon.src = './assets/search-ic.svg';
        searchIcon.className = 'search-icon';
        searchIcon.alt = 'Search';

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'search-input';
        searchInput.placeholder = 'Search..';

        searchWrapper.appendChild(searchIcon);
        searchWrapper.appendChild(searchInput);

        // Add stopPropagation to search input
        searchInput.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        searchInput.addEventListener('keydown', (e) => {
            e.stopPropagation();
        });

        // Column list container
        const columnList = document.createElement('div');
        columnList.className = 'column-list';

        // Add drag and drop support to the column list container
        columnList.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            // Update drag indicator position based on mouse position
            const dragIndicator = columnList.querySelector('.drag-indicator');
            if (dragIndicator) {
                const afterElement = this.getDragAfterElement(columnList, e.clientY);
                this.updateDragIndicatorPosition(dragIndicator, columnList, afterElement);
            }
        });

        columnList.addEventListener('drop', (e) => {
            e.preventDefault();
            e.stopPropagation();
            const draggedField = e.dataTransfer.getData('text/plain');

            if (draggedField && this.reorderColumnByIndex) {
                try {
                    // Don't allow dropping fixed columns
                    if (this.isFixedColumn(draggedField)) {
                        return;
                    }

                    // Calculate insertion position based on drop location
                    const afterElement = this.getDragAfterElement(columnList, e.clientY);
                    const validRange = this.getValidInsertionRange();

                    let insertionIndex;
                    if (afterElement == null) {
                        // Insert at the end, but respect valid range
                        insertionIndex = Math.min(validRange.maxIndex, this.processedColumns.length);
                    } else {
                        // Insert before the afterElement
                        const afterField = afterElement.getAttribute('data-field');
                        insertionIndex = this.processedColumns.findIndex(col => col.field === afterField);

                        // Validate that the insertion position is within valid range
                        if (insertionIndex < validRange.minIndex || insertionIndex > validRange.maxIndex) {
                            // Invalid drop position, don't perform the reorder
                            return;
                        }
                    }

                    this.reorderColumnByIndex(draggedField, insertionIndex);
                } catch (error) {
                    console.error('Error during column reordering:', error);
                    // Don't re-throw the error to prevent it from bubbling up
                }
            }

            // Always remove any existing drag indicator after drop
            const di = columnList.querySelector('.drag-indicator');
            if (di) di.remove();
        });

        // Add Select All checkbox as first item in the column list
        const selectAllItem = document.createElement('div');
        selectAllItem.className = 'column-item select-all-item';

        const selectAllCheckboxWrapper = document.createElement('div');
        selectAllCheckboxWrapper.className = 'checkbox-wrapper';

        const selectAllCheckbox = document.createElement('img');
        selectAllCheckbox.className = 'checkbox-icon';
        selectAllCheckbox.draggable = false;
        // Don't set initial src - let updateSelectAllStateInList set the correct state
        selectAllCheckbox.alt = 'Select All';

        const selectAllLabel = document.createElement('label');
        selectAllLabel.textContent = 'Select All';
        selectAllLabel.className = 'column-label';

        selectAllCheckboxWrapper.appendChild(selectAllCheckbox);
        selectAllItem.appendChild(selectAllCheckboxWrapper);
        selectAllItem.appendChild(selectAllLabel);

        // Add Select All functionality
        selectAllItem.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleSelectAllColumnsInList(selectAllCheckbox, columnList);
        });

        // Add Select All as first item in the list
        columnList.appendChild(selectAllItem);

        // Create column items (exclude fixed columns)
        this.processedColumns.forEach((column, index) => {
            if (['checkbox', 'preview', 'actions', 'marketplace'].includes(column.field)) {
                return; // Skip special columns
            }

            const item = this.createVisibilityColumnItem(column);
            item.setAttribute('data-column-index', index);
            columnList.appendChild(item);
        });

        // Initialize Select All state based on current column visibility (after all items are created)
        this.updateSelectAllStateInList(selectAllCheckbox, columnList);

        // Search functionality
        searchInput.addEventListener('input', (e) => {
            e.stopPropagation();
            const searchTerm = e.target.value.toLowerCase();
            const items = columnList.querySelectorAll('.column-item');

            items.forEach(item => {
                const label = item.querySelector('label').textContent.toLowerCase();
                item.style.display = label.includes(searchTerm) ? 'flex' : 'none';
            });
        });

        panel.appendChild(searchWrapper);
        panel.appendChild(columnList);

        return panel;
    }

    /**
     * Create a column item for the visibility tab (matching the original format)
     */
    createVisibilityColumnItem(column) {
        const item = document.createElement('div');
        item.className = 'column-item';
        item.setAttribute('data-field', column.field);

        const isFixed = this.isFixedColumn(column.field);
        item.draggable = !isFixed;
        if (isFixed) item.classList.add('fixed');

        // Create checkbox wrapper using the app's checkbox system
        const checkboxWrapper = document.createElement('div');
        checkboxWrapper.className = 'checkbox-wrapper';

        const checkboxImg = document.createElement('img');
        checkboxImg.className = 'checkbox-icon';
        checkboxImg.draggable = false;
        const isVisible = !column.hide;
        checkboxImg.src = isVisible ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
        checkboxImg.alt = isVisible ? 'Checked' : 'Unchecked';

        // Add grip handle for reordering
        const gripHandle = document.createElement('img');
        gripHandle.src = './assets/grip-ic.svg';
        gripHandle.className = 'grip-handle';
        gripHandle.alt = isFixed ? 'Reordering disabled' : 'Drag to reorder';
        gripHandle.draggable = false;
        if (isFixed) {
            gripHandle.style.opacity = '0.35';
            gripHandle.style.cursor = 'not-allowed';
        }

        const label = document.createElement('label');
        label.textContent = column.headerName || column.field;

        // Toggle functionality
        const toggleColumn = (e) => {
            e.stopPropagation();
            const currentlyVisible = !column.hide;
            if (currentlyVisible) {
                this.setColumnVisible(column.field, false);
                checkboxImg.src = './assets/uncheckedbox-ic.svg';
                checkboxImg.alt = 'Unchecked';
            } else {
                this.setColumnVisible(column.field, true);
                checkboxImg.src = './assets/checkbox-ic.svg';
                checkboxImg.alt = 'Checked';
            }

            // Update Select All state in the visibility tab
            if (this.updateSelectAllStateInVisibilityTab) {
                this.updateSelectAllStateInVisibilityTab();
            }
        };

        // Add click listeners
        checkboxWrapper.addEventListener('click', toggleColumn);
        label.addEventListener('click', toggleColumn);

        // Add drag and drop event listeners only if not fixed
        if (!isFixed && this.addColumnDragListeners) {
            this.addColumnDragListeners(item, column);
        }

        checkboxWrapper.appendChild(checkboxImg);
        item.appendChild(checkboxWrapper);
        item.appendChild(gripHandle);
        item.appendChild(label);

        return item;
    }

    /**
     * Create menu option with icon
     */
    createMenuOptionWithIcon(text, iconSrc, onClick) {
        const option = document.createElement('div');
        option.className = 'menu-option-with-icon';
        option.addEventListener('click', onClick);

        const iconWrapper = document.createElement('div');
        iconWrapper.className = 'menu-option-icon-wrapper';

        const icon = document.createElement('img');
        icon.className = 'menu-option-icon';
        icon.src = iconSrc;
        icon.alt = text;

        const label = document.createElement('span');
        label.className = 'menu-option-label';
        label.textContent = text;

        iconWrapper.appendChild(icon);
        option.appendChild(iconWrapper);
        option.appendChild(label);

        return option;
    }

    /**
     * Setup tab switching functionality
     */
    setupTabSwitching(menu) {
        const tabButtons = menu.querySelectorAll('.menu-tab-btn');

        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                const tabId = button.getAttribute('data-tab');
                this.setActiveTab(menu, tabId);
            });
        });
    }

    /**
     * Set active tab
     */
    setActiveTab(menu, tabId) {
        // Update tab buttons
        const tabButtons = menu.querySelectorAll('.menu-tab-btn');
        tabButtons.forEach(btn => {
            const btnTabId = btn.getAttribute('data-tab');
            const icon = btn.querySelector('.tab-icon');

            if (btnTabId === tabId) {
                btn.classList.add('active');
                // Update icon to active version
                const iconName = this.getIconNameFromTab(btnTabId);
                icon.src = `assets/${iconName}-active-ic.svg`;
            } else {
                btn.classList.remove('active');
                // Update icon to inactive version
                const iconName = this.getIconNameFromTab(btn.getAttribute('data-tab'));
                icon.src = `assets/${iconName}-inactive-ic.svg`;
            }
        });

        // Update tab panels
        const tabPanels = menu.querySelectorAll('.menu-tab-panel');
        tabPanels.forEach(panel => {
            const panelTabId = panel.getAttribute('data-tab-panel');
            if (panelTabId === tabId) {
                panel.classList.add('active');
            } else {
                panel.classList.remove('active');
            }
        });
    }

    /**
     * Get icon name from tab ID
     */
    getIconNameFromTab(tabId) {
        const iconMap = {
            'filter': 'filters',
            'management': 'column-man',
            'visibility': 'show-hide-col'
        };
        return iconMap[tabId] || 'filters';
    }

    /**
     * Set filter for a column - unified function handling both simple and complex filters
     */
    setFilter(field, filterOrValue, filterType = null) {
        window.SnapLogger?.debug('setFilter called', { field, filterOrValue, filterType });

        // Handle both signatures: setFilter(field, filterObject) and setFilter(field, value, type)
        if (typeof filterOrValue === 'object' && filterOrValue !== null) {
            // Complex filter object signature
            this.filterState[field] = filterOrValue;
        } else if (filterOrValue !== undefined && filterOrValue !== null && filterOrValue !== '') {
            // Simple value signature - convert to filter object
            this.filterState[field] = {
                type: filterType || 'text',
                operator: 'contains',
                value: filterOrValue
            };
        } else {
            // Empty/null value - clear filter
            delete this.filterState[field];
        }

        // Apply immediately for responsive performance
        this.processData();
        this.render();

        // Regenerate checkbox lists for other open menus when any filter changes
        this.regenerateAllCheckboxLists(field);

        // Notify external listeners
        if (this.options.onFilter) {
            this.options.onFilter(field, filterOrValue, filterType);
        }
    }

    /**
     * Clear filter for a column (immediate - for internal use)
     */
    clearFilter(field) {
        console.log('🗑️ clearFilter called:', { field });

        // Clear both filter state and checkbox state for this field
        delete this.filterState[field];
        delete this.checkboxState[field];

        // Clear temporary state if it exists
        if (this.tempCheckboxState && this.tempCheckboxState[field]) {
            delete this.tempCheckboxState[field];
        }

        // Apply immediately for responsive performance
        this.processData();
        this.render();

        // Regenerate checkbox lists for other open menus when filter is cleared
        this.regenerateAllCheckboxLists(field);
    }

    // REMOVED: clearFilterDebounced - redundant with main clearFilter function
    // Use clearFilter() directly - it already handles immediate application efficiently

    /**
     * Autosize column to fit content
     */
    autosizeColumn(field) {
        const column = this.processedColumns.find(col => col.field === field);
        if (!column) return;

        // Calculate minimum width based on header text
        const minWidth = this.calculateMinHeaderWidth(column);
        
        // Set to a reasonable autosize width (larger than min-width)
        const autosizeWidth = Math.max(minWidth, 200);
        column.width = autosizeWidth;
        
        this.render();
    }

    /**
     * Reset column to default state
     */
    resetColumn(field) {
        const column = this.processedColumns.find(col => col.field === field);
        if (!column) return;

        // Reset width and other properties
        column.width = 150; // Default width
        delete this.filterState[field];
        delete this.sortState[field];

        this.processData();
        this.render();
    }

    /**
     * Set column visibility
     */
    setColumnVisible(field, visible) {
        const column = this.processedColumns.find(col => col.field === field);
        if (!column) return;

        column.hide = !visible;
        this.render();
    }

    /**
     * Column type mapping system - exact copy from old grid
     */
    getColumnType(field) {
        const columnTypes = {
            // text: Pure text columns
            'brand': 'text',
            'productTitle': 'text',
            'product_title': 'text',
            'title': 'text',

            // numeric: Pure numeric columns
            'price': 'numeric',
            'sales': 'numeric',
            'returns': 'numeric',
            'royalties': 'numeric',
            'bsr': 'numeric',
            'reviews': 'numeric',

            // percentage: Formatted percentage columns
            'returnRate': 'percentage',

            // text-numeric: Mixed content (can be filtered as text or numbers)
            'asin': 'text-numeric',
            'designId': 'text-numeric',
            'design_id': 'text-numeric',

            // text-special: Checkbox-only columns
            'marketplace': 'text-special',
            'productType': 'text-special',
            'product_type': 'text-special',
            'producttype': 'text-special',
            'status': 'text-special',
            'category': 'text-special',

            // date: Date columns
            'firstSold': 'date',
            'lastSold': 'date',
            'firstPublished': 'date',
            'lastUpdated': 'date',
            'firstsold': 'date',
            'lastsold': 'date',
            'firstpublished': 'date',
            'lastupdated': 'date',
            'first_sold': 'date',
            'last_sold': 'date',
            'first_published': 'date',
            'last_updated': 'date',
            'date': 'date',

            // fixed: Special UI columns (no filtering, no sorting, no resizing)
            'actions': 'fixed',
            'checkbox': 'fixed',
            'preview': 'fixed'
        };

        const fieldLower = field.toLowerCase();
        return columnTypes[fieldLower] || columnTypes[field] || 'text'; // Default to text
    }

    /**
     * Get filter type options based on column type - exact copy from old grid
     */
    getFilterTypeOptions(column) {
        const columnType = this.getColumnType(column.field);

        // Define filter options for each column type
        const filterOptionsByType = {
            text: [
                { value: 'contains', label: 'Contains' },
                { value: 'notContains', label: 'Not Contains' },
                { value: 'startsWith', label: 'Starts with' },
                { value: 'endsWith', label: 'Ends with' }
            ],

            numeric: [
                { value: 'equals', label: 'Equals' },
                { value: 'notEquals', label: 'Not equal' },
                { value: 'lessThan', label: 'Less than' },
                { value: 'lessThanOrEqual', label: 'Less than or equals' },
                { value: 'greaterThan', label: 'Greater than' },
                { value: 'greaterThanOrEqual', label: 'Greater than or equals' },
                { value: 'inRange', label: 'In range' }
            ],

            percentage: [
                { value: 'equals', label: 'Equals' },
                { value: 'notEquals', label: 'Not equal' },
                { value: 'lessThan', label: 'Less than' },
                { value: 'lessThanOrEqual', label: 'Less than or equals' },
                { value: 'greaterThan', label: 'Greater than' },
                { value: 'greaterThanOrEqual', label: 'Greater than or equals' },
                { value: 'inRange', label: 'In range' }
            ],

            'text-numeric': [
                { value: 'contains', label: 'Contains' },
                { value: 'notContains', label: 'Not Contains' },
                { value: 'startsWith', label: 'Starts with' },
                { value: 'endsWith', label: 'Ends with' }
            ],

            date: [
                { value: 'pleaseSelect', label: 'Please Select' },
                { value: 'today', label: 'Today' },
                { value: 'yesterday', label: 'Yesterday' },
                { value: 'last7Days', label: 'Last 7 Days' },
                { value: 'last30Days', label: 'Last 30 Days' },
                { value: 'last90Days', label: 'Last 90 Days' },
                { value: 'last6Months', label: 'Last 6 Months' },
                { value: 'currentMonth', label: 'Current Month' },
                { value: 'lastMonth', label: 'Last Month' },
                { value: 'currentYear', label: 'Current Year' },
                { value: 'lastYear', label: 'Last Year' },
                { value: 'equals', label: 'Equals' },
                { value: 'notEquals', label: 'Not equal' },
                { value: 'lessThan', label: 'Less than' },
                { value: 'greaterThan', label: 'Greater than' },
                { value: 'inRange', label: 'In range' }
            ],

            'text-special': [], // No dropdown - checkbox only

            fixed: [] // No filtering at all
        };

        const options = filterOptionsByType[columnType] || filterOptionsByType.text;

        // Return in the format expected by createCustomDropdown
        return options.map(opt => ({
            value: opt.value,
            text: opt.label
        }));
    }

    /**
     * Get filter type based on column type (for internal filtering logic) - exact copy from old grid
     */
    getFilterType(column) {
        const columnType = this.getColumnType(column.field);

        // Map our column types to internal filter types
        switch (columnType) {
            case 'numeric':
            case 'percentage':
                return 'number';
            case 'date':
                return 'date';
            case 'text-special':
                return 'set';
            case 'text':
            case 'text-numeric':
            default:
                return 'text';
        }
    }

    /**
     * Create snap dropdown component (exact copy from old grid)
     */
    createCustomDropdown(options, defaultValue = '') {
        const dropdown = document.createElement('div');
        dropdown.className = 'snap-dropdown';

        const trigger = document.createElement('div');
        trigger.className = 'dropdown-header';

        const triggerText = document.createElement('span');
        triggerText.textContent = defaultValue;

        const arrow = document.createElement('img');
        arrow.src = 'assets/dropdown-ic.svg';
        arrow.alt = 'Dropdown';
        arrow.className = 'dropdown-arrow';

        trigger.appendChild(triggerText);
        trigger.appendChild(arrow);

        const menu = document.createElement('div');
        menu.className = 'dropdown-menu hidden';

        // Parse options (can be HTML string or array)
        let optionsList = [];
        if (typeof options === 'string') {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = options;
            const optionElements = tempDiv.querySelectorAll('option');
            optionsList = Array.from(optionElements).map(opt => ({
                value: opt.value,
                text: opt.textContent
            }));
        } else if (Array.isArray(options)) {
            optionsList = options;
        }

        // Check if defaultValue matches any option value
        const matchingOption = optionsList.find(option => option.value === defaultValue);

        optionsList.forEach(option => {
            // Check if this is a divider
            if (option.divider) {
                const dividerElement = this.createMenuSeparator();
                menu.appendChild(dividerElement);
                return;
            }

            const optionElement = document.createElement('div');
            optionElement.className = 'dropdown-item';
            optionElement.textContent = option.text || option.value;
            optionElement.setAttribute('data-value', option.value);

            // Only add selected class if defaultValue matches an actual option value
            if (matchingOption && option.value === defaultValue) {
                optionElement.classList.add('selected');
                triggerText.textContent = option.text || option.value;
            }

            optionElement.addEventListener('click', (e) => {
                e.stopPropagation();

                // Update selected option
                menu.querySelectorAll('.dropdown-item').forEach(opt => {
                    opt.classList.remove('selected');
                });
                optionElement.classList.add('selected');
                triggerText.textContent = optionElement.textContent;

                // Close dropdown
                dropdown.classList.remove('focused');
                menu.classList.add('hidden');

                // Dispatch change event
                const changeEvent = new CustomEvent('change', {
                    detail: { value: option.value, text: option.text || option.value }
                });
                dropdown.dispatchEvent(changeEvent);
            });

            menu.appendChild(optionElement);
        });

        // Toggle dropdown
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();
            const isOpen = !menu.classList.contains('hidden');

            // Close all other dropdowns
            document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                d.classList.remove('focused');
                d.querySelector('.dropdown-menu').classList.add('hidden');
            });

            if (!isOpen) {
                dropdown.classList.add('focused');
                menu.classList.remove('hidden');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!dropdown.contains(e.target)) {
                dropdown.classList.remove('focused');
                menu.classList.add('hidden');
            }
        });

        dropdown.appendChild(trigger);
        dropdown.appendChild(menu);

        return dropdown;
    }

    /**
     * Get custom dropdown value (exact copy from old grid)
     */
    getCustomDropdownValue(dropdown) {
        const selectedItem = dropdown.querySelector('.dropdown-item.selected');
        return selectedItem ? selectedItem.getAttribute('data-value') : '';
    }

    /**
     * Set custom dropdown value (exact copy from old grid)
     */
    setCustomDropdownValue(dropdown, value) {
        const triggerText = dropdown.querySelector('.dropdown-header span');
        const menu = dropdown.querySelector('.dropdown-menu');
        const items = menu.querySelectorAll('.dropdown-item');

        // Remove selected class from all items
        items.forEach(item => item.classList.remove('selected'));

        // Find and select the matching item
        const matchingItem = Array.from(items).find(item =>
            item.getAttribute('data-value') === value
        );

        if (matchingItem) {
            matchingItem.classList.add('selected');
            triggerText.textContent = matchingItem.textContent;
        }
    }

    /**
     * Create menu separator (helper for dropdown)
     */
    createMenuSeparator() {
        const separator = document.createElement('div');
        separator.className = 'dropdown-separator';
        return separator;
    }

    /**
     * Create checkbox item - exact copy from old grid
     */
    createCheckboxItem(value, checked = false, isSelectAll = false, fieldName = null, applyFilterCallback = null) {
        const item = document.createElement('div');
        item.className = 'filter-checkbox-item';
        if (isSelectAll) {
            item.classList.add('select-all');
        }

        // Create checkbox wrapper using the app's checkbox system (same as Show/Hide)
        const checkboxWrapper = document.createElement('div');
        checkboxWrapper.className = 'checkbox-wrapper';

        const checkboxImg = document.createElement('img');
        checkboxImg.className = 'checkbox-icon';
        checkboxImg.draggable = false;
        checkboxImg.setAttribute('data-value', value);

        // Set appropriate icon based on state
        if (isSelectAll) {
            // Calculate proper initial state for Select All checkbox
            if (fieldName) {
                const currentState = this.getCheckboxState(fieldName);
                const allValues = this._getUniqueValuesSyncCached(fieldName, true, Number.MAX_SAFE_INTEGER);

                if (currentState.size === 0) {
                    checkboxImg.src = './assets/uncheckedbox-ic.svg';
                    checkboxImg.alt = 'Unchecked';
                } else if (currentState.size === allValues.length) {
                    checkboxImg.src = './assets/checkbox-ic.svg';
                    checkboxImg.alt = 'Checked';
                } else {
                    checkboxImg.src = './assets/indeterminate-ic.svg';
                    checkboxImg.alt = 'Indeterminate';
                }
            } else {
                // Fallback to indeterminate if no field name
                checkboxImg.src = './assets/indeterminate-ic.svg';
                checkboxImg.alt = 'Select All';
            }
        } else {
            checkboxImg.src = checked ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
            checkboxImg.alt = checked ? 'Checked' : 'Unchecked';
        }

        const label = document.createElement('label');
        label.className = 'filter-checkbox-label';
        // Show value exactly like the cell displays, but keep raw value for matching
        label.textContent = isSelectAll ? String(value) : String(value);

        // Track checked state
        let isChecked = checked;

        // For Select All, set isChecked based on actual state
        if (isSelectAll && fieldName) {
            const currentState = this.getCheckboxState(fieldName);
            const allValues = this._getUniqueValuesSyncCached(fieldName, true, Number.MAX_SAFE_INTEGER);
            isChecked = currentState.size === allValues.length;
        }

        // Toggle function using centralized state
        const toggleCheckbox = () => {
            if (isSelectAll) {
                // Select All logic using centralized state
                if (!fieldName) return;

                const currentState = this.getCheckboxState(fieldName);
                // Derive all values from the current DOM list instead of the (cleared) unique-values cache
                const parentList = item.parentElement;
                const allValueIcons = parentList.querySelectorAll('.filter-checkbox-item:not(.select-all) .checkbox-icon');
                const allValues = Array.from(allValueIcons).map(ic => ic.getAttribute('data-value'));
                const shouldCheckAll = currentState.size < allValues.length;

                console.log('🔄 Select All clicked:', {
                    field: fieldName,
                    totalValues: allValues.length,
                    currentlyChecked: currentState.size,
                    shouldCheckAll,
                    willApplyFilter: !!applyFilterCallback
                });

                // Update centralized state
                this.setAllCheckboxValues(fieldName, allValues, shouldCheckAll);

                // Update all visual checkboxes
                const checkboxList = item.parentElement;
                const allIcons = checkboxList.querySelectorAll('.checkbox-icon:not([data-value="Select All"])');
                allIcons.forEach(icon => {
                    icon.src = shouldCheckAll ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                    icon.alt = shouldCheckAll ? 'Checked' : 'Unchecked';
                });

                // Update Select All icon
                checkboxImg.src = shouldCheckAll ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                checkboxImg.alt = shouldCheckAll ? 'Checked' : 'Unchecked';
                isChecked = shouldCheckAll;

                // Apply filter using callback
                if (applyFilterCallback) {
                    console.log('🎯 Triggering filter application after Select All');
                    applyFilterCallback();
                } else {
                    console.warn('⚠️ No applyFilterCallback available');
                }
            } else {
                // Regular checkbox toggle using centralized state
                if (!fieldName) return;

                const currentlyChecked = this.getCheckboxState(fieldName).has(value);
                isChecked = !currentlyChecked;

                // Update centralized state
                this.setCheckboxValue(fieldName, value, isChecked);

                // Update visual state
                checkboxImg.src = isChecked ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                checkboxImg.alt = isChecked ? 'Checked' : 'Unchecked';

                // Update Select All visual state
                const checkboxList = item.parentElement;
                const selectAllIcon = checkboxList.querySelector('.checkbox-icon[data-value="Select All"]');
                if (selectAllIcon) {
                    const currentState = this.getCheckboxState(fieldName);
                    // Derive all values from current DOM list for accurate tri-state
                    const allValueIcons = checkboxList.querySelectorAll('.filter-checkbox-item:not(.select-all) .checkbox-icon');
                    const allValues = Array.from(allValueIcons).map(ic => ic.getAttribute('data-value'));

                    if (currentState.size === 0) {
                        selectAllIcon.src = './assets/uncheckedbox-ic.svg';
                        selectAllIcon.alt = 'Unchecked';
                    } else if (currentState.size === allValues.length) {
                        selectAllIcon.src = './assets/checkbox-ic.svg';
                        selectAllIcon.alt = 'Checked';
                    } else {
                        selectAllIcon.src = './assets/indeterminate-ic.svg';
                        selectAllIcon.alt = 'Indeterminate';
                    }
                }

                // Apply filter using callback
                if (applyFilterCallback) {
                    console.log('🎯 Triggering filter application after checkbox change');
                    applyFilterCallback();
                } else {
                    console.warn('⚠️ No applyFilterCallback available');
                }
            }
        };

        // Store checkbox state reference for Select All updates
        item._checkboxState = { isChecked };

        // Add single click handler to the entire item to prevent double-click issues
        item.addEventListener('click', (e) => {
            e.stopPropagation();
            toggleCheckbox();
        });

        checkboxWrapper.appendChild(checkboxImg);
        item.appendChild(checkboxWrapper);
        item.appendChild(label);

        return item;
    }

    /**
     * Create checkbox list for text-special columns with async processing
     */
    async createCheckboxList(column, checkboxList, searchInput) {
        console.log('🔍 createCheckboxList called for field:', column.field);

        // Show loading indicator for large datasets
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'checkbox-loading';
        loadingIndicator.textContent = 'Loading options...';
        loadingIndicator.style.padding = '10px';
        loadingIndicator.style.textAlign = 'center';
        loadingIndicator.style.color = '#666';
        checkboxList.appendChild(loadingIndicator);

        try {
            // Get unique values from current column data (async for large datasets)
            const uniqueValues = await this.getFilteredUniqueValues(column.field, 200);
            console.log('📊 Unique values found:', uniqueValues);

            // Remove loading indicator
            loadingIndicator.remove();

        // Initialize checkbox state for this field
        this.initializeCheckboxState(column.field, uniqueValues);

        // Create apply filter callback
        const applyFilter = () => {
            this.applyCheckboxFilter(column.field);
        };

        // Create "Select All" checkbox using the proper method
        const selectAllItem = this.createCheckboxItem('Select All', true, true, column.field, applyFilter);
        checkboxList.appendChild(selectAllItem);
        console.log('✅ Select All checkbox added');

        // Create checkboxes for each unique value using centralized state
        uniqueValues.forEach(value => {
            const isChecked = this.getCheckboxState(column.field).has(value);
            const checkboxItem = this.createCheckboxItem(value, isChecked, false, column.field, applyFilter);
            checkboxList.appendChild(checkboxItem);
        });
        console.log('✅ All checkbox items added, total items:', checkboxList.children.length);

        // Standard tri-state update for Select All based on current state and visible values
        const initSelectAllIcon = checkboxList.querySelector('.checkbox-icon[data-value="Select All"]');
        if (initSelectAllIcon) {
            const currentState = this.getCheckboxState(column.field);
            const total = uniqueValues.length;
            if (total === 0 || currentState.size === 0) {
                initSelectAllIcon.src = './assets/uncheckedbox-ic.svg';
                initSelectAllIcon.alt = 'Unchecked';
            } else if (currentState.size === total) {
                initSelectAllIcon.src = './assets/checkbox-ic.svg';
                initSelectAllIcon.alt = 'Checked';
            } else {
                initSelectAllIcon.src = './assets/indeterminate-ic.svg';
                initSelectAllIcon.alt = 'Indeterminate';
            }
        }

        // Function to regenerate checkbox list based on current filtered data (like old grid)
        const regenerateCheckboxList = async () => {
            // Get current search term
            const searchTerm = searchInput.value;

            // Get updated unique values based on current filters
            const updatedUniqueValues = await this.getFilteredUniqueValues(column.field, 200);

            // Clear existing checkboxes (except Select All)
            const existingItems = checkboxList.querySelectorAll('.filter-checkbox-item:not(.select-all)');
            existingItems.forEach(item => item.remove());

            // Reset checkbox state to reflect current filtered set (all checked)
            this.checkboxState[column.field] = new Set(updatedUniqueValues);
            updatedUniqueValues.forEach(value => {
                const checkboxItem = this.createCheckboxItem(value, true, false, column.field, applyFilter);
                checkboxList.appendChild(checkboxItem);
            });

            // Reapply search filter if there was one
            if (searchTerm) {
                this.filterCheckboxList(checkboxList, searchTerm);
            }

            // Standard tri-state update for Select All based on current state and visible values
            const selectAllIcon = checkboxList.querySelector('.checkbox-icon[data-value="Select All"]');
            if (selectAllIcon) {
                const current = this.getCheckboxState(column.field);
                const total = updatedUniqueValues.length;
                if (total === 0 || current.size === 0) {
                    selectAllIcon.src = './assets/uncheckedbox-ic.svg';
                    selectAllIcon.alt = 'Unchecked';
                } else if (current.size === total) {
                    selectAllIcon.src = './assets/checkbox-ic.svg';
                    selectAllIcon.alt = 'Checked';
                } else {
                    selectAllIcon.src = './assets/indeterminate-ic.svg';
                    selectAllIcon.alt = 'Indeterminate';
                }
            }
        };

        // Search functionality - filter the checkbox list
        searchInput.addEventListener('input', (e) => {
            this.filterCheckboxList(checkboxList, e.target.value);
        });

        // Return the regenerateCheckboxList function so it can be used by filter inputs
        return regenerateCheckboxList;

        } catch (error) {
            console.error('Error creating checkbox list:', error);
            // Remove loading indicator on error
            if (loadingIndicator && loadingIndicator.parentNode) {
                loadingIndicator.remove();
            }
            // Return empty function to prevent errors
            return () => {};
        } finally {
            // no-op
        }
    }

    /**
     * Get unique values for a field from the current filtered dataset (excludes the field's own filter)
     * Does NOT modify checkbox state - that's handled separately by initializeCheckboxState
     */
    async getFilteredUniqueValues(field, limit = 200) {
        return await this.getUniqueColumnValues(field, true, limit);
    }


    /**
     * Filter checkbox list based on search term - exact copy from old grid
     */
    filterCheckboxList(checkboxList, searchTerm) {
        const items = checkboxList.querySelectorAll('.filter-checkbox-item:not(.select-all)');
        const lowerSearchTerm = searchTerm.toLowerCase();

        items.forEach(item => {
            const label = item.querySelector('.filter-checkbox-label');
            const text = label ? label.textContent.toLowerCase() : '';
            const shouldShow = text.includes(lowerSearchTerm);
            item.style.display = shouldShow ? 'flex' : 'none';
        });
    }

    // Unique list helpers (index-array based)

    /**
     * Get unique values from column data with index arrays, cache, and early exit
     */
    async getUniqueColumnValues(field, useFilteredData = false, limit = 200) {
        // Cache key excludes this field's checkbox state to avoid circular deps
        const filterSig = this._buildFilterSignature(field);
        const cacheKey = `${field}|${filterSig}|${limit}`;
        if (this._uniqueCache[cacheKey]) return this._uniqueCache[cacheKey].slice();

        const values = new Set();
        const rowIdx = useFilteredData ? this._getFilteredIdxExcludingCheckboxes(field) : this._getAllIdx();

        // For large datasets, use chunked processing to prevent UI blocking
        const data = this.data;
        const len = rowIdx.length;
        const target = Math.max(10, limit);
        const chunkSize = len > 50000 ? 5000 : 2000;
        for (let i = 0; i < len; i += chunkSize) {
            const end = Math.min(len, i + chunkSize);
            for (let j = i; j < end; j++) {
                const row = data[rowIdx[j]];
                const v = row && row[field];
                if (v !== null && v !== undefined && v !== '') {
                    values.add(String(v));
                    if (values.size >= target) break;
                }
            }
            if (values.size >= target) break;
            if (end < len) await new Promise(r => setTimeout(r, 0));
        }

        const uniqueValues = this.sortValuesForField(field, Array.from(values));
        this._uniqueCache[cacheKey] = uniqueValues.slice();
        return uniqueValues;

    }

    /** Build a signature of current filter state excluding checkbox values for a given field */
    _buildFilterSignature(excludeField) {
        const entries = Object.entries(this.filterState || {});
        // strip checkedValues for excludeField to avoid circular cache invalidation
        const simplified = entries.map(([f, flt]) => {
            if (f === excludeField) {
                const { type, operator, value, secondOperator, secondValue, logicOperator } = flt || {};
                return [f, { type, operator, value, secondOperator, secondValue, logicOperator }];
            }
            return [f, flt];
        }).sort((a,b) => a[0].localeCompare(b[0]));
        return JSON.stringify(simplified);
    }

    /** Return all row indices [0..data.length) */
    _getAllIdx() { return this.data ? this.data.map((_, i) => i) : []; }

    /**
     * Return cached unique values if present, otherwise [] (non-blocking)
     */
    _getUniqueValuesSyncCached(field, useFilteredData = false, limit = 200) {
        const filterSig = this._buildFilterSignature(field);
        const cacheKey = `${field}|${filterSig}|${limit}`;
        const arr = this._uniqueCache[cacheKey];
        return Array.isArray(arr) ? arr.slice() : [];
    }


    /**
     * Build filtered indices excluding checkbox filter for a specific field
     */
    _getFilteredIdxExcludingCheckboxes(excludeField) {
        // If there are no filters at all, return all indices quickly
        const fs = this.filterState || {};
        if (!fs || Object.keys(fs).length === 0) return this._getAllIdx();
        const out = [];
        const data = this.data;
        const len = data.length;
        for (let i = 0; i < len; i++) {
            const row = data[i];
            let pass = true;
            for (const [field, filter] of Object.entries(fs)) {
                if (field === excludeField) {
                    // Ignore checkbox-only filtering for excludeField but still apply text/date/number ops
                    const { operator, type, value, secondOperator, secondValue, logicOperator, checkedValues } = filter || {};
                    const hasText = operator && operator !== 'pleaseSelect';
                    const hasSecond = secondOperator && secondOperator !== 'pleaseSelect';
                    if (hasText) {
                        const firstOk = this.applySingleCondition(row[field], type, operator, value);
                        let ok = firstOk;
                        if (hasSecond) {
                            const secondOk = this.applySingleCondition(row[field], type, secondOperator, secondValue);
                            ok = (logicOperator === 'OR') ? (firstOk || secondOk) : (firstOk && secondOk);
                        }
                        if (!ok) { pass = false; break; }
                    }
                    // Skip checkedValues match here on purpose
                    continue;
                } else {
                    if (!this.matchesFilter(row[field], filter)) { pass = false; break; }
                }
            }
            if (pass) out.push(i);
        }
        return out;
    }




    // -----------------------
    // Legacy object-returning path below kept for compatibility
    // -----------------------

    /**
     * Get filtered data excluding checkbox filters for a specific field
     * This is used to populate checkbox lists based on other active filters
     */
    getFilteredDataExcludingCheckboxes(excludeField) {
        window.SnapLogger?.debug('getFilteredDataExcludingCheckboxes', excludeField);

        if (Object.keys(this.filterState).length === 0) {
            return [...this.data];
        }

        return this.data.filter(row => {
            return Object.entries(this.filterState).every(([field, filter]) => {
                // Skip checkbox filtering for the field we're generating checkboxes for
                if (field === excludeField) {
                    // FIXED: Apply only text/dropdown filters, completely ignore checkbox filters
                    const {
                        type,
                        operator,
                        value: filterValue,
                        secondOperator,
                        secondValue,
                        logicOperator
                    } = filter;

                    /* debug: applying text-only filter for excluded field */

                    // Apply first condition only if operator and value are valid
                    let firstResult = true;
                    if (operator && operator !== 'pleaseSelect') {
                        if (operator === 'blank' || operator === 'notBlank') {
                            const value = row[field];
                            firstResult = this.applySingleCondition(value, type, operator, filterValue);
                        } else if (filterValue !== undefined && filterValue !== null && filterValue.length > 0) {
                            const value = row[field];
                            firstResult = this.applySingleCondition(value, type, operator, filterValue);
                        }
                    }

                    // Apply second condition if exists
                    let secondResult = true;
                    if (secondOperator && secondOperator !== 'pleaseSelect') {
                        if (secondOperator === 'blank' || secondOperator === 'notBlank') {
                            const value = row[field];
                            secondResult = this.applySingleCondition(value, type, secondOperator, secondValue);
                        } else if (secondValue !== undefined && secondValue !== null && secondValue.length > 0) {
                            const value = row[field];
                            secondResult = this.applySingleCondition(value, type, secondOperator, secondValue);
                        }
                    }

                    // FIXED: Combine results with logic operator if second condition exists
                    if (secondOperator && secondOperator !== 'pleaseSelect' &&
                        ((secondValue && secondValue.length > 0) || secondOperator === 'blank' || secondOperator === 'notBlank')) {

                        console.log('🔍 Applying logic in checkbox filter:', {
                            firstResult,
                            secondResult,
                            logicOperator,
                            operator,
                            value: filterValue,
                            secondOperator,
                            secondValue
                        });

                        if (logicOperator === 'AND') {
                            return firstResult && secondResult;
                        } else if (logicOperator === 'OR') {
                            return firstResult || secondResult;
                        }
                    }

                    return firstResult;
                }

                // Apply other filters normally (including checkbox filters for other fields)
                const value = row[field];
                return this.matchesFilter(value, filter);
            });
        });
    }

    /**
     * Sort values for a specific field (helper method)
     */
    sortValuesForField(field, values) {
        // For now, just sort alphabetically
        // In the future, this could be enhanced with field-specific sorting logic
        return values.sort((a, b) => {
            // Handle numeric values
            const aNum = parseFloat(a);
            const bNum = parseFloat(b);
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return aNum - bNum;
            }
            // Default to string comparison
            return String(a).localeCompare(String(b));
        });
    }

    /**
     * Initialize checkbox state for a field - all checkboxes checked by default
     */
    initializeCheckboxState(field, uniqueValues) {
        if (!this.checkboxState) {
            this.checkboxState = {};
        }

        if (!this.checkboxState[field]) {
            // Initialize with all values checked by default
            this.checkboxState[field] = new Set(uniqueValues);
        }
    }

    /**
     * Get checkbox state for a field - exact copy from old grid
     */
    getCheckboxState(field) {
        return this.checkboxState[field] || new Set();
    }

    /**
     * Set checkbox value for a field - exact copy from old grid
     */
    setCheckboxValue(field, value, checked) {
        if (!this.checkboxState[field]) {
            this.checkboxState[field] = new Set();
        }

        if (checked) {
            this.checkboxState[field].add(value);
        } else {
            this.checkboxState[field].delete(value);
        }
    }

    /**
     * Set all checkbox values for a field - exact copy from old grid
     */
    setAllCheckboxValues(field, allValues, checked) {
        if (!this.checkboxState[field]) {
            this.checkboxState[field] = new Set();
        }

        if (checked) {
            allValues.forEach(value => this.checkboxState[field].add(value));
        } else {
            this.checkboxState[field].clear();
        }
    }

    /**
     * Check if a value is checked - exact copy from old grid
     */
    isValueChecked(field, value) {
        return this.checkboxState[field] && this.checkboxState[field].has(value);
    }

    /**
     * Check a value - exact copy from old grid
     */
    checkValue(field, value) {
        if (!this.checkboxState[field]) {
            this.checkboxState[field] = new Set();
        }
        this.checkboxState[field].add(value);
    }

    /**
     * Uncheck a value - exact copy from old grid
     */
    uncheckValue(field, value) {
        if (this.checkboxState[field]) {
            this.checkboxState[field].delete(value);
        }
    }

    /**
     * Apply checkbox filter with correct filter type and operator
     */
    applyCheckboxFilter(field) {
        const checkedValues = Array.from(this.checkboxState[field] || []);

        console.log('🎯 applyCheckboxFilter called:', {
            field,
            checkedValuesCount: checkedValues.length,
            checkedValues: checkedValues.slice(0, 3) // Show first 3 for debugging
        });

        if (checkedValues.length === 0) {
            // No values selected = hide all rows for this field
            this.setFilter(field, {
                type: 'checkbox',
                operator: 'in',
                checkedValues: []
            });
        } else {
            // Set filter with proper checkbox type and operator
            this.setFilter(field, {
                type: 'checkbox',
                operator: 'in', // Use 'in' operator for checkbox filtering
                checkedValues: checkedValues
            });
        }

        // Note: setFilter already calls regenerateAllCheckboxLists, so no need to call it again
    }

    /**
     * Regenerate checkbox lists for all open column menus except the specified field
     * Optimized with debouncing to prevent excessive regeneration
     */
    regenerateAllCheckboxLists(excludeField) {
        // Debounce regeneration to prevent excessive calls
        if (this.regenerateDebounceTimer) {
            clearTimeout(this.regenerateDebounceTimer);
        }

        this.regenerateDebounceTimer = setTimeout(() => {
            // Find all open column menus with checkbox lists
            const openMenus = document.querySelectorAll('.snap-grid-column-menu');

            openMenus.forEach(menu => {
                const fieldName = menu.getAttribute('data-field');
                if (fieldName && fieldName !== excludeField) {
                    const checkboxList = menu.querySelector('.filter-checkbox-list');
                    if (checkboxList) {
                        // Trigger regeneration by dispatching a custom event
                        // This allows the existing regenerateCheckboxList function to handle it
                        const regenerateEvent = new CustomEvent('regenerateCheckboxList');
                        checkboxList.dispatchEvent(regenerateEvent);
                    }
                }
            });
        }, 100); // Debounce for 100ms to batch multiple calls
    }

    // REMOVED: regenerateCheckboxListForField - redundant with regenerateCheckboxList function
    // The regenerateCheckboxList function in createCheckboxList already handles this functionality

    /**
     * Create logic toggle for AND/OR filter logic
     */
    createLogicToggle(text, isActive, logicSection, applyFilter, firstInput, secondInput, firstDropdown, secondDropdown) {
        const wrapper = document.createElement('div');
        wrapper.className = 'logic-toggle';
        wrapper.setAttribute('data-logic', text);

        const checkbox = document.createElement('img');
        checkbox.className = 'logic-checkbox';
        checkbox.src = isActive ? 'assets/checkbox-ic.svg' : 'assets/uncheckedbox-ic.svg';
        checkbox.alt = isActive ? 'Checked' : 'Unchecked';
        checkbox.draggable = false;

        const label = document.createElement('span');
        label.textContent = text;
        label.className = 'logic-label';

        wrapper.appendChild(checkbox);
        wrapper.appendChild(label);

        // Toggle functionality - mutually exclusive
        wrapper.addEventListener('click', (e) => {
            e.stopPropagation();

            // Get both toggles
            const andToggle = logicSection.querySelector('[data-logic="AND"]');
            const orToggle = logicSection.querySelector('[data-logic="OR"]');

            // Reset both to unchecked
            const andCheckbox = andToggle.querySelector('.logic-checkbox');
            const orCheckbox = orToggle.querySelector('.logic-checkbox');

            andCheckbox.src = 'assets/uncheckedbox-ic.svg';
            andCheckbox.alt = 'Unchecked';

            orCheckbox.src = 'assets/uncheckedbox-ic.svg';
            orCheckbox.alt = 'Unchecked';

            // Set clicked one to checked
            checkbox.src = 'assets/checkbox-ic.svg';
            checkbox.alt = 'Checked';

            console.log('🔍 Logic toggle clicked:', text, 'is now active');

            // Trigger filter immediately when AND/OR logic changes
            if (applyFilter && typeof applyFilter === 'function') {
                console.log('🔍 Applying filter due to logic change to:', text);
                applyFilter(); // Apply immediately when logic changes
            }
        });

        return wrapper;
    }

    /**
     * Create menu option with icon
     */
    createMenuOptionWithIcon(text, iconSrc, action) {
        const option = document.createElement('div');
        option.className = 'menu-option-with-icon';

        const iconWrapper = document.createElement('div');
        iconWrapper.className = 'menu-option-icon-wrapper';

        const icon = document.createElement('img');
        icon.src = iconSrc;
        icon.className = 'menu-option-icon';
        icon.alt = text;
        icon.draggable = false;

        const label = document.createElement('span');
        label.textContent = text;
        label.className = 'menu-option-label';

        iconWrapper.appendChild(icon);
        option.appendChild(iconWrapper);
        option.appendChild(label);

        option.addEventListener('click', (e) => {
            e.stopPropagation();
            action();
        });

        return option;
    }

    /**
     * Get column pin state (stub implementation)
     */
    getColumnPinState(field) {
        const col = this.getColumn(field);
        if (!col || !col.pinned) return 'none';
        return col.pinned === 'left' || col.pinned === 'right' ? col.pinned : 'none';
    }

    /**
     * Pin column (stub implementation)
     */
    pinColumn(field, direction) {
        // direction: 'left' | 'right'
        this.setColumnPinned(field, direction);
    }

    /**
     * Unpin column (stub implementation)
     */
    unpinColumn(field) {
        // Clear pinned state, then restore to original center order
        const col = this.getColumn(field);
        if (!col) return;
        col.pinned = null;
        this.sortColumnsByPinned();
        this.restoreUnpinnedColumnOrder(field);
        this.render();
    }

    /**
     * Update pin submenu (stub implementation)
     */
    updatePinSubmenu(field, submenu) {
        if (!submenu) return;
        const state = this.getColumnPinState(field);
        const pinLeftCheck = submenu.querySelector('.pin-option:nth-child(1) .check-icon');
        const pinRightCheck = submenu.querySelector('.pin-option:nth-child(2) .check-icon');
        const dontPinCheck = submenu.querySelector('.pin-option:nth-child(3) .check-icon');

        if (pinLeftCheck) pinLeftCheck.className = `check-icon ${state === 'left' ? '' : 'hidden'}`;
        if (pinRightCheck) pinRightCheck.className = `check-icon ${state === 'right' ? '' : 'hidden'}`;
        if (dontPinCheck) dontPinCheck.className = `check-icon ${state === 'none' ? '' : 'hidden'}`;
    }

    /**
     * Restore unpinned column to its original order among center columns
     * based on the initial options.columns configuration order.
     */
    restoreUnpinnedColumnOrder(field) {
        const currentIndex = this.getColumnIndex(field);
        if (currentIndex < 0) return;

        // Build mapping from field -> original order index
        const originalOrderIndex = new Map(
            this.options.columns.map((c, i) => [c.field, i])
        );

        // Collect center (unpinned) column indices and compute target position
        const centerEntries = this.processedColumns
            .map((c, i) => ({ c, i }))
            .filter(x => (x.c.pinned || null) === null);

        if (centerEntries.length === 0) return;

        const minCenter = Math.min(...centerEntries.map(x => x.i));

        // Sort center fields by their original order
        const centerFieldsByOriginal = centerEntries
            .map(x => x.c.field)
            .sort((a, b) => (originalOrderIndex.get(a) ?? 1e9) - (originalOrderIndex.get(b) ?? 1e9));

        const posInCenter = centerFieldsByOriginal.indexOf(field);
        if (posInCenter < 0) return;

        const targetIndex = minCenter + posInCenter;
        if (targetIndex !== currentIndex) {
            this.moveColumn(currentIndex, targetIndex);
        }
    }

    /**
     * Autosize all columns (stub implementation)
     */
    autosizeAllColumns() {
        const excluded = ['checkbox', 'preview', 'actions'];
        this.processedColumns.forEach(col => {
            if (!col.hide && !excluded.includes(col.field)) {
                this.autosizeColumn(col.field);
            }
        });
        // autosizeColumn already calls render; avoid excessive renders by final refresh if needed
        // this.render(); // not needed due to per-column render inside autosizeColumn
    }

    /**
     * Reset columns (stub implementation)
     */
    resetColumns() {
        // Reset widths to minimums and restore default pinning
        const excluded = ['checkbox', 'preview', 'actions'];

        // Reset widths and visibility
        this.processedColumns.forEach(col => {
            // Reset visibility (show everything except respect explicit hide for special if any)
            if (!excluded.includes(col.field)) {
                col.hide = false;
            }

            // Reset width to minimum reasonable width
            const minWidth = this.calculateMinHeaderWidth(col);
            col.width = Math.max(minWidth, col.field === 'actions' ? (col.width || 96) : minWidth);
        });

        // Default pinning: checkbox, preview -> left; actions -> right; others -> none
        this.processedColumns.forEach(col => {
            if (col.field === 'checkbox' || col.field === 'preview') {
                col.pinned = 'left';
            } else if (col.field === 'actions') {
                col.pinned = 'right';
            } else {
                col.pinned = null;
            }
        });

        // Re-sort by pinning and render
        this.sortColumnsByPinned();
        this.render();
    }

    /**
     * Check if column is fixed (stub implementation)
     */
    isFixedColumn(field) {
        return ['checkbox', 'preview', 'actions'].includes(field);
    }

    /**
     * Get valid insertion range for reordering visibility list items
     */
    getValidInsertionRange() {
        return this.getValidInsertionRangeForOrder(this.processedColumns);
    }

    /**
     * Compute valid insertion range for a given order of columns
     * Ensures we don't cross special/fixed columns like checkbox/preview/actions
     */
    getValidInsertionRangeForOrder(columnOrder) {
        if (!Array.isArray(columnOrder) || columnOrder.length === 0) {
            return { minIndex: 0, maxIndex: 0 };
        }

        const isReorderable = (col) => col && !['checkbox', 'preview', 'actions'].includes(col.field);
        const reorderableIndices = [];
        for (let i = 0; i < columnOrder.length; i++) {
            const col = columnOrder[i];
            if (isReorderable(col)) reorderableIndices.push(i);
        }

        if (reorderableIndices.length === 0) {
            return { minIndex: 0, maxIndex: 0 };
        }

        const minIndex = Math.min(...reorderableIndices);
        const maxIndexExclusive = Math.max(...reorderableIndices) + 1; // insert after last reorderable
        return { minIndex, maxIndex: maxIndexExclusive };
    }

    /**
     * Get the element after which the dragged item should be inserted
     */
    getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.column-item:not(.dragging)')];

        // Translate processedColumns range to list indices (list includes Select All at index 0)
        const validRange = this.getValidInsertionRange();
        const validElements = draggableElements.filter((_, index) => {
            // index 0 is the Select All item; positions start effectively at 0 for before first item
            return index >= validRange.minIndex - 1 && index < validRange.maxIndex;
        });

        return validElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;
            if (offset < 0 && offset > closest.offset) {
                return { offset, element: child };
            }
            return closest;
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    /**
     * Update drag indicator position based on drop target
     */
    updateDragIndicatorPosition(indicator, container, afterElement) {
        if (!indicator || !container) return;

        const columnListRect = container.getBoundingClientRect();
        const validRange = this.getValidInsertionRange();
        const scrollTop = container.scrollTop;
        const allItems = [...container.querySelectorAll('.column-item:not(.dragging)')];

        if (afterElement == null) {
            const maxValidIndex = Math.min(validRange.maxIndex - 1, allItems.length - 1);
            const lastValidItem = allItems[maxValidIndex];
            if (lastValidItem) {
                const rect = lastValidItem.getBoundingClientRect();
                indicator.style.top = (rect.bottom - columnListRect.top + scrollTop) + 'px';
            } else {
                const minValidItem = allItems[validRange.minIndex - 1];
                if (minValidItem) {
                    const rect = minValidItem.getBoundingClientRect();
                    indicator.style.top = (rect.bottom - columnListRect.top + scrollTop) + 'px';
                } else {
                    indicator.style.top = scrollTop + 'px';
                }
            }
        } else {
            const afterIndex = allItems.indexOf(afterElement);
            if (afterIndex >= validRange.minIndex - 1 && afterIndex < validRange.maxIndex) {
                const rect = afterElement.getBoundingClientRect();
                indicator.style.top = (rect.top - columnListRect.top + scrollTop) + 'px';
            } else {
                indicator.classList.remove('active');
                return;
            }
        }

        indicator.style.left = '0px';
        indicator.style.right = '0px';
        indicator.classList.add('active');
    }

    /**
     * Reorder column by insertion index within processedColumns
     */
    reorderColumnByIndex(field, index) {
        if (!field) return;
        if (this.isFixedColumn(field)) return;

        const currentOrder = [...this.processedColumns];
        const fromIndex = currentOrder.findIndex(c => c.field === field);
        if (fromIndex === -1) return;

        // Remove dragged column
        const [dragged] = currentOrder.splice(fromIndex, 1);

        // Compute valid range after removal
        const { minIndex, maxIndex } = this.getValidInsertionRangeForOrder(currentOrder);
        let insertionIndex = index;
        if (fromIndex < insertionIndex) insertionIndex--; // account for removal shift
        insertionIndex = Math.max(minIndex, Math.min(insertionIndex, maxIndex));

        currentOrder.splice(insertionIndex, 0, dragged);
        this.processedColumns = currentOrder;

        if (typeof this.computePinnedOffsets === 'function') {
            this.computePinnedOffsets();
        }
        this.render();

        if (typeof this.updateAllOpenVisibilityLists === 'function') {
            this.updateAllOpenVisibilityLists();
        }
    }

    /**
     * Toggle select all columns in list (stub implementation)
     */
    toggleSelectAllColumnsInList(checkbox, columnList) {
        console.log('Toggle select all columns');
        // Implement actual select all logic later
    }

    /**
     * Update select all state in list (stub implementation)
     */
    updateSelectAllStateInList(checkbox, columnList) {
        // Set initial state to checked for now
        checkbox.src = 'assets/checkbox-ic.svg';
        checkbox.alt = 'Checked';
    }

    /**
     * Update select all state in visibility tab (stub implementation)
     */
    updateSelectAllStateInVisibilityTab() {
        console.log('Update select all state in visibility tab');
        // Implement actual update logic later
    }

    /**
     * Add drag and drop listeners to a visibility list item
     */
    addColumnDragListeners(item, column) {
        if (!item) return;

        item.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', column.field);
            e.dataTransfer.effectAllowed = 'move';
            item.classList.add('dragging');

            const columnList = item.parentElement;
            let dragIndicator = columnList.querySelector('.drag-indicator');
            if (!dragIndicator) {
                dragIndicator = document.createElement('div');
                dragIndicator.className = 'drag-indicator';
                columnList.appendChild(dragIndicator);
            }
            dragIndicator.classList.add('active');
        });

        item.addEventListener('dragend', () => {
            item.classList.remove('dragging');
            const columnList = item.parentElement;
            if (columnList) {
                const dragIndicator = columnList.querySelector('.drag-indicator');
                if (dragIndicator) dragIndicator.remove();
                columnList.querySelectorAll('.column-item').forEach(el => el.classList.remove('drag-over'));
            }
        });

        item.addEventListener('dragenter', (e) => {
            e.preventDefault();
            item.classList.add('drag-over');
        });

        item.addEventListener('dragleave', (e) => {
            if (!item.contains(e.relatedTarget)) {
                item.classList.remove('drag-over');
            }
        });
    }

    /**
     * Rebuild any open visibility lists to reflect current processedColumns order
     */
    updateAllOpenVisibilityLists() {
        const lists = document.querySelectorAll('.snap-grid-column-menu .menu-tab-panel[data-tab-panel="visibility"] .column-list');
        lists.forEach((columnList) => {
            if (!columnList) return;
            const selectAll = columnList.querySelector('.select-all-item');
            columnList.innerHTML = '';
            if (selectAll) columnList.appendChild(selectAll);

            this.processedColumns.forEach((column, index) => {
                if (['checkbox', 'preview', 'actions'].includes(column.field)) return;
                const item = this.createVisibilityColumnItem(column);
                item.setAttribute('data-column-index', index);
                columnList.appendChild(item);
            });

            const selectAllCheckbox = selectAll ? selectAll.querySelector('.checkbox-icon') : null;
            if (selectAllCheckbox) this.updateSelectAllStateInList(selectAllCheckbox, columnList);
        });
    }

    /**
     * Toggle Select All columns functionality for columns in the list
     */
    toggleSelectAllColumnsInList(selectAllCheckbox, columnList) {
        const isCurrentlyAllSelected = this.areAllColumnsInListVisible(columnList);

        if (isCurrentlyAllSelected) {
            // Deselect all columns in the list (hide them)
            this.hideAllColumnsInList(columnList);
            selectAllCheckbox.src = './assets/uncheckedbox-ic.svg';
            selectAllCheckbox.alt = 'Unchecked';
        } else {
            // Select all columns in the list (show them)
            this.showAllColumnsInList(columnList);
            selectAllCheckbox.src = './assets/checkbox-ic.svg';
            selectAllCheckbox.alt = 'Checked';
        }

        // Update individual column checkboxes in the list
        this.updateColumnCheckboxesInList(columnList);
    }

    /**
     * Check if all columns in the list are currently visible
     */
    areAllColumnsInListVisible(columnList) {
        const columnItems = columnList.querySelectorAll('.column-item:not(.select-all-item)');
        let visibleCount = 0;

        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field) {
                const column = this.processedColumns.find(col => col.field === field);
                if (column && !column.hide) {
                    visibleCount++;
                }
            }
        });

        return visibleCount === columnItems.length;
    }

    /**
     * Hide all columns that are in the list
     */
    hideAllColumnsInList(columnList) {
        const columnItems = columnList.querySelectorAll('.column-item:not(.select-all-item)');
        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field) {
                this.setColumnVisible(field, false);
            }
        });
    }

    /**
     * Show all columns that are in the list
     */
    showAllColumnsInList(columnList) {
        const columnItems = columnList.querySelectorAll('.column-item:not(.select-all-item)');
        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field) {
                this.setColumnVisible(field, true);
            }
        });
    }

    /**
     * Update individual column checkboxes in the list
     */
    updateColumnCheckboxesInList(columnList) {
        const columnItems = columnList.querySelectorAll('.column-item:not(.select-all-item)');
        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field) {
                const checkbox = item.querySelector('.checkbox-icon');
                if (checkbox) {
                    const column = this.processedColumns.find(col => col.field === field);
                    const isVisible = column && !column.hide;
                    checkbox.src = isVisible ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                    checkbox.alt = isVisible ? 'Checked' : 'Unchecked';
                }
            }
        });
    }

    /**
     * Update Select All state in visibility tab
     */
    updateSelectAllStateInVisibilityTab() {
        const visibilityTab = document.querySelector('[data-tab-panel="visibility"]');
        if (!visibilityTab) return;

        const columnList = visibilityTab.querySelector('.column-list');
        const selectAllCheckbox = visibilityTab.querySelector('.select-all-item .checkbox-icon');
        if (selectAllCheckbox && columnList) {
            this.updateSelectAllStateInList(selectAllCheckbox, columnList);
        }
    }

    /**
     * Update Select All state based on columns in the list
     */
    updateSelectAllStateInList(selectAllCheckbox, columnList) {
        const columnItems = columnList.querySelectorAll('.column-item:not(.select-all-item)');
        let visibleCount = 0;

        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field) {
                const column = this.processedColumns.find(col => col.field === field);
                if (column && !column.hide) {
                    visibleCount++;
                }
            }
        });

        if (visibleCount === 0) {
            selectAllCheckbox.src = './assets/uncheckedbox-ic.svg';
            selectAllCheckbox.alt = 'Unchecked';
        } else if (visibleCount === columnItems.length) {
            selectAllCheckbox.src = './assets/checkbox-ic.svg';
            selectAllCheckbox.alt = 'Checked';
        } else {
            selectAllCheckbox.src = './assets/indeterminate-ic.svg';
            selectAllCheckbox.alt = 'Indeterminate';
        }
    }

    /**
     * Determine if a column is fixed (non-draggable/non-reorderable)
     */
    isFixedColumn(field) {
        return ['checkbox', 'preview', 'actions'].includes(field);
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SnapGrid;
} else if (typeof window !== 'undefined') {
    window.SnapGrid = SnapGrid;
}
