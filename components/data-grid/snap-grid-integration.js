/**
 * SnapGrid Integration Component
 * Integration example for using SnapGrid within the Snap Dashboard ecosystem
 * Following the component pattern established by other Snap components
 */

// SnapGrid Integration Component
const snapGridIntegrationComponent = {
    // Component initialization
    init() {
        console.log('🔧 Initializing SnapGrid Integration Component...');
        this.setupGridContainer();
        this.loadSampleData();
    },

    // Set up the grid container in the dashboard
    setupGridContainer() {
        const mainContent = document.querySelector('.main-content');
        if (!mainContent) {
            console.error('Main content area not found');
            return;
        }

        // Create grid section
        const gridSection = document.createElement('div');
        gridSection.className = 'snap-grid-section';
        gridSection.innerHTML = `
            <div class="section-header">
                <h2 class="section-title">Product Data Grid</h2>
                <div class="section-controls">
                    <button class="snap-btn snap-btn-secondary" onclick="snapGridIntegrationComponent.exportData()">
                        📊 Export CSV
                    </button>
                    <button class="snap-btn snap-btn-secondary" onclick="snapGridIntegrationComponent.refreshGrid()">
                        🔄 Refresh
                    </button>
                </div>
            </div>
            <div class="grid-container" id="dashboardGrid" style="height: 500px;"></div>
        `;

        mainContent.appendChild(gridSection);
    },

    // Load sample data (in real app, this would come from your data service)
    loadSampleData() {
        // Sample product data that might come from your dashboard data service
        const sampleData = [
            {
                id: 1,
                productName: 'iPhone 14 Pro',
                category: 'Electronics',
                price: 999,
                stock: 150,
                sales: 1250,
                revenue: 1248750,
                marketplace: 'Amazon US',
                status: 'Active',
                lastUpdated: new Date('2024-01-15')
            },
            {
                id: 2,
                productName: 'Samsung Galaxy S23',
                category: 'Electronics',
                price: 899,
                stock: 200,
                sales: 980,
                revenue: 881020,
                marketplace: 'Amazon UK',
                status: 'Active',
                lastUpdated: new Date('2024-01-14')
            },
            {
                id: 3,
                productName: 'MacBook Pro M2',
                category: 'Computers',
                price: 1999,
                stock: 75,
                sales: 450,
                revenue: 899550,
                marketplace: 'Amazon US',
                status: 'Low Stock',
                lastUpdated: new Date('2024-01-13')
            },
            {
                id: 4,
                productName: 'iPad Air',
                category: 'Tablets',
                price: 599,
                stock: 300,
                sales: 750,
                revenue: 449250,
                marketplace: 'Amazon CA',
                status: 'Active',
                lastUpdated: new Date('2024-01-12')
            },
            {
                id: 5,
                productName: 'AirPods Pro',
                category: 'Audio',
                price: 249,
                stock: 500,
                sales: 2100,
                revenue: 522900,
                marketplace: 'Amazon US',
                status: 'Active',
                lastUpdated: new Date('2024-01-11')
            }
        ];

        // Generate more sample data
        for (let i = 6; i <= 100; i++) {
            sampleData.push({
                id: i,
                productName: `Product ${i}`,
                category: ['Electronics', 'Computers', 'Tablets', 'Audio', 'Accessories'][Math.floor(Math.random() * 5)],
                price: Math.floor(Math.random() * 2000) + 100,
                stock: Math.floor(Math.random() * 1000),
                sales: Math.floor(Math.random() * 5000),
                revenue: 0, // Will be calculated
                marketplace: ['Amazon US', 'Amazon UK', 'Amazon CA', 'Amazon DE'][Math.floor(Math.random() * 4)],
                status: ['Active', 'Inactive', 'Low Stock'][Math.floor(Math.random() * 3)],
                lastUpdated: new Date(2024, 0, Math.floor(Math.random() * 30) + 1)
            });
            
            // Calculate revenue
            sampleData[i - 1].revenue = sampleData[i - 1].price * sampleData[i - 1].sales;
        }

        this.createGrid(sampleData);
    },

    // Create the grid with dashboard-specific configuration
    createGrid(data) {
        const container = document.getElementById('dashboardGrid');
        if (!container) {
            console.error('Grid container not found');
            return;
        }

        // Column definitions matching dashboard data structure
        const columns = [
            {
                field: 'id',
                headerName: 'ID',
                width: 80,
                type: 'number',
                sortable: true,
                filterable: true
            },
            {
                field: 'productName',
                headerName: 'Product Name',
                width: 250,
                sortable: true,
                filterable: true,
                editable: true
            },
            {
                field: 'category',
                headerName: 'Category',
                width: 120,
                sortable: true,
                filterable: true
            },
            {
                field: 'price',
                headerName: 'Price',
                width: 100,
                type: 'currency',
                sortable: true,
                filterable: true,
                editable: true
            },
            {
                field: 'stock',
                headerName: 'Stock',
                width: 100,
                type: 'number',
                sortable: true,
                filterable: true,
                editable: true,
                cellRenderer: (value, column, rowData) => {
                    const color = value < 100 ? '#F44336' : value < 200 ? '#FF9800' : '#4CAF50';
                    return `<span style="color: ${color}; font-weight: 500;">${value}</span>`;
                }
            },
            {
                field: 'sales',
                headerName: 'Sales',
                width: 100,
                type: 'number',
                sortable: true,
                filterable: true
            },
            {
                field: 'revenue',
                headerName: 'Revenue',
                width: 120,
                type: 'currency',
                sortable: true,
                filterable: true
            },
            {
                field: 'marketplace',
                headerName: 'Marketplace',
                width: 120,
                sortable: true,
                filterable: true,
                cellRenderer: (value) => {
                    const flagMap = {
                        'Amazon US': '🇺🇸',
                        'Amazon UK': '🇬🇧',
                        'Amazon CA': '🇨🇦',
                        'Amazon DE': '🇩🇪'
                    };
                    return `${flagMap[value] || '🌍'} ${value}`;
                }
            },
            {
                field: 'status',
                headerName: 'Status',
                width: 100,
                sortable: true,
                filterable: true,
                cellRenderer: (value) => {
                    // Map status values to badge classes (confirmed mapping)
                    const statusMap = {
                        // Blue Badge
                        'Processing': 'status-blue',
                        'Auto-uploaded': 'status-blue',
                        'Translating': 'status-blue',
                        
                        // Amber Badge
                        'Under Review': 'status-amber',
                        'Timed Out': 'status-amber',
                        'Locked': 'status-amber',
                        
                        // Red Badge
                        'Declined': 'status-red',
                        'Rejected': 'status-red',
                        'Removed': 'status-red',
                        
                        // Green Badge
                        'Live': 'status-green',
                        
                        // Gray Badge
                        'Draft': 'status-gray'
                    };
                    const className = statusMap[value] || 'status-gray';
                    const safe = String(value || '').replace(/[&<>"']/g, (match) => {
                        const escape = { '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;' };
                        return escape[match];
                    });
                    return `<span class="snap-grid-status ${className}"><span class="dot"></span><span class="text">${safe}</span></span>`;
                }
            },
            {
                field: 'lastUpdated',
                headerName: 'Last Updated',
                width: 140,
                type: 'date',
                sortable: true,
                filterable: true
            }
        ];

        // Create grid with dashboard theme
        this.grid = new SnapGrid(container, {
            data: data,
            columns: columns,
            virtualScrolling: true,
            sortable: true,
            filterable: true,
            resizable: true,
            selectable: true,
            editable: true,
            theme: 'default',
            
            // Dashboard-specific callbacks
            onRowClick: (rowData, rowIndex, event) => {
                console.log('Product selected:', rowData);
                // Could trigger dashboard actions like showing product details
            },
            
            onCellEdit: (field, newValue, oldValue, rowData, rowIndex) => {
                console.log('Product data updated:', { field, newValue, oldValue, rowData });
                
                // Recalculate revenue if price or sales changed
                if (field === 'price' || field === 'sales') {
                    rowData.revenue = rowData.price * rowData.sales;
                    this.grid.refresh();
                }
                
                // In real app, you would sync this change to your backend
                this.syncDataChange(rowData, field, newValue);
            },
            
            onSort: (field, direction) => {
                console.log('Data sorted:', { field, direction });
                // Could save sort preferences to user settings
            },
            
            onFilter: (field, value, type) => {
                console.log('Data filtered:', { field, value, type });
                // Could save filter preferences to user settings
            }
        });

        console.log('✅ Dashboard grid created with', data.length, 'products');
    },

    // Sync data changes (placeholder for real implementation)
    syncDataChange(rowData, field, newValue) {
        // In a real dashboard, you would:
        // 1. Validate the change
        // 2. Send update to backend API
        // 3. Handle success/error responses
        // 4. Update local data cache
        
        console.log('🔄 Syncing data change to backend...', {
            productId: rowData.id,
            field: field,
            newValue: newValue
        });
        
        // Simulate API call
        setTimeout(() => {
            console.log('✅ Data change synced successfully');
        }, 500);
    },

    // Export data functionality
    exportData() {
        if (this.grid) {
            const filename = `dashboard-products-${new Date().toISOString().split('T')[0]}.csv`;
            this.grid.exportToCsv(filename);
            console.log('📊 Dashboard data exported:', filename);
        }
    },

    // Refresh grid
    refreshGrid() {
        if (this.grid) {
            this.grid.refresh();
            console.log('🔄 Dashboard grid refreshed');
        }
    },

    // Component render method (following Snap component pattern)
    render() {
        this.init();
    },

    // Component cleanup
    destroy() {
        if (this.grid) {
            this.grid.destroy();
            this.grid = null;
        }
        
        const gridSection = document.querySelector('.snap-grid-section');
        if (gridSection) {
            gridSection.remove();
        }
        
        console.log('🗑️ SnapGrid Integration Component destroyed');
    }
};

// Export for use in Snap Dashboard
if (typeof window !== 'undefined') {
    window.snapGridIntegrationComponent = snapGridIntegrationComponent;
}

// Auto-initialize if this is loaded as a standalone component
if (typeof module === 'undefined' && document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        snapGridIntegrationComponent.render();
    });
}
