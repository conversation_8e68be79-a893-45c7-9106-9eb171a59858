/**
 * ChunkedDataLoader - Progressive Data Loading for Large Datasets
 * Handles loading of large datasets in chunks to prevent memory issues
 * and maintain UI responsiveness during data generation and loading.
 * 
 * Features:
 * - Progressive data generation and loading
 * - Configurable chunk sizes
 * - Loading progress tracking
 * - Cancellation support
 * - Memory-efficient data management
 * - Event-driven architecture
 * 
 * @version 1.0.0
 * <AUTHOR> Dashboard Team
 */

class ChunkedDataLoader {
    constructor(options = {}) {
        this.options = {
            chunkSize: options.chunkSize || 10000, // Records per chunk
            delayBetweenChunks: options.delayBetweenChunks || 16, // ms (60fps)
            maxMemoryChunks: options.maxMemoryChunks || 5, // Keep only N chunks in memory
            ...options
        };
        
        this.isLoading = false;
        this.isCancelled = false;
        this.currentChunk = 0;
        this.totalChunks = 0;
        this.loadedData = [];
        this.dataGenerator = null;
        this.loadingCallbacks = {
            onProgress: null,
            onChunkLoaded: null,
            onComplete: null,
            onError: null,
            onCancel: null
        };
    }

    /**
     * Load data in chunks
     * @param {number} totalRecords - Total number of records to generate
     * @param {Function} dataGenerator - Function to generate data (count, offset) => Array
     * @param {Object} callbacks - Event callbacks
     * @returns {Promise} Loading promise
     */
    async loadData(totalRecords, dataGenerator, callbacks = {}) {
        if (this.isLoading) {
            throw new Error('ChunkedDataLoader: Already loading data');
        }

        this.isLoading = true;
        this.isCancelled = false;
        this.currentChunk = 0;
        this.totalChunks = Math.ceil(totalRecords / this.options.chunkSize);
        this.loadedData = [];
        this.dataGenerator = dataGenerator;
        
        // Set up callbacks
        Object.assign(this.loadingCallbacks, callbacks);

        try {
            // Notify start
            this.loadingCallbacks.onProgress?.(0, totalRecords, 0, this.totalChunks);

            // Load chunks progressively
            for (let chunkIndex = 0; chunkIndex < this.totalChunks; chunkIndex++) {
                if (this.isCancelled) {
                    this.loadingCallbacks.onCancel?.(this.loadedData);
                    return this.loadedData;
                }

                const chunkStart = chunkIndex * this.options.chunkSize;
                const chunkSize = Math.min(this.options.chunkSize, totalRecords - chunkStart);
                
                // Generate chunk data
                const chunkData = this.dataGenerator(chunkSize, chunkStart);
                this.loadedData.push(...chunkData);

                // Memory management - keep only recent chunks in memory
                this.manageMemory();
                
                // Debug logging for large datasets
                if (totalRecords >= 100000) {
                    console.log(`Chunk ${chunkIndex + 1}/${this.totalChunks}: Generated ${chunkData.length} records, Total loaded: ${this.loadedData.length}/${totalRecords}`);
                }

                this.currentChunk = chunkIndex + 1;
                const progress = Math.min(100, (this.currentChunk / this.totalChunks) * 100);
                const loadedRecords = Math.min(totalRecords, this.loadedData.length);

                // Notify progress
                this.loadingCallbacks.onProgress?.(progress, totalRecords, loadedRecords, this.currentChunk, this.totalChunks);
                this.loadingCallbacks.onChunkLoaded?.(chunkData, chunkIndex, this.loadedData);

                // Delay between chunks to maintain UI responsiveness
                if (chunkIndex < this.totalChunks - 1) {
                    await this.delay(this.options.delayBetweenChunks);
                }
            }

            // Verify all data was loaded
            if (this.loadedData.length !== totalRecords) {
                console.warn(`Data loading mismatch: Expected ${totalRecords} records, but loaded ${this.loadedData.length} records`);
            }
            
            // Notify completion
            this.loadingCallbacks.onComplete?.(this.loadedData);
            return this.loadedData;

        } catch (error) {
            this.loadingCallbacks.onError?.(error);
            throw error;
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Cancel current loading operation
     */
    cancel() {
        this.isCancelled = true;
    }

    /**
     * Get current loading state
     */
    getLoadingState() {
        return {
            isLoading: this.isLoading,
            isCancelled: this.isCancelled,
            currentChunk: this.currentChunk,
            totalChunks: this.totalChunks,
            loadedRecords: this.loadedData.length,
            progress: this.isLoading ? (this.currentChunk / this.totalChunks) * 100 : 100
        };
    }

    /**
     * Get loaded data
     */
    getLoadedData() {
        return [...this.loadedData];
    }

    /**
     * Clear loaded data
     */
    clear() {
        this.loadedData = [];
        this.currentChunk = 0;
        this.totalChunks = 0;
    }

    /**
     * Memory management - keep only recent chunks
     * @private
     */
    manageMemory() {
        // Only apply memory management if explicitly enabled and not loading large datasets
        if (this.options.enableMemoryManagement && 
            this.loadedData.length > this.options.maxMemoryChunks * this.options.chunkSize) {
            // Remove oldest chunks to free memory
            const chunksToKeep = this.options.maxMemoryChunks * this.options.chunkSize;
            this.loadedData = this.loadedData.slice(-chunksToKeep);
        }
    }

    /**
     * Delay utility
     * @private
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Create a data generator function for SnapGrid
     * @param {Object} options - Generator options
     * @returns {Function} Data generator function
     */
    static createDataGenerator(options = {}) {
        const {
            seed = 1234,
            marketplaces = ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'],
            productTypes = ['T-Shirt', 'Hoodie', 'Tank Top', 'Long Sleeve', 'V-Neck'],
            brands = ['Snap Brand', 'Creative Co', 'Design Studio', 'Art House', 'Style Co'],
            statuses = ['Active', 'Inactive', 'Pending', 'Draft']
        } = options;

        // Set up seeded random
        if (typeof setSeed === 'function') {
            setSeed(seed);
        }

        return (count, offset) => {
            const data = [];
            for (let i = 0; i < count; i++) {
                const index = offset + i;
                const marketplace = marketplaces[index % marketplaces.length];
                const productType = productTypes[index % productTypes.length];
                const brand = brands[index % brands.length];
                const status = statuses[index % statuses.length];
                
                data.push({
                    marketplace,
                    asin: `B${String(index).padStart(9, '0')}`,
                    status,
                    productType,
                    brand,
                    title: `${brand} ${productType} - Design ${index + 1}`,
                    price: parseFloat((Math.random() * 50 + 10).toFixed(2)),
                    sales: Math.floor(Math.random() * 1000) + 1,
                    returns: Math.floor(Math.random() * 50),
                    returnRate: parseFloat((Math.random() * 10).toFixed(1)),
                    royalties: parseFloat((Math.random() * 20 + 5).toFixed(2)),
                    firstSold: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    lastSold: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    bsr: Math.floor(Math.random() * 100000) + 1,
                    firstPublished: new Date(Date.now() - Math.random() * 730 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    lastUpdated: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    reviews: Math.floor(Math.random() * 500) + 1,
                    designId: `DESIGN-${String(index).padStart(6, '0')}`
                });
            }
            return data;
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChunkedDataLoader;
}
