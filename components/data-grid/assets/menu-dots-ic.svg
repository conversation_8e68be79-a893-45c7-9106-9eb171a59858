<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 6.9 32">
  <defs>
    <style>
      .cls-1 {
        fill: #606f95;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <circle class="cls-1" cx="3.45" cy="28.55" r="3.45"/>
      <circle class="cls-1" cx="3.45" cy="16" r="3.45"/>
      <circle class="cls-1" cx="3.45" cy="3.45" r="3.45"/>
    </g>
  </g>
</svg>