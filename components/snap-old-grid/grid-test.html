<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SnapGrid Test Suite</title>
    
    <!-- Load existing project fonts and CSS variables -->
    <link rel="stylesheet" href="../../snapapp.css">
    
    <!-- Load grid-specific CSS -->
    <link rel="stylesheet" href="snap-grid.css">
    <link rel="stylesheet" href="../datepicker/snap-datepicker.css">
    
    <style>
        /* Test page specific styles */
        body {
            margin: 0;
            padding: 40px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-family: 'Amazon Ember', Arial, sans-serif;
            min-height: 100vh;
            /* Override main app CSS that prevents scrolling */
            overflow-y: auto !important;
            overflow-x: auto !important;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 32px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .test-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-accent);
            margin: 0 0 8px 0;
        }
        
        .test-subtitle {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }
        
        .controls-section {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-bottom: 32px;
        }
        
        .control-btn {
            padding: 8px 16px;
            border: 1.5px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Amazon Ember', sans-serif;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--theme-transition);
        }
        
        .control-btn:hover {
            background: var(--btn-hover);
            border-color: var(--action-btn-bg);
        }
        
        .control-btn.active {
            background: var(--action-btn-bg);
            color: var(--action-btn-text);
            border-color: var(--action-btn-bg);
        }
        
        .grid-container {
            width: 100%;
            max-width: 1064px;
            margin: 0 auto;
            height: 500px;
            margin-bottom: 32px;
        }
        
        .demo-info {
            background: var(--bg-primary);
            border: 1.5px solid var(--border-color);
            border-radius: 14px;
            padding: 24px;
            margin-top: 32px;
        }
        
        .demo-info h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 700;
            color: var(--text-accent);
        }
        
        .demo-info p {
            margin: 0 0 12px 0;
            font-size: 14px;
            line-height: 1.5;
            color: var(--text-primary);
        }
        
        .demo-info ul {
            margin: 12px 0;
            padding-left: 20px;
        }
        
        .demo-info li {
            margin: 8px 0;
            font-size: 14px;
            color: var(--text-primary);
        }
        
        .code-example {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            overflow-x: auto;
        }
        
        .data-editor {
            background: var(--bg-primary);
            border: 1.5px solid var(--border-color);
            border-radius: 14px;
            padding: 24px;
            margin-bottom: 32px;
        }
        
        .data-editor h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 700;
            color: var(--text-accent);
        }
        
        .data-editor-controls {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .data-editor-controls .control-btn {
            margin: 0;
        }
        
        .performance-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
            margin-top: 12px;
        }
        
        .stat-item {
            text-align: center;
            padding: 8px;
            background: var(--bg-secondary);
            border-radius: 6px;
            border: 1px solid var(--border-color);
        }
        
        .stat-value {
            font-weight: 600;
            color: var(--action-btn-bg);
            font-size: 16px;
        }
        
        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 20px;
            }
            
            .controls-section {
                flex-wrap: wrap;
            }
            
            .test-title {
                font-size: 24px;
            }
            
            .grid-container {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Header -->
        <div class="test-header">
            <h1 class="test-title">SnapGrid - Data Grid Test</h1>
            <p class="test-subtitle">Interactive data grid system with theme support, sorting, filtering, and column management</p>
        </div>
        
        <!-- Controls -->
        <div class="controls-section">
            <button class="control-btn active" id="lightTheme">Light Theme</button>
            <button class="control-btn" id="darkTheme">Dark Theme</button>
            <button class="control-btn" id="updateData">Update Data</button>
            <button class="control-btn" id="toggleVirtualScrolling">Toggle Virtual Scrolling</button>
            <button class="control-btn" id="exportData">Export CSV</button>
            <button class="control-btn" id="startLoadingSimulation" style="background: #007bff; color: white;">Start Loading Simulation</button>
            <button class="control-btn" id="stopLoadingSimulation" style="background: #dc3545; color: white;">Stop Loading Simulation</button>
        </div>
        
        <!-- Grid Container -->
        <div class="grid-container" id="gridContainer">
            <h2 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 700; color: var(--text-accent);">Enhanced Data Grid with All Fields</h2>
        </div>
        
        <!-- Data Editor Section -->
        <div class="data-editor">
            <h3>🎛️ Data Editor & Controls</h3>
            <p>Use the controls below to test different grid configurations and data sets.</p>
            
            <div class="data-editor-controls">
                <button class="control-btn" onclick="refreshBasicGrid()">Refresh Data</button>
                <button class="control-btn" onclick="selectAllBasic()">Select All</button>
                <button class="control-btn" onclick="exportBasicGrid()">Export All CSV</button>
                <button class="control-btn" onclick="exportSelectedBasic()">Export Selected CSV</button>
                <button class="control-btn" onclick="clearBasicSelection()">Clear Selection</button>
                <button class="control-btn" onclick="generateSmallDataset()">Small Dataset (100 rows)</button>
                <button class="control-btn" onclick="generateLargeDataset()">Large Dataset (10,000 rows)</button>
            </div>
            
            <div id="basic-info" class="performance-stats"></div>
        </div>

        <!-- Demo Information -->
        <div class="demo-info">
            <h3>🎯 Features Demonstrated</h3>
            <p>This data grid includes advanced features inspired by AG Grid with custom styling and performance optimizations:</p>
            <ul>
                <li><strong>17 Data Fields:</strong> Complete product data including Marketplace, ASIN, Product Type, Status, Brand, Product Title, Price, Sales, Returns, Royalties, First Sold, Last Sold, BSR, First Published Date, Last Updated Date, Reviews, and Design ID</li>
                <li><strong>Draggable Column Reordering:</strong> Drag columns by grip handles in Show/Hide Columns tab</li>
                <li><strong>Advanced Sorting:</strong> Sort Ascending/Descending with proper data arrangement</li>
                <li><strong>Column Management:</strong> Show/Hide columns with persistent menu behavior</li>
                <li><strong>Row Selection:</strong> Individual and bulk selection with checkbox controls</li>
                <li><strong>Data Export:</strong> Export all data or selected rows to CSV format</li>
                <li><strong>Theme Integration:</strong> Automatically adapts to light/dark themes using CSS variables</li>
                <li><strong>Performance Monitoring:</strong> Real-time performance statistics and render times</li>
                <li><strong>Virtual Scrolling:</strong> Optional virtual scrolling for large datasets</li>
                <li><strong>Responsive Design:</strong> Adapts to different screen sizes while maintaining functionality</li>
            </ul>
            
            <h3>📊 Grid Components</h3>
            <ul>
                <li><strong>Header Row:</strong> Sortable column headers with dropdown menus for advanced actions</li>
                <li><strong>Data Rows:</strong> Product information with formatted values (currency, dates, percentages)</li>
                <li><strong>Action Buttons:</strong> Preview, Edit, and Analyse buttons for each product row</li>
                <li><strong>Selection Controls:</strong> Checkbox column for individual and bulk selection</li>
                <li><strong>Column Menus:</strong> Right-click context menus for sorting, filtering, and column management</li>
                <li><strong>Performance Stats:</strong> Real-time display of row count, selection count, and render times</li>
                <li><strong>Export Functions:</strong> CSV export with options for all data or selected rows only</li>
            </ul>
            
            <h3>🔧 Usage Example</h3>
            <div class="code-example">
const grid = new SnapGrid('gridContainer', {
    columns: [
        { field: 'marketplace', headerName: 'Marketplace', width: 120, sortable: true },
        { field: 'asin', headerName: 'ASIN', width: 120, sortable: true },
        { field: 'title', headerName: 'Product Title', width: 250, sortable: true },
        { field: 'price', headerName: 'Price', width: 80, type: 'currency', sortable: true },
        { field: 'sales', headerName: 'Sales', width: 80, type: 'number', sortable: true }
    ],
    data: productData,
    sortable: true,
    filterable: true,
    selectable: true,
    virtualScrolling: false
});
            </div>
        </div>
    </div>

    <!-- JavaScript Dependencies -->
    <script src="dummy-grid-data.js?v=dev-20250830"></script>
    <script src="snap-grid.js?v=dev-20250830"></script>

    <!-- Datepicker Integration -->
    <script src="../datepicker/snap-datepicker.js"></script>
    <script src="../datepicker/datepicker-integration.js"></script>

    <script>
        // Intercept clicks on calendar icon to open custom datepicker
        function interceptCalendarIconClicks() {
            document.addEventListener('click', function(e) {
                // Check if click is on a date input within snap-grid
                const dateInput = e.target;
                if (dateInput.type === 'date' && dateInput.closest('.snap-grid')) {
                    // Only intercept if click is on the calendar icon area (right side of input)
                    const rect = dateInput.getBoundingClientRect();
                    const clickX = e.clientX;
                    const iconAreaStart = rect.right - 30; // Calendar icon is usually in last 30px

                    if (clickX >= iconAreaStart) {
                        e.preventDefault();
                        e.stopPropagation();

                        // Create and show custom datepicker
                        openCustomDatepicker(dateInput, e);
                    }
                }
            }, true);
        }

        function openCustomDatepicker(dateInput, event) {
            // Remove any existing datepicker
            const existingPicker = document.querySelector('.snap-datepicker-popup');
            if (existingPicker) {
                existingPicker.remove();
            }

            // Create wrapper for the datepicker
            const wrapper = document.createElement('div');
            wrapper.className = 'snap-datepicker-popup';
            wrapper.style.position = 'absolute';
            wrapper.style.zIndex = '9999'; // Lower than menu z-index

            // Position wrapper beneath the specific input that triggered it
            const rect = dateInput.getBoundingClientRect();
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

            wrapper.style.left = (rect.left + scrollLeft) + 'px';
            wrapper.style.top = (rect.bottom + scrollTop + 8) + 'px';
            wrapper.style.width = 'auto';

            document.body.appendChild(wrapper);

            // Create the datepicker with calendar-only mode
            const datepicker = new SnapDatepicker(wrapper, {
                value: dateInput.value,
                placeholder: dateInput.placeholder || 'Select date',
                closeOnSelect: true
            });

            // Hide the input part and calendar icon, show only the calendar
            setTimeout(() => {
                // Hide the input field
                const pickerInput = wrapper.querySelector('.snap-datepicker-input');
                if (pickerInput) {
                    pickerInput.style.display = 'none';
                }

                // Hide the calendar icon
                const pickerIcon = wrapper.querySelector('.snap-datepicker-icon');
                if (pickerIcon) {
                    pickerIcon.style.display = 'none';
                }

                // Show only the calendar dropdown
                const pickerDropdown = wrapper.querySelector('.snap-datepicker-dropdown');
                if (pickerDropdown) {
                    pickerDropdown.style.position = 'static';
                    pickerDropdown.style.display = 'block';
                    pickerDropdown.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                    pickerDropdown.style.border = '1px solid var(--border-color)';
                    pickerDropdown.style.borderRadius = '8px';
                }
            }, 10);

            // Stop all click events from bubbling out of the picker
            wrapper.addEventListener('click', function(e) {
                e.stopPropagation();
            });

            // Sync values back to the native input
            datepicker.on('change', (value) => {
                dateInput.value = value;

                // Trigger both input and change events to ensure grid filtering
                const inputEvent = new Event('input', { bubbles: true });
                const changeEvent = new Event('change', { bubbles: true });

                dateInput.dispatchEvent(inputEvent);
                dateInput.dispatchEvent(changeEvent);

                // Also trigger any custom grid filter update
                setTimeout(() => {
                    // Find the grid instance and trigger filter update
                    const gridContainer = dateInput.closest('.snap-grid');
                    if (gridContainer && gridContainer._snapGrid) {
                        // Force grid to reprocess filters
                        gridContainer._snapGrid.processData();
                        gridContainer._snapGrid.render();
                    }
                }, 10);

                // Close only the picker
                wrapper.remove();
            });

            // Close on outside click (but not on menu clicks)
            setTimeout(() => {
                document.addEventListener('click', function closeOnOutside(e) {
                    if (!wrapper.contains(e.target) && !e.target.closest('.snap-grid-column-menu')) {
                        wrapper.remove();
                        document.removeEventListener('click', closeOnOutside);
                    }
                });
            }, 100);

            // Force open the calendar
            datepicker.open();
        }

        // Start intercepting clicks when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            interceptCalendarIconClicks();
        });
    </script>

    <script>
        // Global grid instance
        let basicGrid;
        let currentDataset = 'small';
        let virtualScrollingEnabled = false;
        let isLoadingSimulation = false;

        // Initialize the test grid
        document.addEventListener('DOMContentLoaded', function() {
            initializeBasicGrid();
            setupThemeControls();
            setupDataControls();
        });
        
        // Theme management
        function setupThemeControls() {
            document.getElementById('lightTheme').addEventListener('click', function() {
                document.documentElement.setAttribute('data-theme', 'light');
                document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
            });
            
            document.getElementById('darkTheme').addEventListener('click', function() {
                document.documentElement.setAttribute('data-theme', 'dark');
                document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
            });
        }
        
        // Data controls
        function setupDataControls() {
            document.getElementById('updateData').addEventListener('click', function() {
                refreshBasicGrid();
            });
            
            document.getElementById('toggleVirtualScrolling').addEventListener('click', function() {
                virtualScrollingEnabled = !virtualScrollingEnabled;
                this.textContent = virtualScrollingEnabled ? 'Disable Virtual Scrolling' : 'Enable Virtual Scrolling';
                this.classList.toggle('active', virtualScrollingEnabled);
                
                // Reinitialize grid with new virtual scrolling setting
                if (basicGrid) {
                    const currentData = basicGrid.state?.displayData || [];
                    basicGrid.destroy();
                    initializeBasicGrid();
                    if (currentData.length > 0) {
                        basicGrid.updateData(currentData);
                    }
                }
            });
            
            document.getElementById('exportData').addEventListener('click', function() {
                exportBasicGrid();
            });
            
            document.getElementById('startLoadingSimulation').addEventListener('click', function() {
                startLoadingSimulation();
            });
            
            document.getElementById('stopLoadingSimulation').addEventListener('click', function() {
                stopLoadingSimulation();
            });
        }
        
        // Performance monitoring
        function updateInfo(elementId, grid) {
            const element = document.getElementById(elementId);
            if (!element || !grid) return;
            
            const stats = grid.performance || {};
            const dataLength = grid.state?.displayData?.length || 0;
            const selectedCount = grid.state?.selectedRowKeys?.size || 0;
            
            element.innerHTML = `
                <div class="stat-item">
                    <div class="stat-value">${dataLength.toLocaleString()}</div>
                    <div class="stat-label">Total Rows</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${selectedCount}</div>
                    <div class="stat-label">Selected</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${(stats.renderTime || 0).toFixed(1)}ms</div>
                    <div class="stat-label">Render Time</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${(stats.filterTime || 0).toFixed(1)}ms</div>
                    <div class="stat-label">Filter Time</div>
                </div>
            `;
        }
        
        // Basic Grid Functions
        function initializeBasicGrid() {
            const columns = [
                { field: 'checkbox', headerName: '', width: 50, sortable: false, resizable: false },
                { field: 'preview', headerName: 'Preview', width: 80, sortable: false },
                { field: 'marketplace', headerName: 'Marketplace', width: 120, sortable: true },
                { field: 'asin', headerName: 'ASIN', width: 120, sortable: true },
                { field: 'productType', headerName: 'Product Type', width: 120, sortable: true },
                { field: 'status', headerName: 'Status', width: 100, sortable: true },
                { field: 'brand', headerName: 'Brand', width: 130, sortable: true },
                { field: 'title', headerName: 'Product Title', width: 250, sortable: true },
                { field: 'price', headerName: 'Price', width: 80, type: 'currency', sortable: true },
                { field: 'sales', headerName: 'Sales', width: 80, type: 'number', sortable: true },
                { field: 'returns', headerName: 'Returns', width: 80, type: 'number', sortable: true },
                { field: 'returnRate', headerName: 'Return Rate', width: 100, type: 'percentage', sortable: true },
                { field: 'royalties', headerName: 'Royalties', width: 100, type: 'currency', sortable: true },
                { field: 'firstSold', headerName: 'First Sold', width: 120, type: 'date', sortable: true },
                { field: 'lastSold', headerName: 'Last Sold', width: 120, type: 'date', sortable: true },
                { field: 'bsr', headerName: 'BSR', width: 100, type: 'number', sortable: true },
                { field: 'firstPublished', headerName: 'First Published Date', width: 150, type: 'date', sortable: true },
                { field: 'lastUpdated', headerName: 'Last Updated Date', width: 150, type: 'date', sortable: true },
                { field: 'reviews', headerName: 'Reviews', width: 80, type: 'number', sortable: true },
                { field: 'designId', headerName: 'Design ID', width: 120, sortable: true },
                { field: 'actions', headerName: 'Actions', width: 80, sortable: false, resizable: false }
            ];

            // Start with only 10 rows for loading simulation
            const initialData = generateProductData(10);

            basicGrid = new SnapGrid('gridContainer', {
                columns: columns,
                data: initialData,
                sortable: true,
                filterable: true,
                selectable: true,
                virtualScrolling: virtualScrollingEnabled,
                performance: true
            });

            // Set total products count for loading simulation
            basicGrid.state.totalProducts = 1000;

            basicGrid.on('rendered', () => updateInfo('basic-info', basicGrid));
            basicGrid.on('selectionChanged', () => updateInfo('basic-info', basicGrid));

            // Add event listeners for new column actions
            basicGrid.on('previewClicked', (event) => {
                console.log('Preview clicked:', event);
                alert(`Preview clicked for row ${event.rowIndex}: ${event.data.title}`);
            });

            basicGrid.on('editClicked', (event) => {
                console.log('Edit clicked:', event);
                alert(`Edit clicked for row ${event.rowIndex}: ${event.data.title}`);
            });

            basicGrid.on('analyseClicked', (event) => {
                console.log('Analyse clicked:', event);
                alert(`Analyse clicked for row ${event.rowIndex}: ${event.data.title}`);
            });
        }
        
        // Dataset generation functions
        function generateSmallDataset() {
            currentDataset = 'small';
            if (basicGrid) {
                // Stop any running simulation
                if (isLoadingSimulation) {
                    stopLoadingSimulation();
                }
                basicGrid.updateData(generateProductData(100));
                basicGrid.state.totalProducts = 100;
                updateInfo('basic-info', basicGrid);
            }
        }
        
        function generateLargeDataset() {
            currentDataset = 'large';
            if (basicGrid) {
                // Stop any running simulation
                if (isLoadingSimulation) {
                    stopLoadingSimulation();
                }
                basicGrid.updateData(generateProductData(10000));
                basicGrid.state.totalProducts = 10000;
                updateInfo('basic-info', basicGrid);
            }
        }

        function refreshBasicGrid() {
            if (basicGrid) {
                // Stop any running simulation
                if (isLoadingSimulation) {
                    stopLoadingSimulation();
                }
                const dataSize = currentDataset === 'small' ? 100 : 10000;
                basicGrid.updateData(generateProductData(dataSize));
                basicGrid.state.totalProducts = dataSize;
                updateInfo('basic-info', basicGrid);
            }
        }

        function selectAllBasic() {
            if (basicGrid) {
                basicGrid.selectAll();
            }
        }

        function exportBasicGrid() {
            if (basicGrid) {
                basicGrid.exportCSV({
                    selectedOnly: false,
                    visibleOnly: true,
                    filename: 'basic-grid-export.csv'
                });
            }
        }

        function exportSelectedBasic() {
            if (basicGrid) {
                basicGrid.exportCSV({
                    selectedOnly: true,
                    visibleOnly: true,
                    filename: 'basic-grid-selected.csv'
                });
            }
        }

        function clearBasicSelection() {
            if (basicGrid) {
                basicGrid.clearSelection();
            }
        }

        // Loading simulation functions
        function startLoadingSimulation() {
            if (!basicGrid || isLoadingSimulation) return;
            
            console.log('🚀 Starting loading simulation...');
            isLoadingSimulation = true;
            
            // Start the grid's loading simulation
            basicGrid.startLoadingSimulation();
            
            // Update button states
            document.getElementById('startLoadingSimulation').disabled = true;
            document.getElementById('stopLoadingSimulation').disabled = false;
        }

        function stopLoadingSimulation() {
            if (!basicGrid || !isLoadingSimulation) return;
            
            console.log('🛑 Stopping loading simulation...');
            isLoadingSimulation = false;
            
            // Stop the grid's loading simulation
            basicGrid.stopLoadingSimulation();
            
            // Update button states
            document.getElementById('startLoadingSimulation').disabled = false;
            document.getElementById('stopLoadingSimulation').disabled = true;
        }


        // Error handling
        window.addEventListener('error', function(e) {
            const msg = (e && e.error && e.error.message) ? e.error.message : (e && e.message) ? e.message : String(e.error || e);
            console.error('Test error:', e.error || e);
            alert('Test error: ' + msg);
        });
        
        // Apply initial theme
        document.documentElement.setAttribute('data-theme', 'light');
    </script>
</body>
</html>


