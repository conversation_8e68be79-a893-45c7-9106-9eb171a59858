/**
 * SnapGrid - Advanced Data Grid Engine
 * A comprehensive client-side data grid
 * Following the single-file architecture pattern of snap-charts.js
 *
 * Features:
 * - Virtual scrolling for performance with large datasets
 * - Column menus with sorting, filtering, grouping
 * - Client-side data operations
 * - Cell editing and custom renderers
 * - Performance monitoring integration
 *
 * @version 1.0.0
 * <AUTHOR> Dashboard Team
 */

class SnapGrid {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        if (!this.container) {
            throw new Error('SnapGrid: Container element not found');
        }

        // Store grid instance on the container element for easy access
        this.container._snapGrid = this;

        // Configuration with defaults
        this.config = {
            columns: options.columns || [],
            data: options.data || [],
            height: options.height || '400px',
            rowHeight: options.rowHeight || 40,
            headerHeight: options.headerHeight || 48,
            virtualScrolling: options.virtualScrolling !== false,
            virtualizeColumns: options.virtualizeColumns || false, // Column virtualization for horizontal performance
            pagination: options.pagination || false,
            pageSize: options.pageSize || 100,
            sortable: options.sortable !== false,
            filterable: options.filterable !== false,
            groupable: options.groupable || false,
            selectable: options.selectable || false,
            editable: options.editable || false,
            resizable: options.resizable !== false,
            theme: options.theme || 'default',
            performance: options.performance !== false,
            rowIdField: options.rowIdField || 'id', // Default field for stable row IDs
            getRowId: options.getRowId || null, // Optional function to get row ID
            allowUnsafeHtml: options.allowUnsafeHtml || false, // Allow unsafe HTML in custom renderers
            sanitizeHTML: options.sanitizeHTML !== false, // Sanitize HTML by default
            smoothScrolling: options.smoothScrolling !== false, // Enable smooth scrolling by default
            ...options
        };

        // Generate unique instance ID
        this.instanceId = 'snap-grid-' + Math.random().toString(36).substr(2, 9);

        // Internal state
        this.state = {
            data: [],
            filteredData: [],
            sortedData: [],
            displayData: [],
            selectedRowKeys: new Set(), // Changed from selectedRows to use stable keys
            rowKeyToIndexMap: new Map(), // Map from row keys to current indices
            sortConfig: [],
            filterConfig: {},
            groupConfig: null,
            expandedGroups: new Set(),
            pageIndex: 0,
            editingCell: null,
            scrollTop: 0,
            scrollLeft: 0,
            visibleRange: { start: 0, end: 0 },
            columnWidths: new Map(),
            columnOrder: [],
            hiddenColumns: new Set(),
            pinnedColumns: { left: [], right: [] },
            columnVisibility: new Map()
        };

        // Performance monitoring
        this.performance = {
            renderTime: 0,
            scrollTime: 0,
            filterTime: 0,
            sortTime: 0,
            memoryUsage: 0
        };

        // Measurement cache for autosize performance
        this.measurementCache = new Map();

        // Event handlers
        this.eventHandlers = new Map();
        this.boundHandlers = new Map();
        
        // Row hover coordination
        this.hoveredRowIndex = null;

        // Dialog/menu references for cleanup
        this.activeDialogs = new Set();

        // Active menu tracking for scroll repositioning
        this.activeMenu = null;
        this.activeMenuTarget = null;

        // Filter debouncing
        this.filterDebounceTimer = null;
        this.filterDebounceDelay = 300; // 300ms delay

        // Centralized checkbox state management
        this.checkboxStates = new Map(); // field -> Set of checked values

        // Disable pagination if virtual scrolling is enabled
        if (this.config.virtualScrolling && this.config.pagination) {
            console.warn('SnapGrid: Pagination disabled when virtual scrolling is enabled');
            this.config.pagination = false;
        }

        // Initialize grid
        this.init();
        
        // Expose loading simulation methods for development testing
        this.startLoadingSimulation = this.startLoadingSimulation.bind(this);
        this.stopLoadingSimulation = this.stopLoadingSimulation.bind(this);
    }

    /**
     * Initialize the grid
     */
    init() {
        try {
            this.validateConfig();
            this.setupContainer();
            this.processColumns();
            this.processData();
            this.createGridStructure();
            this.setupVirtualScrolling();
            this.setupEventListeners();
            this.setupPerformanceMonitoring();
            this.render();
            
            // Restore saved layout selection after grid is fully initialized
            setTimeout(() => {
                this.restoreSavedLayoutSelection();
            }, 200);
            
            this.emit('gridReady', { grid: this });
        } catch (error) {
            console.error('SnapGrid initialization failed:', error);
            this.showError('Failed to initialize grid: ' + error.message);
        }
    }

    /**
     * Validate configuration
     */
    validateConfig() {
        if (!Array.isArray(this.config.columns) || this.config.columns.length === 0) {
            throw new Error('Columns configuration is required and must be a non-empty array');
        }

        if (!Array.isArray(this.config.data)) {
            throw new Error('Data must be an array');
        }

        // Validate column definitions
        this.config.columns.forEach((col, index) => {
            if (!col.field && !col.valueGetter) {
                throw new Error(`Column at index ${index} must have either 'field' or 'valueGetter' property`);
            }
        });
    }

    /**
     * Setup container element
     */
    setupContainer() {
        // Preserve existing classes and add grid classes
        this.container.classList.add('snap-grid', this.config.theme);
        this.container.style.height = this.config.height;
        this.container.style.position = 'relative';
        this.container.style.overflow = 'hidden';

        // Add CSS containment for performance
        this.container.style.contain = 'layout style paint';

        // Apply smooth scrolling if enabled
        if (this.config.smoothScrolling && this.elements?.viewport) {
            this.elements.viewport.classList.add('smooth-scroll');
        }
    }

    /**
     * Process column definitions
     */
    processColumns() {
        this.state.columnOrder = this.config.columns.map((col, index) => col.field || `col_${index}`);

        // Set default column widths with minimum width based on header text
        this.config.columns.forEach((col, index) => {
            const field = col.field || `col_${index}`;
            
            // Special handling for Actions column - set width to fit exactly the two icons
            if (field === 'actions') {
                // Calculate width needed for 2 icons (28px each) + gap (8px) + cell padding (32px total for more breathing room)
                const actionsWidth = 28 + 8 + 28 + 32; // 96px
                this.state.columnWidths.set(field, actionsWidth);
                return;
            }
            
            const minWidth = this.calculateMinHeaderWidth(col);
            const configWidth = col.width || 150;
            const width = Math.max(minWidth, configWidth);
            this.state.columnWidths.set(field, width);
        });

        // Set up default pinning for special columns
        this.setupDefaultPinning();
    }

    /**
     * Calculate minimum width needed for header text
     */
    calculateMinHeaderWidth(column) {
        // Special handling for checkbox column - just enough for checkbox + padding
        if (column.field === 'checkbox') {
            return 46; // 16px checkbox + 15px padding each side
        }

        // Special handling for preview column - fixed square size
        if (column.field === 'preview') {
            return 60; // 32px square + 14px padding each side
        }

        // Create a temporary element to measure text width
        const tempElement = document.createElement('div');
        tempElement.style.position = 'absolute';
        tempElement.style.visibility = 'hidden';
        tempElement.style.whiteSpace = 'nowrap';
        tempElement.style.fontFamily = 'Amazon Ember, Arial, sans-serif';
        tempElement.style.fontSize = '14px';
        tempElement.style.fontWeight = '500'; // Medium weight for headers
        tempElement.style.padding = '0 12px'; // Account for cell padding

        // Set the header text
        const headerText = column.headerName || column.field || '';
        tempElement.textContent = headerText;

        // Add to DOM to measure
        document.body.appendChild(tempElement);
        const textWidth = tempElement.offsetWidth;
        document.body.removeChild(tempElement);

        // Add extra space for sort indicators, resize handles, etc.
        const extraSpace = 40; // Space for sort icon + resize handle + margins
        const minWidth = textWidth + extraSpace;

        // Set reasonable bounds
        const absoluteMin = 80; // Never go below this
        const absoluteMax = 300; // Don't auto-expand beyond this

        return Math.max(absoluteMin, Math.min(minWidth, absoluteMax));
    }

    /**
     * Recalculate and enforce minimum widths for all columns
     */
    enforceMinimumWidths() {
        this.config.columns.forEach(column => {
            const field = column.field;
            
            // Skip Actions column - it has a fixed width
            if (field === 'actions') {
                return;
            }
            
            const currentWidth = this.state.columnWidths.get(field) || 150;
            const minWidth = this.calculateMinHeaderWidth(column);

            if (currentWidth < minWidth) {
                this.setColumnWidth(field, minWidth);
            }
        });
    }

    /**
     * Setup default column pinning
     */
    setupDefaultPinning() {
        // Pin checkbox and preview to left
        const leftPinColumns = ['checkbox', 'preview'];
        leftPinColumns.forEach(field => {
            if (this.config.columns.find(col => col.field === field)) {
                if (!this.state.pinnedColumns.left.includes(field)) {
                    this.state.pinnedColumns.left.push(field);
                }
            }
        });

        // Pin actions to right
        const rightPinColumns = ['actions'];
        rightPinColumns.forEach(field => {
            if (this.config.columns.find(col => col.field === field)) {
                if (!this.state.pinnedColumns.right.includes(field)) {
                    this.state.pinnedColumns.right.push(field);
                }
            }
        });
    }

    /**
     * Process and prepare data
     */
    processData() {
        const startTime = performance.now();

        this.state.data = [...this.config.data];
        this.applyFilters();
        this.applySorting();
        this.applyGrouping();

        this.performance.filterTime = performance.now() - startTime;
    }

    /**
     * Create grid DOM structure
     */
    createGridStructure() {
        this.container.innerHTML = `
            <div class="snap-grid-controls-header">
                <div class="snap-grid-controls-left">
                    <div class="snap-grid-filters-dropdown"></div>
                    <div class="snap-grid-layout-dropdown"></div>
                    <button class="snap-grid-clear-filters-btn">Clear Filters</button>
                </div>
                <div class="snap-grid-controls-center">
                    <button class="snap-grid-delete-btn" title="Delete Selected">
                        <img src="./assets/delete-ic.svg" alt="Delete" />
                    </button>
                    <button class="snap-grid-export-btn" title="Export Data">
                        <img src="./assets/export-ic.svg" alt="Export" />
                    </button>
                </div>
                <div class="snap-grid-controls-right">
                    <div class="snap-grid-loaded-info">0 / 1,167,765</div>
                </div>
            </div>
            <div class="snap-grid-header" role="rowgroup">
                <div class="snap-grid-header-pinned-left"></div>
                <div class="snap-grid-header-viewport">
                    <div class="snap-grid-header-row" role="row"></div>
                </div>
                <div class="snap-grid-header-pinned-right"></div>
            </div>
            <div class="snap-grid-body" role="rowgroup">
                <div class="snap-grid-pinned-left"></div>
                <div class="snap-grid-viewport">
                    <div class="snap-grid-canvas"></div>
                </div>
                <div class="snap-grid-pinned-right"></div>
            </div>
            <div class="snap-grid-footer" role="contentinfo">
                <div class="snap-grid-footer-stats">
                    <div class="snap-grid-stat-item">
                        <div class="snap-grid-stat-label">ROWS</div>
                        <div class="snap-grid-stat-value">1,186,236</div>
                    </div>
                    <div class="snap-grid-stat-item">
                        <div class="snap-grid-stat-label">FILTERED</div>
                        <div class="snap-grid-stat-value">726</div>
                    </div>
                    <div class="snap-grid-stat-item">
                        <div class="snap-grid-stat-label">SELECTED</div>
                        <div class="snap-grid-stat-value">2</div>
                    </div>
                </div>
            </div>
            <div class="snap-grid-overlay" style="display: none;"></div>
        `;

        // Cache DOM elements
        this.elements = {
            controlsHeader: this.container.querySelector('.snap-grid-controls-header'),
            controlsLeft: this.container.querySelector('.snap-grid-controls-left'),
            filtersDropdown: this.container.querySelector('.snap-grid-filters-dropdown'),
            layoutDropdown: this.container.querySelector('.snap-grid-layout-dropdown'),
            clearFiltersBtn: this.container.querySelector('.snap-grid-clear-filters-btn'),
            controlsCenter: this.container.querySelector('.snap-grid-controls-center'),
            deleteBtn: this.container.querySelector('.snap-grid-delete-btn'),
            exportBtn: this.container.querySelector('.snap-grid-export-btn'),
            controlsRight: this.container.querySelector('.snap-grid-controls-right'),
            loadedInfo: this.container.querySelector('.snap-grid-loaded-info'),
            header: this.container.querySelector('.snap-grid-header'),
            headerPinnedLeft: this.container.querySelector('.snap-grid-header-pinned-left'),
            headerViewport: this.container.querySelector('.snap-grid-header-viewport'),
            headerRow: this.container.querySelector('.snap-grid-header-row'),
            headerPinnedRight: this.container.querySelector('.snap-grid-header-pinned-right'),
            body: this.container.querySelector('.snap-grid-body'),
            pinnedLeft: this.container.querySelector('.snap-grid-pinned-left'),
            viewport: this.container.querySelector('.snap-grid-viewport'),
            canvas: this.container.querySelector('.snap-grid-canvas'),
            pinnedRight: this.container.querySelector('.snap-grid-pinned-right'),
            footer: this.container.querySelector('.snap-grid-footer'),
            footerStats: this.container.querySelector('.snap-grid-footer-stats'),
            overlay: this.container.querySelector('.snap-grid-overlay')
        };

        // Apply smooth scrolling if enabled
        if (this.config.smoothScrolling) {
            this.elements.viewport.classList.add('smooth-scroll');
        }

        // Set viewport height
        const controlsHeaderHeight = 75; // Height of the controls header
        const headerHeight = this.config.headerHeight;
        const footerHeight = 72; // Height of the footer
        this.elements.body.style.height = `calc(100% - ${controlsHeaderHeight + headerHeight + footerHeight}px)`;

        // Initialize header and footer components (without updating stats yet)
        this.initializeControlsHeader();
        this.initializeFooter();
        
        // Debug: Check if footer elements are created
        console.log('Footer elements created:', {
            footer: !!this.elements.footer,
            footerStats: !!this.elements.footerStats,
            statItems: this.elements.footerStats ? this.elements.footerStats.querySelectorAll('.snap-grid-stat-item').length : 0
        });
    }

    /**
     * Initialize controls header with dropdowns and buttons
     */
    initializeControlsHeader() {
        // Create Filters dropdown
        const filtersDropdown = this.createCustomDropdown([
            { value: 'products-with-sales', text: 'Products With Sales' },
            { value: 'with-no-sales', text: 'With No Sales' },
            { divider: true },
            { value: 'uploaded-today', text: 'Uploaded Today' },
            { value: 'uploaded-yesterday', text: 'Uploaded Yesterday' },
            { value: 'uploaded-this-month', text: 'Uploaded This Month' }
        ], 'Filters');
        
        // Add filter icon to the dropdown
        const filtersTrigger = filtersDropdown.querySelector('.dropdown-header');
        const filterIcon = document.createElement('img');
        filterIcon.src = './assets/filters-active-ic.svg';
        filterIcon.alt = 'Filters';
        filterIcon.style.width = '12px';
        filterIcon.style.height = '12px';
        filterIcon.style.marginRight = '8px';
        filtersTrigger.insertBefore(filterIcon, filtersTrigger.firstChild);
        
        this.elements.filtersDropdown.appendChild(filtersDropdown);

        // Add event listener for Filters dropdown
        filtersDropdown.addEventListener('change', (e) => {
            this.handleFilterDropdownChange(e.detail.value);
        });

        // Create Default Layout dropdown with custom styling
        const layoutDropdown = this.createLayoutDropdown();
        
        // Add layout icon to the dropdown
        const layoutTrigger = layoutDropdown.querySelector('.dropdown-header');
        const layoutIcon = document.createElement('img');
        layoutIcon.src = './assets/show-hide-col-active-ic.svg';
        layoutIcon.alt = 'Layout';
        layoutIcon.style.width = '12px';
        layoutIcon.style.height = '12px';
        layoutIcon.style.marginRight = '8px';
        layoutTrigger.insertBefore(layoutIcon, layoutTrigger.firstChild);
        
        this.elements.layoutDropdown.appendChild(layoutDropdown);

        // Setup Layout dropdown event listener
        layoutDropdown.addEventListener('change', (e) => {
            this.handleLayoutDropdownChange(e.detail.value);
        });

        // Setup Clear Filters button
        this.elements.clearFiltersBtn.addEventListener('click', () => {
            this.handleClearFiltersClick();
        });

        // Setup Delete button dropdown
        this.setupDeleteDropdown();

        // Setup Export button dropdown
        this.setupExportDropdown();
    }

    /**
     * Handle filter dropdown selection changes
     */
    handleFilterDropdownChange(filterValue) {
        console.log('🔍 Filter dropdown changed to:', filterValue);
        
        // Debug: Log all available columns
        console.log('📋 Available columns:', this.config.columns.map(col => ({
            field: col.field,
            headerName: col.headerName,
            type: col.type
        })));
        
        // Find the relevant columns for filtering
        const salesColumn = this.config.columns.find(col => 
            col.field === 'sales' || col.field === 'Sales' || 
            col.headerName === 'Sales' || col.headerName === 'sales'
        );
        
        const dateColumn = this.config.columns.find(col => 
            col.field === 'firstPublishedDate' || col.field === 'First Published Date' ||
            col.headerName === 'First Published Date' || col.headerName === 'firstPublishedDate' ||
            col.field === 'publishedDate' || col.headerName === 'Published Date'
        );
        
        console.log('🔍 Found sales column:', salesColumn);
        console.log('🔍 Found date column:', dateColumn);

        // Clear opposite category filters when switching between sales and uploaded filters
        const isSalesFilter = ['products-with-sales', 'with-no-sales'].includes(filterValue);
        const isUploadedFilter = ['uploaded-today', 'uploaded-yesterday', 'uploaded-this-month'].includes(filterValue);
        
        if (isSalesFilter && dateColumn) {
            // Clear uploaded filters when switching to sales filters
            console.log('🧹 Clearing uploaded filters before applying sales filter');
            this.clearFilter(dateColumn.field);
        } else if (isUploadedFilter && salesColumn) {
            // Clear sales filters when switching to uploaded filters
            console.log('🧹 Clearing sales filters before applying uploaded filter');
            this.clearFilter(salesColumn.field);
        }

        switch (filterValue) {
            case 'products-with-sales':
                if (salesColumn) {
                    console.log('🎯 Applying "Products With Sales" filter to column:', salesColumn.field);
                    // Ensure the Sales column is visible
                    this.ensureColumnVisible(salesColumn.field);
                    // Apply the filter silently
                    this.setFilter(salesColumn.field, {
                        type: 'number',
                        operator: 'greaterThan',
                        value: 0
                    });
                    console.log('✅ Applied "Products With Sales" filter silently');
                } else {
                    console.warn('⚠️ Could not find Sales column for filtering');
                }
                break;

            case 'with-no-sales':
                if (salesColumn) {
                    console.log('🎯 Applying "With No Sales" filter to column:', salesColumn.field);
                    console.log('🎯 Filter config:', {
                        type: 'number',
                        operator: 'equals',
                        value: 0
                    });
                    // Ensure the Sales column is visible
                    this.ensureColumnVisible(salesColumn.field);
                    // Apply the filter silently
                    this.setFilter(salesColumn.field, {
                        type: 'number',
                        operator: 'equals',
                        value: 0
                    });
                    console.log('✅ Applied "With No Sales" filter silently');
                    console.log('🔍 Current filterConfig after setFilter:', this.state.filterConfig);
                } else {
                    console.warn('⚠️ Could not find Sales column for filtering');
                }
                break;

            case 'uploaded-today':
                if (dateColumn) {
                    // Ensure the Date column is visible
                    this.ensureColumnVisible(dateColumn.field);
                    this.setFilter(dateColumn.field, {
                        type: 'date',
                        operator: 'today',
                        value: null
                    });
                    console.log('✅ Applied "Uploaded Today" filter using predefined "today" option');
                } else {
                    console.warn('⚠️ Could not find Date column for filtering');
                }
                break;

            case 'uploaded-yesterday':
                if (dateColumn) {
                    // Ensure the Date column is visible
                    this.ensureColumnVisible(dateColumn.field);
                    this.setFilter(dateColumn.field, {
                        type: 'date',
                        operator: 'yesterday',
                        value: null
                    });
                    console.log('✅ Applied "Uploaded Yesterday" filter using predefined "yesterday" option');
                } else {
                    console.warn('⚠️ Could not find Date column for filtering');
                }
                break;

            case 'uploaded-this-month':
                if (dateColumn) {
                    // Ensure the Date column is visible
                    this.ensureColumnVisible(dateColumn.field);
                    this.setFilter(dateColumn.field, {
                        type: 'date',
                        operator: 'currentMonth',
                        value: null
                    });
                    console.log('✅ Applied "Uploaded This Month" filter using predefined "currentMonth" option');
                } else {
                    console.warn('⚠️ Could not find Date column for filtering');
                }
                break;

            default:
                console.log('ℹ️ No filter applied for value:', filterValue);
                break;
        }
    }

    /**
     * Create the Layout dropdown with custom styling and icons
     */
    createLayoutDropdown() {
        const dropdown = document.createElement('div');
        dropdown.className = 'snap-dropdown layout-dropdown';

        // Create dropdown header
        const trigger = document.createElement('div');
        trigger.className = 'dropdown-header';

        const triggerText = document.createElement('span');
        triggerText.textContent = 'Default Layout';

        const arrow = document.createElement('img');
        arrow.src = './assets/dropdown-ic.svg';
        arrow.alt = 'Dropdown';
        arrow.className = 'dropdown-arrow';

        trigger.appendChild(triggerText);
        trigger.appendChild(arrow);

        // Create dropdown menu
        const menu = document.createElement('div');
        menu.className = 'dropdown-menu hidden';

        // Create menu items
        const items = [
            { value: 'default', text: 'Default Layout', icon: 'checked-option-ic.svg', selected: true, isLayout: true },
            { divider: true },
            { value: 'save', text: 'Save Layout', icon: 'save-layout-ic.svg', selected: false, isLayout: false },
            { value: 'delete', text: 'Delete Selected', icon: 'delete-ic.svg', selected: false, isLayout: false }
        ];

        items.forEach(item => {
            if (item.divider) {
                const dividerElement = this.createMenuSeparator();
                menu.appendChild(dividerElement);
                return;
            }

            const optionElement = document.createElement('div');
            optionElement.className = 'dropdown-item layout-item';
            optionElement.setAttribute('data-value', item.value);

            // Create icon container (always reserve space for icon)
            const iconContainer = document.createElement('div');
            iconContainer.className = 'item-icon';
            iconContainer.style.width = '16px';
            iconContainer.style.height = '16px';
            iconContainer.style.marginRight = '8px';
            iconContainer.style.display = 'flex';
            iconContainer.style.alignItems = 'center';
            iconContainer.style.justifyContent = 'center';

            // Add icon based on item type
            if (item.isLayout) {
                // For layout items, show check icon only if selected
                const icon = document.createElement('img');
                icon.src = `./assets/${item.icon}`;
                icon.alt = item.text;
                icon.className = `check-icon ${item.selected ? '' : 'hidden'}`;
                icon.style.width = '12px';
                icon.style.height = '12px';
                iconContainer.appendChild(icon);
            } else {
                // For action items, always show their specific icon
                const icon = document.createElement('img');
                icon.src = `./assets/${item.icon}`;
                icon.alt = item.text;
                icon.style.width = '12px';
                icon.style.height = '12px';
                iconContainer.appendChild(icon);
            }

            // Create text container
            const textContainer = document.createElement('span');
            textContainer.textContent = item.text;
            textContainer.className = 'item-text';

            // Assemble the option
            optionElement.appendChild(iconContainer);
            optionElement.appendChild(textContainer);

            // Add selected class if this is the default selected item
            if (item.selected) {
                optionElement.classList.add('selected');
            }

            // Add click handler
            optionElement.addEventListener('click', (e) => {
                e.stopPropagation();

                if (item.isLayout) {
                    // Only layout items can be selected
                    // Update selection using the same logic as saved layouts
                    this.updateLayoutSelection(optionElement);

                    // Update header text
                    triggerText.textContent = item.text;
                }

                // Close dropdown
                dropdown.classList.remove('focused');
                menu.classList.add('hidden');

                // Dispatch change event
                const changeEvent = new CustomEvent('change', {
                    detail: { value: item.value, text: item.text }
                });
                dropdown.dispatchEvent(changeEvent);
            });

            menu.appendChild(optionElement);
        });

        // Toggle dropdown
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();
            const isOpen = !menu.classList.contains('hidden');

            // Close all other dropdowns
            document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                d.classList.remove('focused');
                d.querySelector('.dropdown-menu').classList.add('hidden');
            });

            if (!isOpen) {
                // Load saved layouts when opening dropdown
                this.updateLayoutDropdown();
                
                // Update selection state to match current layout
                this.updateCurrentLayoutSelection();
                
                dropdown.classList.add('focused');
                menu.classList.remove('hidden');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', () => {
            dropdown.classList.remove('focused');
            menu.classList.add('hidden');
        });

        dropdown.appendChild(trigger);
        dropdown.appendChild(menu);

        // Load saved layouts into the dropdown
        this.updateLayoutDropdown();

        return dropdown;
    }

    getSaveLayoutPopupHTML() {
        return `
            <div class="popup-overlay" id="saveLayoutPopup">
                <div class="popup-card save-layout-popup">
                    <img src="./assets/close-ic.svg" class="popup-close" alt="Close">
                    <h3 class="popup-title">Save New Layout</h3>
                    <div class="popup-form-group">
                        <div class="layout-input-container">
                            <input type="text" id="layoutNameInput" placeholder="Enter layout name">
                        </div>
                        <div class="error-message">This layout name already exists.</div>
                        <div class="checkbox-container">
                            <div class="filter-checkbox-item">
                                <div class="checkbox-wrapper" id="includeFiltersCheckboxWrapper">
                                    <img src="./assets/checkbox-ic.svg" class="checkbox-icon" alt="Checked">
                                </div>
                                <label class="filter-checkbox-label">Include current filters</label>
                            </div>
                        </div>
                    </div>
                    <button id="saveLayoutButton" class="save-layout-button">
                        <span>Save Layout</span>
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Handle layout dropdown selection changes
     */
    handleLayoutDropdownChange(value) {
        console.log('🎨 Layout dropdown changed to:', value);
        
        switch (value) {
            case 'default':
                console.log('✅ Default Layout selected');
                this.applyDefaultLayout();
                break;
                
            case 'save':
                console.log('💾 Save Layout selected');
                // Implement save layout functionality
                this.saveCurrentLayout();
                break;
                
            case 'delete':
                console.log('🗑️ Delete Selected selected');
                // Implement delete selected layout functionality
                this.deleteSelectedLayout();
                break;
                
            default:
                // Check if it's a saved layout ID
                if (value.startsWith('layout_')) {
                    const savedLayouts = this.getSavedLayouts();
                    const layout = savedLayouts.find(l => l.id === value);
                    if (layout) {
                        console.log('🎨 Applying saved layout:', layout.name);
                        this.applyLayout(layout);
                    }
                } else {
                    console.log('ℹ️ Unknown layout option:', value);
                }
                break;
        }
    }

    /**
     * Save current layout configuration
     */
    saveCurrentLayout() {
        console.log('💾 Save Current Layout clicked');
        this.showSaveLayoutPopup();
    }

    showSaveLayoutPopup() {
        // Create popup if it doesn't exist
        let popup = document.getElementById('saveLayoutPopup');
        if (!popup) {
            // Add popup to body
            document.body.insertAdjacentHTML('beforeend', this.getSaveLayoutPopupHTML());
            popup = document.getElementById('saveLayoutPopup');
            this.setupSaveLayoutPopupEvents();
        }

        // Show popup
        popup.style.display = 'flex';
        
        // Focus input and clear previous values
        const input = document.getElementById('layoutNameInput');
        const errorMsg = popup.querySelector('.error-message');
        const saveBtn = document.getElementById('saveLayoutButton');
        
        input.value = '';
        errorMsg.style.display = 'none';
        saveBtn.classList.remove('active');
        
        // Focus input after a short delay
        setTimeout(() => {
            input.focus();
        }, 50);
    }

    hideSaveLayoutPopup() {
        const popup = document.getElementById('saveLayoutPopup');
        if (popup) {
            popup.style.display = 'none';
        }
    }

    setupSaveLayoutPopupEvents() {
        const popup = document.getElementById('saveLayoutPopup');
        if (!popup) return;

        const input = document.getElementById('layoutNameInput');
        const closeBtn = popup.querySelector('.popup-close');
        const saveBtn = document.getElementById('saveLayoutButton');
        const errorMsg = popup.querySelector('.error-message');
        const checkboxWrapper = document.getElementById('includeFiltersCheckboxWrapper');

        // Close button
        closeBtn.addEventListener('click', () => {
            this.hideSaveLayoutPopup();
        });

        // Click outside to close
        popup.addEventListener('click', (e) => {
            if (e.target === popup) {
                this.hideSaveLayoutPopup();
            }
        });

        // Input validation
        input.addEventListener('input', () => {
            this.validateLayoutName();
        });

        // Checkbox click handler - direct implementation for popup
        if (checkboxWrapper) {
            const checkboxIcon = checkboxWrapper.querySelector('.checkbox-icon');
            const checkboxLabel = popup.querySelector('.filter-checkbox-label');
            
            // Function to toggle checkbox
            const toggleCheckbox = () => {
                const isChecked = checkboxIcon.src.includes('checkbox-ic.svg');
                const newState = !isChecked;
                
                // Update the icon
                checkboxIcon.src = `./assets/${newState ? 'checkbox-ic' : 'uncheckedbox-ic'}.svg`;
                checkboxIcon.alt = newState ? 'Checked' : 'Unchecked';
                
                console.log('Checkbox toggled:', newState);
            };
            
            // Click handler for the checkbox wrapper
            checkboxWrapper.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleCheckbox();
            });
            
            // Click handler for the label
            if (checkboxLabel) {
                checkboxLabel.addEventListener('click', (e) => {
                    e.stopPropagation();
                    toggleCheckbox();
                });
            }
            
            // Also listen for the global checkbox change event as backup
            checkboxWrapper.addEventListener('checkbox-change', (e) => {
                console.log('Checkbox state changed via global system:', e.detail.checked);
            });
        }

        // Save button
        saveBtn.addEventListener('click', () => {
            if (saveBtn.classList.contains('active')) {
                this.performSaveLayout();
            }
        });
    }

    validateLayoutName() {
        const input = document.getElementById('layoutNameInput');
        const inputContainer = input.closest('.layout-input-container');
        const saveBtn = document.getElementById('saveLayoutButton');
        const errorMsg = document.querySelector('#saveLayoutPopup .error-message');
        
        const name = input.value.trim();
        saveBtn.classList.remove('active');
        errorMsg.style.display = 'none';
        inputContainer.classList.remove('error');

        if (name) {
            // Check if name is too long
            if (name.length > 30) {
                errorMsg.textContent = 'Layout name cannot exceed 30 characters';
                errorMsg.style.display = 'block';
                inputContainer.classList.add('error');
                return;
            }

            // Check if name already exists (you can implement this check)
            // For now, we'll just enable the button if name is valid
            saveBtn.classList.add('active');
        }
    }

    performSaveLayout() {
        const input = document.getElementById('layoutNameInput');
        const checkboxWrapper = document.getElementById('includeFiltersCheckboxWrapper');
        const checkboxIcon = checkboxWrapper?.querySelector('.checkbox-icon');
        const name = input.value.trim();
        
        // Check if checkbox is checked by looking at the icon source
        const includeFilters = checkboxIcon?.src.includes('checkbox-ic.svg') || false;

        console.log('💾 Saving layout:', { name, includeFilters });
        
        // Capture current visible columns and their order
        const visibleColumns = this.getOrderedColumns().map(col => col.field);
        console.log('📋 Visible columns to save:', visibleColumns);
        
        // Capture current filters and checkbox states if includeFilters is true
        let savedFilters = {};
        let savedCheckboxStates = {};
        
        if (includeFilters) {
            savedFilters = { ...this.state.filterConfig };
            
            // Also save checkbox states for each field that has filters
            Object.keys(savedFilters).forEach(field => {
                if (this.checkboxStates.has(field)) {
                    savedCheckboxStates[field] = Array.from(this.checkboxStates.get(field));
                }
            });
        }
        
        console.log('🔍 Saved filters:', savedFilters);
        console.log('🔍 Saved checkbox states:', savedCheckboxStates);
        
        // Create layout object
        const layout = {
            id: 'layout_' + Date.now(),
            name: name,
            visibleColumns: visibleColumns,
            filters: savedFilters,
            checkboxStates: savedCheckboxStates,
            createdAt: new Date().toISOString()
        };
        
        // Store layout in localStorage
        this.saveLayoutToStorage(layout);
        
        // Update layout dropdown to include the new layout
        this.updateLayoutDropdown();
        
        // Switch to the newly saved layout
        this.applyLayout(layout);
        
        // Hide popup
        this.hideSaveLayoutPopup();
        
        // Show success message
        console.log('✅ Layout saved successfully!', layout);
    }

    /**
     * Save layout to localStorage
     */
    saveLayoutToStorage(layout) {
        try {
            const savedLayouts = this.getSavedLayouts();
            savedLayouts.push(layout);
            localStorage.setItem('snapGrid_savedLayouts', JSON.stringify(savedLayouts));
            console.log('💾 Layout saved to localStorage:', layout);
        } catch (error) {
            console.error('❌ Failed to save layout to localStorage:', error);
        }
    }

    /**
     * Get saved layouts from localStorage
     */
    getSavedLayouts() {
        try {
            const saved = localStorage.getItem('snapGrid_savedLayouts');
            return saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.error('❌ Failed to load layouts from localStorage:', error);
            return [];
        }
    }

    /**
     * Update layout dropdown to include saved layouts
     */
    updateLayoutDropdown() {
        const layoutDropdown = this.elements.layoutDropdown;
        if (!layoutDropdown) return;

        const menu = layoutDropdown.querySelector('.dropdown-menu');
        if (!menu) return;

        // Clear existing layout items (keep Default Layout, divider, Save Layout, Delete Selected)
        const existingLayoutItems = menu.querySelectorAll('.dropdown-item[data-layout-id]');
        existingLayoutItems.forEach(item => item.remove());

        // Get saved layouts
        const savedLayouts = this.getSavedLayouts();
        
        // Find the divider to insert layouts before it
        const divider = menu.querySelector('.snap-grid-menu-separator');
        if (divider && savedLayouts.length > 0) {
            // Insert saved layouts before the divider
            savedLayouts.forEach(layout => {
                const layoutItem = this.createLayoutMenuItem(layout);
                divider.parentNode.insertBefore(layoutItem, divider);
            });
        }
    }

    /**
     * Create a layout menu item
     */
    createLayoutMenuItem(layout) {
        const item = document.createElement('div');
        item.className = 'dropdown-item layout-item';
        item.setAttribute('data-layout-id', layout.id);
        item.setAttribute('data-value', layout.id);
        
        // Create icon container (always reserve space for icon)
        const iconContainer = document.createElement('div');
        iconContainer.className = 'item-icon';
        iconContainer.style.width = '16px';
        iconContainer.style.height = '16px';
        iconContainer.style.marginRight = '8px';
        iconContainer.style.display = 'flex';
        iconContainer.style.alignItems = 'center';
        iconContainer.style.justifyContent = 'center';
        
        // Create checkbox icon (initially hidden)
        const icon = document.createElement('img');
        icon.src = './assets/checked-option-ic.svg';
        icon.alt = 'Selected';
        icon.className = 'check-icon hidden';
        icon.style.width = '12px';
        icon.style.height = '12px';
        iconContainer.appendChild(icon);
        
        // Create text container
        const textContainer = document.createElement('span');
        textContainer.textContent = layout.name;
        textContainer.className = 'item-text';
        
        item.appendChild(iconContainer);
        item.appendChild(textContainer);
        
        // Add click handler
        item.addEventListener('click', (e) => {
            e.stopPropagation();
            
            // Update selection in dropdown
            this.updateLayoutSelection(item);
            
            // Apply the layout
            this.applyLayout(layout);
            
            // Close the dropdown
            const layoutDropdown = this.elements.layoutDropdown;
            if (layoutDropdown) {
                layoutDropdown.classList.remove('focused');
                const menu = layoutDropdown.querySelector('.dropdown-menu');
                if (menu) {
                    menu.classList.add('hidden');
                }
            }
        });
        
        return item;
    }

    /**
     * Update layout selection in dropdown (show checkbox only on selected item)
     */
    updateLayoutSelection(selectedItem) {
        const layoutDropdown = this.elements.layoutDropdown;
        if (!layoutDropdown) return;
        
        const menu = layoutDropdown.querySelector('.dropdown-menu');
        if (!menu) return;
        
        // Remove selection from all layout items
        const allLayoutItems = menu.querySelectorAll('.layout-item');
        allLayoutItems.forEach(item => {
            item.classList.remove('selected');
            const checkIcon = item.querySelector('.check-icon');
            if (checkIcon) {
                checkIcon.classList.add('hidden');
            }
        });
        
        // Mark selected item
        selectedItem.classList.add('selected');
        const selectedCheckIcon = selectedItem.querySelector('.check-icon');
        if (selectedCheckIcon) {
            selectedCheckIcon.classList.remove('hidden');
        }
    }

    /**
     * Apply a saved layout
     */
    applyLayout(layout) {
        console.log('🎨 Applying layout:', layout);
        
        // Apply column visibility
        this.applyLayoutColumns(layout.visibleColumns);
        
        // Apply filters if they exist
        if (layout.filters && Object.keys(layout.filters).length > 0) {
            this.applyLayoutFilters(layout.filters, layout.checkboxStates || {});
        } else {
            // Clear all filters if no filters in layout
            this.clearAllFilters();
        }
        
        // Update layout dropdown header to show current layout
        this.updateLayoutDropdownHeader(layout.name);
        
        // Update dropdown selection to show checkbox on selected layout
        const layoutDropdown = this.elements.layoutDropdown;
        if (layoutDropdown) {
            const menu = layoutDropdown.querySelector('.dropdown-menu');
            if (menu) {
                const selectedItem = menu.querySelector(`[data-layout-id="${layout.id}"]`);
                if (selectedItem) {
                    this.updateLayoutSelection(selectedItem);
                }
            }
        }
        
        console.log('✅ Layout applied successfully!');
    }

    /**
     * Apply layout columns (show only specified columns)
     */
    applyLayoutColumns(visibleColumns) {
        // Hide all columns first
        this.config.columns.forEach(col => {
            if (col.field !== 'checkbox' && col.field !== 'preview' && col.field !== 'actions') {
                this.hideColumn(col.field);
            }
        });
        
        // Show only the columns specified in the layout
        visibleColumns.forEach(field => {
            if (field !== 'checkbox' && field !== 'preview' && field !== 'actions') {
                this.showColumn(field);
            }
        });
        
        // Update column order to match layout
        this.state.columnOrder = visibleColumns;
        
        // Re-render the grid
        this.render();
    }

    /**
     * Apply layout filters
     */
    applyLayoutFilters(filters, checkboxStates = {}) {
        // Clear existing filters first
        this.clearAllFilters();
        
        // Apply each filter from the layout
        Object.entries(filters).forEach(([field, filterConfig]) => {
            this.setFilter(field, filterConfig);
        });
        
        // Restore checkbox states for each field that has filters
        Object.entries(checkboxStates).forEach(([field, checkedValues]) => {
            if (checkedValues && Array.isArray(checkedValues)) {
                // Clear current checkbox state for this field
                this.checkboxStates.set(field, new Set());
                
                // Set the saved checkbox state
                this.setAllCheckboxValues(field, checkedValues, true);
                
                console.log('🔄 Restored checkbox state for field:', field, 'with values:', checkedValues);
            }
        });
        
        // Update filter indicators to show which columns have filters
        this.updateFilterIndicators();
        
        // Refresh checkbox UI for all fields that have restored states
        Object.keys(checkboxStates).forEach(field => {
            this.refreshCheckboxUIForField(field);
        });
        
        console.log('✅ Applied layout filters and updated indicators for fields:', Object.keys(filters));
    }

    /**
     * Refresh checkbox UI for a specific field after restoring state
     */
    refreshCheckboxUIForField(field) {
        // Find all open column menus for this field
        const menus = this.container.querySelectorAll('.snap-grid-column-menu');
        menus.forEach(menu => {
            const filterPanel = menu.querySelector('[data-tab-panel="filter"].menu-tab-panel');
            if (filterPanel) {
                const checkboxList = filterPanel.querySelector('.filter-checkbox-list');
                if (checkboxList) {
                    // Regenerate the checkbox list to reflect the restored state
                    this.regenerateCheckboxListForField(field, checkboxList);
                }
            }
        });
    }

    /**
     * Update layout dropdown header text
     */
    updateLayoutDropdownHeader(layoutName) {
        const layoutDropdown = this.elements.layoutDropdown;
        if (!layoutDropdown) return;
        
        const headerSpan = layoutDropdown.querySelector('.dropdown-header span');
        if (headerSpan) {
            headerSpan.textContent = layoutName;
        }
        
        // Save current layout to localStorage
        this.saveCurrentLayoutSelection(layoutName);
    }
    
    /**
     * Save current layout selection to localStorage
     */
    saveCurrentLayoutSelection(layoutName) {
        try {
            localStorage.setItem('snapGrid_currentLayout', layoutName);
            console.log('💾 Saved current layout selection:', layoutName);
        } catch (error) {
            console.error('❌ Failed to save current layout selection:', error);
        }
    }
    
    /**
     * Get current layout selection from localStorage
     */
    getCurrentLayoutSelection() {
        try {
            const currentLayout = localStorage.getItem('snapGrid_currentLayout');
            return currentLayout || 'Default Layout';
        } catch (error) {
            console.error('❌ Failed to get current layout selection:', error);
            return 'Default Layout';
        }
    }

    /**
     * Restore saved layout selection on page load
     */
    restoreSavedLayoutSelection() {
        const savedLayoutName = this.getCurrentLayoutSelection();
        console.log('🔄 Restoring saved layout selection:', savedLayoutName);
        
        // If it's Default Layout, apply it
        if (savedLayoutName === 'Default Layout') {
            this.applyDefaultLayout();
            return;
        }
        
        // If it's a saved layout, find and apply it
        const savedLayouts = this.getSavedLayouts();
        const savedLayout = savedLayouts.find(layout => layout.name === savedLayoutName);
        
        if (savedLayout) {
            console.log('✅ Found saved layout, applying:', savedLayout);
            // Apply the layout - this will also update the dropdown selection
            this.applyLayout(savedLayout);
        } else {
            console.log('⚠️ Saved layout not found, falling back to Default Layout');
            this.applyDefaultLayout();
        }
    }
    
    /**
     * Update layout dropdown selection by layout ID
     */
    updateLayoutDropdownSelection(layoutId) {
        const layoutDropdown = this.elements.layoutDropdown;
        if (!layoutDropdown) return;
        
        const menu = layoutDropdown.querySelector('.dropdown-menu');
        if (!menu) return;
        
        const selectedItem = menu.querySelector(`[data-layout-id="${layoutId}"]`);
        if (selectedItem) {
            console.log('✅ Updating dropdown selection to layout:', layoutId);
            this.updateLayoutSelection(selectedItem);
        } else {
            console.log('⚠️ Could not find layout item in dropdown:', layoutId);
        }
    }
    
    /**
     * Update dropdown selection to match current layout
     */
    updateCurrentLayoutSelection() {
        const layoutDropdown = this.elements.layoutDropdown;
        if (!layoutDropdown) return;
        
        const headerSpan = layoutDropdown.querySelector('.dropdown-header span');
        const currentLayoutName = headerSpan ? headerSpan.textContent : '';
        
        console.log('🔄 Updating dropdown selection for current layout:', currentLayoutName);
        
        const menu = layoutDropdown.querySelector('.dropdown-menu');
        if (!menu) return;
        
        // Find the item that matches the current layout name
        let selectedItem = null;
        
        if (currentLayoutName === 'Default Layout') {
            selectedItem = menu.querySelector('[data-value="default"]');
        } else {
            // Find saved layout by name
            const savedLayouts = this.getSavedLayouts();
            const savedLayout = savedLayouts.find(layout => layout.name === currentLayoutName);
            if (savedLayout) {
                selectedItem = menu.querySelector(`[data-layout-id="${savedLayout.id}"]`);
            }
        }
        
        if (selectedItem) {
            console.log('✅ Found matching item, updating selection');
            this.updateLayoutSelection(selectedItem);
        } else {
            console.log('⚠️ Could not find item for current layout:', currentLayoutName);
        }
    }

    /**
     * Apply default layout (show all columns, clear filters)
     */
    applyDefaultLayout() {
        console.log('🎨 Applying default layout');
        
        try {
            // Show all columns
            this.config.columns.forEach(col => {
                if (col.field !== 'checkbox' && col.field !== 'preview' && col.field !== 'actions') {
                    this.showColumn(col.field);
                }
            });
            
            // Reset column order to default (if method exists)
            if (typeof this.resetToDefaultLayout === 'function') {
                this.resetToDefaultLayout();
            } else {
                // Fallback: reset column order manually
                this.state.columnOrder = this.config.columns.map(col => col.field);
            }
            
            // Clear all filters
            this.clearAllFilters();
            
            // Update layout dropdown header
            this.updateLayoutDropdownHeader('Default Layout');
            
            // Update dropdown selection to show checkbox on Default Layout
            const layoutDropdown = this.elements.layoutDropdown;
            if (layoutDropdown) {
                const menu = layoutDropdown.querySelector('.dropdown-menu');
                if (menu) {
                    const defaultItem = menu.querySelector('[data-value="default"]');
                    if (defaultItem) {
                        this.updateLayoutSelection(defaultItem);
                    }
                }
            }
            
            console.log('✅ Default layout applied successfully!');
        } catch (error) {
            console.error('❌ Error applying default layout:', error);
            // Still try to update the UI even if there was an error
            this.updateLayoutDropdownHeader('Default Layout');
        }
    }

    /**
     * Handle Clear Filters button click
     */
    handleClearFiltersClick() {
        console.log('🧹 Clearing all filters from current layout');
        this.clearAllFilters();
        // Reset the filters dropdown header to default
        this.resetFiltersDropdownHeader();
    }

    /**
     * Delete selected layout
     */
    deleteSelectedLayout() {
        console.log('🗑️ Delete Selected clicked');
        
        // Get current layout name from dropdown header
        const layoutDropdown = this.elements.layoutDropdown;
        if (!layoutDropdown) {
            console.error('❌ Layout dropdown not found');
            return;
        }
        
        const headerSpan = layoutDropdown.querySelector('.dropdown-header span');
        const currentLayoutName = headerSpan ? headerSpan.textContent : '';
        
        console.log('📋 Current layout:', currentLayoutName);
        
        // Check if trying to delete Default Layout
        if (currentLayoutName === 'Default Layout') {
            this.showNotification('This layout cannot be deleted. The Default Layout is protected and cannot be removed.', 'warning');
            console.log('⚠️ Attempted to delete Default Layout - blocked');
            return;
        }
        
        // Check if it's a saved layout
        if (currentLayoutName && currentLayoutName !== 'Default Layout') {
            // Show confirmation dialog
            const confirmed = confirm(`Are you sure you want to delete "${currentLayoutName}"?\n\nThis action cannot be undone.`);
            
            if (confirmed) {
                console.log('✅ User confirmed deletion of layout:', currentLayoutName);
                this.performDeleteLayout(currentLayoutName);
            } else {
                console.log('❌ User cancelled deletion');
            }
        } else {
            console.log('⚠️ No valid layout selected for deletion');
        }
    }
    
    /**
     * Perform the actual deletion of a saved layout
     */
    performDeleteLayout(layoutName) {
        try {
            // Get saved layouts
            const savedLayouts = this.getSavedLayouts();
            
            // Find the layout to delete
            const layoutIndex = savedLayouts.findIndex(layout => layout.name === layoutName);
            
            if (layoutIndex === -1) {
                console.error('❌ Layout not found:', layoutName);
                this.showNotification('Layout not found. It may have already been deleted.', 'warning');
                return;
            }
            
            // Remove the layout
            const deletedLayout = savedLayouts.splice(layoutIndex, 1)[0];
            
            // Save updated layouts to localStorage
            localStorage.setItem('snapGrid_savedLayouts', JSON.stringify(savedLayouts));
            
            console.log('✅ Layout deleted successfully:', deletedLayout);
            
            // Update the dropdown to remove the deleted layout
            this.updateLayoutDropdown();
            
            // If we were on the deleted layout, switch to Default Layout
            const headerSpan = this.elements.layoutDropdown.querySelector('.dropdown-header span');
            if (headerSpan && headerSpan.textContent === layoutName) {
                console.log('🔄 Switching to Default Layout after deletion');
                this.applyDefaultLayout();
            }
            
            // Show success message
            this.showNotification(`Layout "${layoutName}" has been deleted successfully.`, 'success');
            
        } catch (error) {
            console.error('❌ Error deleting layout:', error);
            this.showNotification('Failed to delete layout. Please try again.', 'warning');
        }
    }

    /**
     * Show notification message (Snap Image Studio style)
     */
    showNotification(message, type = 'warning') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        // Append to main-content instead of body
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.appendChild(notification);
        } else {
            document.body.appendChild(notification);
        }
        
        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
        
        return notification;
    }

    /**
     * Ensure a column is visible by showing it if it's hidden
     */
    ensureColumnVisible(field) {
        // Check if column is currently hidden using the proper state management
        if (this.state.hiddenColumns.has(field)) {
            console.log('🔧 Column is hidden, making it visible:', field);
            // Use the proper showColumn method to update both state and UI
            this.showColumn(field);
            console.log('✅ Column made visible and grid re-rendered:', field);
        } else {
            console.log('ℹ️ Column is already visible:', field);
        }
    }

    /**
     * Reset the filters dropdown header to default text and clear selection highlighting
     */
    resetFiltersDropdownHeader() {
        const filtersDropdown = this.elements.filtersDropdown;
        if (filtersDropdown) {
            const headerSpan = filtersDropdown.querySelector('.dropdown-header span');
            if (headerSpan) {
                headerSpan.textContent = 'Filters';
                console.log('✅ Reset filters dropdown header to "Filters"');
            }
            
            // Clear selection highlighting from all dropdown items
            const dropdownItems = filtersDropdown.querySelectorAll('.dropdown-item');
            dropdownItems.forEach(item => {
                item.classList.remove('selected');
            });
            console.log('✅ Cleared selection highlighting from all dropdown items');
        }
    }

    /**
     * Update filter UI elements to show applied filter values
     */
    updateFilterUI(field, operator, value) {
        // Find the column menu for this field
        const columnMenu = this.container.querySelector(`[data-field="${field}"] .snap-grid-column-menu`);
        if (!columnMenu) {
            console.log('⚠️ No column menu found for field:', field);
            return;
        }

        // Find the filter panel within the menu
        const filterPanel = columnMenu.querySelector('[data-tab-panel="filter"]');
        if (!filterPanel) {
            console.log('⚠️ No filter panel found for field:', field);
            return;
        }

        // Find the first dropdown (operator) and input (value)
        const firstDropdown = filterPanel.querySelector('.filter-type-dropdown');
        const firstInput = filterPanel.querySelector('.filter-input');

        if (firstDropdown && firstInput) {
            // Set the operator dropdown
            this.setCustomDropdownValue(firstDropdown, operator);
            
            // Set the input value
            firstInput.value = value;
            
            console.log('✅ Updated filter UI:', {
                field,
                operator,
                value,
                dropdownValue: firstDropdown.querySelector('.dropdown-header span').textContent,
                inputValue: firstInput.value
            });
        } else {
            console.log('⚠️ Could not find filter UI elements for field:', field);
        }
    }

    /**
     * Initialize footer with statistics
     */
    initializeFooter() {
        // Footer structure is already created in HTML, no need to update stats yet
    }

    /**
     * Update loaded products info display (loader/counter)
     */
    updateLoadedInfo() {
        if (!this.state || !this.elements.loadedInfo) return;
        
        const loadedCount = this.state.data ? this.state.data.length : 0;
        const totalCount = this.state.totalProducts ? this.state.totalProducts : loadedCount;
        
        this.elements.loadedInfo.textContent = `${loadedCount.toLocaleString()} / ${totalCount.toLocaleString()}`;
    }

    /**
     * Start loading simulation for development testing
     */
    startLoadingSimulation() {
        console.log('🚀 Starting loading simulation for development testing');
        
        // Set a high total count to simulate loading
        this.state.totalProducts = 1000;
        this.state.isLoading = true;
        
        // Add loading class to the loaded info element
        if (this.elements.loadedInfo) {
            this.elements.loadedInfo.classList.add('loading');
        }
        
        // Start loading 10 rows per second
        this.loadingInterval = setInterval(() => {
            this.simulateLoadMoreData();
        }, 1000); // 1 second = 10 rows
        
        // Stop loading after 10 seconds (100 rows)
        setTimeout(() => {
            this.stopLoadingSimulation();
        }, 10000);
    }

    /**
     * Stop loading simulation
     */
    stopLoadingSimulation() {
        console.log('🛑 Stopping loading simulation');
        
        if (this.loadingInterval) {
            clearInterval(this.loadingInterval);
            this.loadingInterval = null;
        }
        
        this.state.isLoading = false;
        
        // Remove loading class
        if (this.elements.loadedInfo) {
            this.elements.loadedInfo.classList.remove('loading');
        }
        
        // Update the display
        this.updateLoadedInfo();
    }

    /**
     * Simulate loading more data
     */
    simulateLoadMoreData() {
        if (!this.state.isLoading) return;
        
        // Generate 10 more rows of dummy data
        const newRows = this.generateDummyRows(10);
        
        // Add to existing data
        this.state.data = [...this.state.data, ...newRows];
        
        // Update filtered data and display data
        this.processData();
        
        // Update the loaded info
        this.updateLoadedInfo();
        
        // Re-render the grid
        this.render();
        
        console.log(`📊 Loaded ${this.state.data.length} / ${this.state.totalProducts} rows`);
    }

    /**
     * Generate dummy rows for testing
     */
    generateDummyRows(count) {
        const rows = [];
        const marketplaces = ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'];
        const statuses = ['Draft', 'Translating', 'Under Review', 'Declined', 'Rejected', 'Processing', 'Timed out', 'Auto-uploaded', 'Live', 'Removed', 'Locked'];
        const productTypes = ['Standard t-shirt', 'Premium t-shirt', 'V-neck t-shirt', 'Tank top', 'Long sleeve t-shirt', 'Raglan', 'Sweatshirt', 'Pullover hoodie', 'Zip hoodie', 'PopSockets', 'iPhone cases', 'Samsung Galaxy cases', 'Tote bag', 'Throw pillows', 'Tumbler'];
        
        for (let i = 0; i < count; i++) {
            const rowIndex = this.state.data.length + i;
            rows.push({
                id: `dummy-${rowIndex}`,
                marketplace: marketplaces[Math.floor(Math.random() * marketplaces.length)],
                status: statuses[Math.floor(Math.random() * statuses.length)],
                productType: productTypes[Math.floor(Math.random() * productTypes.length)],
                title: `Product ${rowIndex + 1}`,
                price: `$${(Math.random() * 50 + 10).toFixed(2)}`,
                sales: Math.floor(Math.random() * 1000),
                revenue: `$${(Math.random() * 10000).toFixed(2)}`
            });
        }
        
        return rows;
    }

    /**
     * Update button visibility based on selection state
     */
    updateButtonVisibility() {
        if (!this.elements.deleteBtn || !this.elements.exportBtn) return;
        
        const hasSelection = this.state.selectedRowKeys && this.state.selectedRowKeys.size > 0;
        
        if (hasSelection) {
            this.elements.deleteBtn.style.display = 'flex';
            this.elements.exportBtn.style.display = 'flex';
        } else {
            this.elements.deleteBtn.style.display = 'none';
            this.elements.exportBtn.style.display = 'none';
        }
    }

    /**
     * Check if there are any active filters by looking for filter indicators
     */
    hasActiveFilters() {
        // Look for filter indicators (dots) on column headers
        const filterIndicators = this.container.querySelectorAll('.snap-grid-filter-indicator');
        const hasIndicators = filterIndicators.length > 0;
        
        console.log('🔍 Checking for filter indicators:', filterIndicators.length, 'found');
        return hasIndicators;
    }

    /**
     * Clear all column filters and reset to default
     */
    clearAllFilters() {
        if (!this.hasActiveFilters()) {
            return; // No filters to clear
        }

        console.log('🧹 Clearing all filters...');

        // Find all columns with filter indicators and reset them
        const filterIndicators = this.container.querySelectorAll('.snap-grid-filter-indicator');
        const columnsToReset = [];
        
        filterIndicators.forEach(indicator => {
            // Find the column header that contains this indicator
            const headerCell = indicator.closest('.snap-grid-header-cell');
            if (headerCell) {
                const columnField = headerCell.getAttribute('data-field');
                if (columnField) {
                    columnsToReset.push(columnField);
                }
            }
        });

        console.log('🧹 Columns to reset:', columnsToReset);

        // Clear filters for each column
        columnsToReset.forEach(columnField => {
            this.clearColumnFilter(columnField);
        });
        
        this.emit('filtersCleared', {});
    }

    /**
     * Clear filter for a specific column
     */
    clearColumnFilter(columnField) {
        console.log('🧹 Clearing filter for column:', columnField);
        
        // Use the existing clearFilter method which handles everything properly
        this.clearFilter(columnField);
    }



    /**
     * Update clear button state based on active filters
     */
    updateClearButtonState() {
        if (!this.elements.clearFiltersBtn) return;
        
        const hasFilters = this.hasActiveFilters();
        
        if (hasFilters) {
            this.elements.clearFiltersBtn.disabled = false;
            this.elements.clearFiltersBtn.removeAttribute('disabled');
        } else {
            this.elements.clearFiltersBtn.disabled = true;
            this.elements.clearFiltersBtn.setAttribute('disabled', 'true');
        }
    }

    /**
     * Update footer statistics
     */
    updateFooterStats() {
        if (!this.state || !this.elements.footerStats) {
            console.log('Footer stats not available:', { state: !!this.state, footerStats: !!this.elements.footerStats });
            return;
        }
        
        const totalRows = this.state.data ? this.state.data.length : 0;
        const filteredRows = this.state.filteredData ? this.state.filteredData.length : 0;
        const selectedRows = this.state.selectedRowKeys ? this.state.selectedRowKeys.size : 0;

        console.log('Updating footer stats:', { totalRows, filteredRows, selectedRows });

        const statItems = this.elements.footerStats.querySelectorAll('.snap-grid-stat-item');
        console.log('Found stat items:', statItems.length);
        
        if (statItems[0]) {
            const valueElement = statItems[0].querySelector('.snap-grid-stat-value');
            if (valueElement) {
                valueElement.textContent = totalRows.toLocaleString();
                console.log('Updated ROWS value:', valueElement.textContent);
            }
        }
        if (statItems[1]) {
            const valueElement = statItems[1].querySelector('.snap-grid-stat-value');
            if (valueElement) {
                valueElement.textContent = filteredRows.toLocaleString();
                console.log('Updated FILTERED value:', valueElement.textContent);
            }
        }
        if (statItems[2]) {
            const valueElement = statItems[2].querySelector('.snap-grid-stat-value');
            if (valueElement) {
                valueElement.textContent = selectedRows.toLocaleString();
                console.log('Updated SELECTED value:', valueElement.textContent);
            }
        }
    }

    /**
     * Delete selected rows
     */
    deleteSelectedRows() {
        if (this.state.selectedRowKeys.size === 0) {
            console.log('No rows selected for deletion');
            return;
        }

        const selectedKeys = Array.from(this.state.selectedRowKeys);
        const selectedData = this.getSelectedData();
        
        // Remove selected rows from data
        this.state.data = this.state.data.filter(row => {
            const rowKey = this.getRowId(row);
            return !this.state.selectedRowKeys.has(rowKey);
        });

        // Clear selection
        this.state.selectedRowKeys.clear();
        
        // Reprocess data and re-render
        this.processData();
        this.render();
        
        // Emit event
        this.emit('rowsDeleted', { 
            deletedKeys: selectedKeys, 
            deletedData: selectedData 
        });
        
        console.log(`Deleted ${selectedKeys.length} rows`);
    }

    /**
     * Export data
     */
    exportData() {
        const dataToExport = this.state.filteredData.length > 0 ? this.state.filteredData : this.state.data;
        
        if (dataToExport.length === 0) {
            console.log('No data to export');
            return;
        }

        // Convert data to CSV
        const csvContent = this.convertToCSV(dataToExport);
        
        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `snap-grid-export-${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Emit event
        this.emit('dataExported', { 
            rowCount: dataToExport.length,
            exportType: 'csv'
        });
        
        console.log(`Exported ${dataToExport.length} rows to CSV`);
    }

    /**
     * Setup export dropdown functionality
     */
    setupExportDropdown() {
        if (!this.elements.exportBtn) return;

        // Create dropdown container
        const dropdown = document.createElement('div');
        dropdown.className = 'snap-dropdown export-dropdown';
        dropdown.style.position = 'relative';
        dropdown.style.display = 'inline-block';

        // Create trigger button (reuse existing export button styling)
        const trigger = this.elements.exportBtn.cloneNode(true);
        trigger.className = 'snap-grid-export-btn';
        trigger.style.position = 'relative';

        // Create dropdown menu
        const menu = document.createElement('div');
        menu.className = 'dropdown-menu hidden';
        menu.style.position = 'absolute';
        menu.style.top = 'calc(100% + 4px)';
        menu.style.left = '0';
        menu.style.zIndex = '1000';
        menu.style.minWidth = '200px';

        // Create menu items
        const items = [
            { text: 'Export Current (CSV)', action: 'export-current' },
            { text: 'Export ASINs (CSV)', action: 'export-asins' },
            { text: 'Copy ASINs to Clipboard', action: 'copy-asins' }
        ];

        items.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'dropdown-item';
            itemElement.textContent = item.text;
            itemElement.setAttribute('data-action', item.action);
            itemElement.style.padding = '8px 12px';
            itemElement.style.cursor = 'pointer';
            itemElement.style.fontFamily = 'Amazon Ember, sans-serif';
            itemElement.style.fontWeight = '500';
            itemElement.style.fontSize = '12px';
            itemElement.style.color = 'var(--text-primary)';
            
            // Hover effect
            itemElement.addEventListener('mouseenter', () => {
                itemElement.style.background = '#F3F4F6';
            });
            itemElement.addEventListener('mouseleave', () => {
                itemElement.style.background = 'transparent';
            });

            // Click handler
            itemElement.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handleExportAction(item.action);
                menu.classList.add('hidden');
                dropdown.classList.remove('focused');
            });

            menu.appendChild(itemElement);
        });

        // Replace original button with dropdown
        this.elements.exportBtn.parentNode.replaceChild(dropdown, this.elements.exportBtn);
        dropdown.appendChild(trigger);
        dropdown.appendChild(menu);

        // Update reference
        this.elements.exportBtn = trigger;
        this.elements.exportDropdown = dropdown;

        // Toggle dropdown on click
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();
            
            const isOpen = !menu.classList.contains('hidden');
            
            // Close all other dropdowns
            document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                d.classList.remove('focused');
                d.querySelector('.dropdown-menu').classList.add('hidden');
            });

            if (!isOpen) {
                menu.classList.remove('hidden');
                dropdown.classList.add('focused');
            } else {
                menu.classList.add('hidden');
                dropdown.classList.remove('focused');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!dropdown.contains(e.target)) {
                menu.classList.add('hidden');
                dropdown.classList.remove('focused');
            }
        });
    }

    /**
     * Handle export action based on selection
     */
    handleExportAction(action) {
        const selectedData = this.getSelectedData();
        
        if (selectedData.length === 0) {
            console.log('No rows selected for export');
            this.showNotification('Please select rows to export', 'warning');
            return;
        }

        switch (action) {
            case 'export-current':
                this.exportCurrentData(selectedData);
                break;
            case 'export-asins':
                this.exportASINs(selectedData);
                break;
            case 'copy-asins':
                this.copyASINsToClipboard(selectedData);
                break;
        }
    }

    /**
     * Export current visible columns (excluding checkbox, preview, actions)
     */
    exportCurrentData(selectedData) {
        if (selectedData.length === 0) {
            console.log('No data to export');
            return;
        }

        // Get visible columns excluding checkbox, preview, and actions
        const visibleColumns = this.config.columns.filter(col => 
            !this.state.hiddenColumns.has(col.field) && 
            col.field !== 'checkbox' && 
            col.field !== 'preview' && 
            col.field !== 'actions'
        );
        
        // Convert to CSV
        const csvContent = this.convertToCSV(selectedData, visibleColumns);
        
        // Create and download file
        this.downloadCSV(csvContent, `snap-grid-current-export-${new Date().toISOString().split('T')[0]}.csv`);
        
        console.log(`Exported ${selectedData.length} selected rows to CSV`);
        this.showNotification(`Successfully exported ${selectedData.length} rows to CSV`, 'success');
    }

    /**
     * Export only ASINs
     */
    exportASINs(selectedData) {
        if (selectedData.length === 0) {
            console.log('No data to export');
            return;
        }

        // Extract ASINs
        const asins = selectedData.map(row => row.ASIN || row.asin || '').filter(asin => asin);
        
        if (asins.length === 0) {
            console.log('No ASINs found in selected data');
            this.showNotification('No ASINs found in selected data', 'warning');
            return;
        }

        // Create CSV with ASIN header
        const csvContent = 'ASIN\n' + asins.join('\n');
        
        // Create and download file
        this.downloadCSV(csvContent, `snap-grid-asins-export-${new Date().toISOString().split('T')[0]}.csv`);
        
        console.log(`Exported ${asins.length} ASINs to CSV`);
        this.showNotification(`Successfully exported ${asins.length} ASINs to CSV`, 'success');
    }

    /**
     * Copy ASINs to clipboard
     */
    async copyASINsToClipboard(selectedData) {
        if (selectedData.length === 0) {
            console.log('No data to copy');
            return;
        }

        // Extract ASINs
        const asins = selectedData.map(row => row.ASIN || row.asin || '').filter(asin => asin);
        
        if (asins.length === 0) {
            console.log('No ASINs found in selected data');
            this.showNotification('No ASINs found in selected data', 'warning');
            return;
        }

        try {
            await navigator.clipboard.writeText(asins.join('\n'));
            console.log(`Copied ${asins.length} ASINs to clipboard`);
            this.showNotification(`Successfully copied ${asins.length} ASINs to clipboard`, 'success');
        } catch (err) {
            console.error('Failed to copy ASINs to clipboard:', err);
            // Fallback: create a temporary textarea
            const textarea = document.createElement('textarea');
            textarea.value = asins.join('\n');
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            console.log(`Copied ${asins.length} ASINs to clipboard (fallback method)`);
            this.showNotification(`Successfully copied ${asins.length} ASINs to clipboard`, 'success');
        }
    }

    /**
     * Download CSV file
     */
    downloadCSV(csvContent, filename) {
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    /**
     * Setup delete dropdown functionality
     */
    setupDeleteDropdown() {
        if (!this.elements.deleteBtn) return;

        // Create dropdown container
        const dropdown = document.createElement('div');
        dropdown.className = 'snap-dropdown delete-dropdown';
        dropdown.style.position = 'relative';
        dropdown.style.display = 'inline-block';

        // Create trigger button (reuse existing delete button styling)
        const trigger = this.elements.deleteBtn.cloneNode(true);
        trigger.className = 'snap-grid-delete-btn';
        trigger.style.position = 'relative';

        // Create dropdown menu
        const menu = document.createElement('div');
        menu.className = 'dropdown-menu hidden';
        menu.style.position = 'absolute';
        menu.style.top = 'calc(100% + 4px)';
        menu.style.left = '0';
        menu.style.zIndex = '1000';
        menu.style.minWidth = '200px';

        // Create menu items
        const items = [
            { text: 'Delete Product', action: 'delete-product' },
            { text: 'Delete Design', action: 'delete-design' },
            { text: 'Delete From Database Only', action: 'delete-database' }
        ];

        items.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'dropdown-item';
            itemElement.textContent = item.text;
            itemElement.setAttribute('data-action', item.action);
            itemElement.style.padding = '8px 12px';
            itemElement.style.cursor = 'pointer';
            itemElement.style.fontFamily = 'Amazon Ember, sans-serif';
            itemElement.style.fontWeight = '500';
            itemElement.style.fontSize = '12px';
            itemElement.style.color = 'var(--text-primary)';
            
            // Hover effect
            itemElement.addEventListener('mouseenter', () => {
                itemElement.style.background = '#F3F4F6';
            });
            itemElement.addEventListener('mouseleave', () => {
                itemElement.style.background = 'transparent';
            });

            // Click handler
            itemElement.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handleDeleteAction(item.action);
                menu.classList.add('hidden');
                dropdown.classList.remove('focused');
            });

            menu.appendChild(itemElement);
        });

        // Replace original button with dropdown
        this.elements.deleteBtn.parentNode.replaceChild(dropdown, this.elements.deleteBtn);
        dropdown.appendChild(trigger);
        dropdown.appendChild(menu);

        // Update reference
        this.elements.deleteBtn = trigger;
        this.elements.deleteDropdown = dropdown;

        // Toggle dropdown on click
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();
            
            const isOpen = !menu.classList.contains('hidden');
            
            // Close all other dropdowns
            document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                d.classList.remove('focused');
                d.querySelector('.dropdown-menu').classList.add('hidden');
            });

            if (!isOpen) {
                menu.classList.remove('hidden');
                dropdown.classList.add('focused');
            } else {
                menu.classList.add('hidden');
                dropdown.classList.remove('focused');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!dropdown.contains(e.target)) {
                menu.classList.add('hidden');
                dropdown.classList.remove('focused');
            }
        });
    }

    /**
     * Handle delete action based on selection
     */
    handleDeleteAction(action) {
        const selectedData = this.getSelectedData();
        
        if (selectedData.length === 0) {
            this.showNotification('Please select rows to delete', 'warning');
            return;
        }

        switch (action) {
            case 'delete-product':
                this.showDeleteProductConfirmation(selectedData);
                break;
            case 'delete-design':
                this.showDeleteDesignConfirmation(selectedData);
                break;
            case 'delete-database':
                this.showDeleteDatabaseConfirmation(selectedData);
                break;
        }
    }

    /**
     * Show delete product confirmation dialog
     */
    showDeleteProductConfirmation(selectedData) {
        // Calculate total sales and reviews for selected products
        const totalSales = selectedData.reduce((sum, row) => sum + (parseInt(row.sales) || 0), 0);
        const totalReviews = selectedData.reduce((sum, row) => sum + (parseInt(row.reviews) || 0), 0);
        const productCount = selectedData.length;
        const isPlural = productCount > 1;
        const productText = isPlural ? 'products' : 'product';

        const dialog = document.createElement('div');
        dialog.className = 'delete-confirmation-dialog';
        dialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;

        dialog.innerHTML = `
            <div style="
                background: var(--bg-primary);
                border: 1px solid var(--border-color);
                border-radius: 8px;
                padding: 24px;
                max-width: 500px;
                width: 90%;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            ">
                <h3 style="margin: 0 0 16px 0; color: var(--text-primary); font-size: 18px; font-weight: 600; font-family: 'Amazon Ember', sans-serif;">
                    Delete ${productText.charAt(0).toUpperCase() + productText.slice(1)}${isPlural ? 's' : ''}
                </h3>
                <div style="margin-bottom: 16px; color: var(--text-secondary); font-family: 'Amazon Ember', sans-serif;">
                    <p><strong>${productCount}</strong> ${productText} selected for deletion</p>
                    <p><strong>${totalSales.toLocaleString()}</strong> total sales</p>
                    <p><strong>${totalReviews.toLocaleString()}</strong> total reviews</p>
                </div>
                <p style="color: var(--text-primary); margin-bottom: 16px; font-family: 'Amazon Ember', sans-serif;">
                    This action will permanently delete the selected ${productText} and all associated data. 
                    This cannot be undone.
                </p>
                <div style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 8px; color: var(--text-primary); font-weight: 500; font-family: 'Amazon Ember', sans-serif;">
                        Type <strong>SNAP</strong> to confirm deletion:
                    </label>
                    <input type="text" id="deleteProductConfirm" style="
                        width: 100%;
                        padding: var(--spacing-sm, 12px);
                        border: 1px solid var(--grid-border-color, #dce0e5);
                        border-radius: var(--border-radius-sm, 4px);
                        background: var(--bg-primary);
                        color: var(--text-primary);
                        font-size: var(--font-size-sm, 12px);
                        font-family: 'Amazon Ember', sans-serif;
                        box-sizing: border-box;
                    " placeholder="Type SNAP here">
                </div>
                <div style="display: flex; gap: 12px; justify-content: flex-end;">
                    <button id="cancelDeleteProduct" style="
                        padding: 8px 16px;
                        border: 1px solid var(--border-color);
                        border-radius: 4px;
                        background: var(--bg-secondary);
                        color: var(--text-primary);
                        cursor: pointer;
                        font-family: 'Amazon Ember', sans-serif;
                        font-size: 14px;
                    ">Cancel</button>
                    <button id="confirmDeleteProduct" style="
                        padding: 8px 16px;
                        border: none;
                        border-radius: 4px;
                        background: #dc3545;
                        color: white;
                        cursor: pointer;
                        opacity: 0.5;
                        font-family: 'Amazon Ember', sans-serif;
                        font-size: 14px;
                    " disabled>Delete ${productText.charAt(0).toUpperCase() + productText.slice(1)}${isPlural ? 's' : ''}</button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        const confirmInput = dialog.querySelector('#deleteProductConfirm');
        const cancelBtn = dialog.querySelector('#cancelDeleteProduct');
        const confirmBtn = dialog.querySelector('#confirmDeleteProduct');

        // Enable/disable confirm button based on input
        confirmInput.addEventListener('input', (e) => {
            const isValid = e.target.value === 'SNAP';
            confirmBtn.disabled = !isValid;
            confirmBtn.style.opacity = isValid ? '1' : '0.5';
        });

        // Cancel button
        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(dialog);
        });

        // Confirm button
        confirmBtn.addEventListener('click', () => {
            if (confirmInput.value === 'SNAP') {
                this.deleteSelectedRows();
                this.showNotification(`Successfully deleted ${productCount} ${productText}`, 'success');
                document.body.removeChild(dialog);
            }
        });

        // Close on escape key
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                document.body.removeChild(dialog);
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);

        // Focus input
        confirmInput.focus();
    }

    /**
     * Show delete design confirmation dialog
     */
    showDeleteDesignConfirmation(selectedData) {
        // Calculate design statistics
        const designCount = new Set(selectedData.map(row => row.designId || row.design)).size;
        const totalProducts = selectedData.length;
        const totalSales = selectedData.reduce((sum, row) => sum + (parseInt(row.sales) || 0), 0);
        const totalReviews = selectedData.reduce((sum, row) => sum + (parseInt(row.reviews) || 0), 0);
        
        // Calculate products with sales for this design
        const productsWithSales = selectedData.filter(row => (parseInt(row.sales) || 0) > 0).length;
        const salesProductsText = productsWithSales === 1 ? 'product has sales' : 'products have sales';
        
        const isPlural = designCount > 1;
        const designText = isPlural ? 'designs' : 'design';

        const dialog = document.createElement('div');
        dialog.className = 'delete-confirmation-dialog';
        dialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;

        dialog.innerHTML = `
            <div style="
                background: var(--bg-primary);
                border: 1px solid var(--border-color);
                border-radius: 8px;
                padding: 24px;
                max-width: 500px;
                width: 90%;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            ">
                <h3 style="margin: 0 0 16px 0; color: var(--text-primary); font-size: 18px; font-weight: 600; font-family: 'Amazon Ember', sans-serif;">
                    Delete ${designText.charAt(0).toUpperCase() + designText.slice(1)}${isPlural ? 's' : ''}
                </h3>
                <div style="margin-bottom: 16px; color: var(--text-secondary); font-family: 'Amazon Ember', sans-serif;">
                    <p><strong>${designCount}</strong> ${designText} selected for deletion</p>
                    <p><strong>${totalProducts}</strong> product${totalProducts > 1 ? 's' : ''} using ${designCount > 1 ? 'these' : 'this'} ${designText}</p>
                    <p><strong>${productsWithSales}</strong> ${salesProductsText} with ${designCount > 1 ? 'these' : 'this'} ${designText}</p>
                    <p><strong>${totalSales.toLocaleString()}</strong> total sales generated</p>
                    <p><strong>${totalReviews.toLocaleString()}</strong> total reviews</p>
                </div>
                <p style="color: var(--text-primary); margin-bottom: 16px; font-family: 'Amazon Ember', sans-serif;">
                    This action will permanently delete the selected ${designText} and ALL associated products. 
                    This cannot be undone.
                </p>
                <div style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 8px; color: var(--text-primary); font-weight: 500; font-family: 'Amazon Ember', sans-serif;">
                        Type <strong>SNAP</strong> to confirm deletion:
                    </label>
                    <input type="text" id="deleteDesignConfirm" style="
                        width: 100%;
                        padding: var(--spacing-sm, 12px);
                        border: 1px solid var(--grid-border-color, #dce0e5);
                        border-radius: var(--border-radius-sm, 4px);
                        background: var(--bg-primary);
                        color: var(--text-primary);
                        font-size: var(--font-size-sm, 12px);
                        font-family: 'Amazon Ember', sans-serif;
                        box-sizing: border-box;
                    " placeholder="Type SNAP here">
                </div>
                <div style="display: flex; gap: 12px; justify-content: flex-end;">
                    <button id="cancelDeleteDesign" style="
                        padding: 8px 16px;
                        border: 1px solid var(--border-color);
                        border-radius: 4px;
                        background: var(--bg-secondary);
                        color: var(--text-primary);
                        cursor: pointer;
                        font-family: 'Amazon Ember', sans-serif;
                        font-size: 14px;
                    ">Cancel</button>
                    <button id="confirmDeleteDesign" style="
                        padding: 8px 16px;
                        border: none;
                        border-radius: 4px;
                        background: #dc3545;
                        color: white;
                        cursor: pointer;
                        opacity: 0.5;
                        font-family: 'Amazon Ember', sans-serif;
                        font-size: 14px;
                    " disabled>Delete ${designText.charAt(0).toUpperCase() + designText.slice(1)}${isPlural ? 's' : ''}</button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        const confirmInput = dialog.querySelector('#deleteDesignConfirm');
        const cancelBtn = dialog.querySelector('#cancelDeleteDesign');
        const confirmBtn = dialog.querySelector('#confirmDeleteDesign');

        // Enable/disable confirm button based on input
        confirmInput.addEventListener('input', (e) => {
            const isValid = e.target.value === 'SNAP';
            confirmBtn.disabled = !isValid;
            confirmBtn.style.opacity = isValid ? '1' : '0.5';
        });

        // Cancel button
        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(dialog);
        });

        // Confirm button
        confirmBtn.addEventListener('click', () => {
            if (confirmInput.value === 'SNAP') {
                this.deleteSelectedRows();
                this.showNotification(`Successfully deleted ${designCount} ${designText} and ${totalProducts} associated product${totalProducts > 1 ? 's' : ''}`, 'success');
                document.body.removeChild(dialog);
            }
        });

        // Close on escape key
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                document.body.removeChild(dialog);
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);

        // Focus input
        confirmInput.focus();
    }

    /**
     * Show delete from database only confirmation dialog
     */
    showDeleteDatabaseConfirmation(selectedData) {
        const productCount = selectedData.length;
        const isPlural = productCount > 1;
        const rowText = isPlural ? 'rows' : 'row';

        const dialog = document.createElement('div');
        dialog.className = 'delete-confirmation-dialog';
        dialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;

        dialog.innerHTML = `
            <div style="
                background: var(--bg-primary);
                border: 1px solid var(--border-color);
                border-radius: 8px;
                padding: 24px;
                max-width: 400px;
                width: 90%;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            ">
                <h3 style="margin: 0 0 16px 0; color: var(--text-primary); font-size: 18px; font-weight: 600; font-family: 'Amazon Ember', sans-serif;">
                    Delete From Database Only
                </h3>
                <p style="color: var(--text-primary); margin-bottom: 16px; font-family: 'Amazon Ember', sans-serif;">
                    This will remove <strong>${productCount}</strong> selected ${rowText} from the database only. 
                    The data will be permanently deleted and cannot be recovered.
                </p>
                <div style="display: flex; gap: 12px; justify-content: flex-end;">
                    <button id="cancelDeleteDatabase" style="
                        padding: 8px 16px;
                        border: 1px solid var(--border-color);
                        border-radius: 4px;
                        background: var(--bg-secondary);
                        color: var(--text-primary);
                        cursor: pointer;
                        font-family: 'Amazon Ember', sans-serif;
                        font-size: 14px;
                    ">Cancel</button>
                    <button id="confirmDeleteDatabase" style="
                        padding: 8px 16px;
                        border: none;
                        border-radius: 4px;
                        background: #dc3545;
                        color: white;
                        cursor: pointer;
                        font-family: 'Amazon Ember', sans-serif;
                        font-size: 14px;
                    ">Delete</button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        const cancelBtn = dialog.querySelector('#cancelDeleteDatabase');
        const confirmBtn = dialog.querySelector('#confirmDeleteDatabase');

        // Cancel button
        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(dialog);
        });

        // Confirm button
        confirmBtn.addEventListener('click', () => {
            this.deleteSelectedRows();
            this.showNotification(`Successfully deleted ${productCount} ${rowText} from database`, 'success');
            document.body.removeChild(dialog);
        });

        // Close on escape key
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                document.body.removeChild(dialog);
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);
    }

    /**
     * Convert data to CSV format
     */
    convertToCSV(data, columns = null) {
        if (data.length === 0) return '';
        
        // Get visible columns (use provided columns or default logic)
        const visibleColumns = columns || this.config.columns.filter(col => 
            !this.state.hiddenColumns.has(col.field) && col.field !== 'checkbox'
        );
        
        // Create header row
        const headers = visibleColumns.map(col => col.headerName || col.field);
        const csvRows = [headers.join(',')];
        
        // Create data rows
        data.forEach(row => {
            const values = visibleColumns.map(col => {
                let value = row[col.field] || '';
                
                // Escape commas and quotes in CSV
                if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                    value = `"${value.replace(/"/g, '""')}"`;
                }
                
                return value;
            });
            csvRows.push(values.join(','));
        });
        
        return csvRows.join('\n');
    }

    /**
     * Setup virtual scrolling
     */
    setupVirtualScrolling() {
        if (!this.config.virtualScrolling) return;

        this.virtualScroller = {
            rowHeight: this.config.rowHeight,
            visibleRows: Math.ceil(this.elements.viewport.clientHeight / this.config.rowHeight) + 2,
            totalRows: this.state.displayData.length,
            scrollTop: 0,
            startIndex: 0,
            endIndex: 0
        };

        this.updateVirtualScrolling();
        this.compensatePinnedColumnsHeight();
    }

    /**
     * Update virtual scrolling calculations
     */
    updateVirtualScrolling() {
        if (!this.config.virtualScrolling) return;

        const { rowHeight } = this.virtualScroller;
        const scrollTop = this.elements.viewport.scrollTop;
        const visibleHeight = this.elements.viewport.clientHeight;

        this.virtualScroller.startIndex = Math.floor(scrollTop / rowHeight);
        this.virtualScroller.endIndex = Math.min(
            this.virtualScroller.startIndex + Math.ceil(visibleHeight / rowHeight) + 2,
            this.state.displayData.length
        );

        // Update canvas height for proper scrollbar
        const totalHeight = this.state.displayData.length * rowHeight;
        // Ensure minimum height of 1px to maintain horizontal scrollbar even with 0 data
        this.elements.canvas.style.height = `${Math.max(totalHeight, 1)}px`;

        this.state.visibleRange = {
            start: this.virtualScroller.startIndex,
            end: this.virtualScroller.endIndex
        };

        // Column virtualization for horizontal performance
        if (this.config.virtualizeColumns) {
            this.updateColumnVirtualization();
        }
    }

    /**
     * Update column virtualization calculations
     */
    updateColumnVirtualization() {
        if (!this.config.virtualizeColumns) return;

        const scrollLeft = this.state.scrollLeft;
        const visibleWidth = this.elements.viewport.clientWidth;

        // Calculate visible column range based on scroll position and column widths
        let cumulativeWidth = 0;
        let startColIndex = 0;
        let endColIndex = this.config.columns.length - 1;

        // Find start column
        for (let i = 0; i < this.config.columns.length; i++) {
            const colWidth = this.getColumnWidth(i);
            if (cumulativeWidth + colWidth > scrollLeft) {
                startColIndex = Math.max(0, i - 1); // Include one column before for smooth scrolling
                break;
            }
            cumulativeWidth += colWidth;
        }

        // Find end column
        cumulativeWidth = 0;
        for (let i = 0; i < this.config.columns.length; i++) {
            const colWidth = this.getColumnWidth(i);
            cumulativeWidth += colWidth;
            if (cumulativeWidth > scrollLeft + visibleWidth) {
                endColIndex = Math.min(this.config.columns.length - 1, i + 1); // Include one column after
                break;
            }
        }

        this.state.visibleColumnRange = {
            start: startColIndex,
            end: endColIndex
        };
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Scroll handling
        this.boundHandlers.set('scroll', this.handleScroll.bind(this));
        this.elements.viewport.addEventListener('scroll', this.boundHandlers.get('scroll'), { passive: true });

        // Prevent independent scrolling of pinned columns - sync with main viewport
        this.boundHandlers.set('pinnedScroll', this.handlePinnedScroll.bind(this));
        if (this.elements.pinnedLeft) {
            this.elements.pinnedLeft.addEventListener('scroll', this.boundHandlers.get('pinnedScroll'), { passive: true });
        }
        if (this.elements.pinnedRight) {
            this.elements.pinnedRight.addEventListener('scroll', this.boundHandlers.get('pinnedScroll'), { passive: true });
        }

        // Resize handling
        this.boundHandlers.set('resize', this.handleResize.bind(this));
        window.addEventListener('resize', this.boundHandlers.get('resize'));

        // Click handling
        this.boundHandlers.set('click', this.handleClick.bind(this));
        this.container.addEventListener('click', this.boundHandlers.get('click'));

        // Keyboard handling
        this.boundHandlers.set('keydown', this.handleKeydown.bind(this));
        this.container.addEventListener('keydown', this.boundHandlers.get('keydown'));


    }



    /**
     * Setup performance monitoring
     */
    setupPerformanceMonitoring() {
        if (!this.config.performance) return;

        // Monitor memory usage
        if (window.performance && window.performance.memory) {
            this.performance.memoryUsage = window.performance.memory.usedJSHeapSize;
        }

        // Setup performance observer for long tasks
        if (window.PerformanceObserver) {
            try {
                const observer = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.duration > 50) {
                            console.warn('SnapGrid: Long task detected:', entry.duration + 'ms');
                        }
                    }
                });
                observer.observe({ entryTypes: ['longtask'] });
            } catch (e) {
                // PerformanceObserver not supported
            }
        }
    }

    /**
     * Render the grid
     */
    render() {
        const startTime = performance.now();

        try {
            this.renderHeader();
            this.renderBody();
            this.renderFooter();

            // Update header and footer info
            this.updateLoadedInfo();
            this.updateFooterStats();
            this.updateButtonVisibility();
            this.updateClearButtonState();

            // Compensate for scrollbar after rendering to ensure proper alignment
            this.compensateHeaderScrollbar();
            this.compensatePinnedColumnsHeight();

            // ENHANCED: Preserve both horizontal and vertical scroll positions
            // This ensures the user stays at the same column/row view when filters result in no data
            // Use multiple attempts to ensure scroll position is restored
            const restoreScrollPosition = () => {
                let attempts = 0;
                const maxAttempts = 5;

                const tryRestore = () => {
                    attempts++;

                    // Restore horizontal scroll position
                    if (this.state.scrollLeft !== undefined) {
                        if (this.elements.headerViewport && this.elements.headerViewport.scrollLeft !== this.state.scrollLeft) {
                            this.elements.headerViewport.scrollLeft = this.state.scrollLeft;
                            console.log(`📍 Attempt ${attempts}: Restored header scroll to:`, this.state.scrollLeft);
                        }
                        if (this.elements.viewport && this.elements.viewport.scrollLeft !== this.state.scrollLeft) {
                            this.elements.viewport.scrollLeft = this.state.scrollLeft;
                            console.log(`📍 Attempt ${attempts}: Restored viewport horizontal scroll to:`, this.state.scrollLeft);
                        }
                    }

                    // Restore vertical scroll position
                    if (this.state.scrollTop !== undefined && this.elements.viewport && this.elements.viewport.scrollTop !== this.state.scrollTop) {
                        this.elements.viewport.scrollTop = this.state.scrollTop;
                        console.log(`📍 Attempt ${attempts}: Restored viewport vertical scroll to:`, this.state.scrollTop);
                    }

                    // Check if we need to try again
                    const horizontalRestored = !this.state.scrollLeft || this.elements.viewport?.scrollLeft === this.state.scrollLeft;
                    const verticalRestored = !this.state.scrollTop || this.elements.viewport?.scrollTop === this.state.scrollTop;

                    if ((!horizontalRestored || !verticalRestored) && attempts < maxAttempts) {
                        setTimeout(tryRestore, 10); // Try again in 10ms
                    }
                };

                tryRestore();
            };

            // Start restoration immediately and also after a delay
            setTimeout(restoreScrollPosition, 0);
            setTimeout(restoreScrollPosition, 50);

            // Update filter indicators after rendering
            this.updateFilterIndicators();

            // Refresh active menu target after any render to prevent positioning issues
            // This ensures menu positioning works correctly after DOM recreation
            this.refreshActiveMenuTarget();

            this.performance.renderTime = performance.now() - startTime;
            this.emit('rendered', { renderTime: this.performance.renderTime });
        } catch (error) {
            console.error('SnapGrid render failed:', error);
            this.showError('Render failed: ' + error.message);
        }
    }

    /**
     * Render grid header
     */
    renderHeader() {
        // Clear all header sections
        this.elements.headerPinnedLeft.innerHTML = '';
        this.elements.headerRow.innerHTML = '';
        this.elements.headerPinnedRight.innerHTML = '';

        // Set height for all header sections
        const height = `${this.config.headerHeight}px`;
        this.elements.headerPinnedLeft.style.height = height;
        this.elements.headerViewport.style.height = height;
        this.elements.headerPinnedRight.style.height = height;

        const orderedColumns = this.getOrderedColumns();
        orderedColumns.forEach((column, index) => {
            const headerCell = this.createHeaderCell(column, index);

            // Distribute header cells to appropriate sections
            if (this.state.pinnedColumns.left.includes(column.field)) {
                this.elements.headerPinnedLeft.appendChild(headerCell);
            } else if (this.state.pinnedColumns.right.includes(column.field)) {
                this.elements.headerPinnedRight.appendChild(headerCell);
            } else {
                this.elements.headerRow.appendChild(headerCell);
            }
        });
    }

    /**
     * Get columns in current visual order (respects hidden columns)
     */
    getOrderedColumns() {
        // Start from columnOrder if present, otherwise use config order
        const order = (this.state.columnOrder && this.state.columnOrder.length)
            ? this.state.columnOrder
            : this.config.columns.map(c => c.field);

        // Map to actual column objects and filter out hidden ones
        const ordered = [];
        order.forEach(field => {
            const col = this.config.columns.find(c => c.field === field);
            if (col && !this.state.hiddenColumns.has(col.field)) {
                ordered.push(col);
            }
        });

        // Also include any columns not in order (fallback), excluding hidden
        this.config.columns.forEach(col => {
            if (!order.includes(col.field) && !this.state.hiddenColumns.has(col.field)) {
                ordered.push(col);
            }
        });

        return ordered;
    }

    /**
     * Create header cell
     */
    createHeaderCell(column) {
        const cell = document.createElement('div');
        cell.className = 'snap-grid-header-cell';
        cell.setAttribute('role', 'columnheader');
        cell.setAttribute('data-field', column.field);
        cell.style.width = `${this.state.columnWidths.get(column.field)}px`;

        // Apply pinning styles
        this.applyPinningStyles(cell, column.field);

        // Header content
        const content = document.createElement('div');
        content.className = 'snap-grid-header-content';

        // Special handling for checkbox header (global checkbox)
        if (column.field === 'checkbox') {
            const checkboxWrapper = document.createElement('div');
            checkboxWrapper.className = 'checkbox-wrapper';
            checkboxWrapper.style.display = 'flex';
            checkboxWrapper.style.alignItems = 'center';
            checkboxWrapper.style.justifyContent = 'center';
            checkboxWrapper.style.width = '100%';

            const checkboxImg = document.createElement('img');
            checkboxImg.className = 'checkbox-icon global-checkbox';
            checkboxImg.draggable = false;
            checkboxImg.style.width = '16px';
            checkboxImg.style.height = '16px';
            checkboxImg.style.cursor = 'pointer';

            // Determine checkbox state based on selection
            const totalRows = this.state.displayData.length;
            const selectedRows = this.state.selectedRowKeys.size;

            if (selectedRows === 0) {
                checkboxImg.src = './assets/uncheckedbox-ic.svg';
                checkboxImg.alt = 'Select All';
            } else if (selectedRows === totalRows) {
                checkboxImg.src = './assets/checkbox-ic.svg';
                checkboxImg.alt = 'Deselect All';
            } else {
                checkboxImg.src = './assets/indeterminate-ic.svg';
                checkboxImg.alt = 'Select All';
            }

            // Add click handler for select all/none
            checkboxImg.addEventListener('click', (e) => {
                e.stopPropagation();
                if (selectedRows === totalRows) {
                    this.clearSelection();
                } else {
                    this.selectAll();
                }
            });

            checkboxWrapper.appendChild(checkboxImg);
            content.appendChild(checkboxWrapper);
        } else {
            // Column title for regular columns
            const title = document.createElement('span');
            title.className = 'snap-grid-header-title';
            title.textContent = column.headerName || column.field;
            content.appendChild(title);
        }

        // Sort indicator
        if (this.config.sortable && column.sortable !== false) {
            const sortIndicator = this.createSortIndicator(column.field);
            content.appendChild(sortIndicator);
        }

        // Column menu button (hide for fixed columns)
        const columnType = this.getColumnType(column.field);
        if ((this.config.filterable || this.config.sortable || this.config.groupable) && columnType !== 'fixed') {
            const menuButton = this.createColumnMenuButton(column);
            content.appendChild(menuButton);
        }

        cell.appendChild(content);

        // Resize handle (not for fixed columns)
        if (this.config.resizable && column.resizable !== false && columnType !== 'fixed') {
            const resizeHandle = this.createResizeHandle(column.field);
            cell.appendChild(resizeHandle);
        }

        return cell;
    }

    /**
     * Create sort indicator
     */
    createSortIndicator(field) {
        const indicator = document.createElement('div');
        indicator.className = 'snap-grid-sort-indicator';

        const sortConfig = this.state.sortConfig.find(s => s.field === field);
        if (sortConfig) {
            indicator.className += ` sorted-${sortConfig.direction}`;
            // No innerHTML needed - CSS background images handle the icons
        }

        return indicator;
    }

    /**
     * Create column menu button
     */
    createColumnMenuButton(column) {
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'snap-grid-column-menu-container';
        buttonContainer.style.position = 'relative';
        buttonContainer.style.display = 'inline-block';

        const button = document.createElement('button');
        button.className = 'snap-grid-column-menu-btn';
        button.setAttribute('aria-label', `Column menu for ${column.headerName || column.field}`);
        button.innerHTML = '⋮';

        button.addEventListener('click', (e) => {
            e.stopPropagation();
            this.showColumnMenu(column, buttonContainer);
        });

        buttonContainer.appendChild(button);

        // Add filter indicator if column has active filter
        if (this.state.filterConfig[column.field]) {
            const indicator = document.createElement('div');
            indicator.className = 'snap-grid-filter-indicator';
            indicator.style.position = 'absolute';
            indicator.style.top = '2px';
            indicator.style.right = '2px';
            indicator.style.width = '6px';
            indicator.style.height = '6px';
            indicator.style.borderRadius = '50%';
            indicator.style.background = 'var(--color-primary-600, #470CED)';
            indicator.style.pointerEvents = 'none';
            indicator.style.zIndex = '1';
            indicator.setAttribute('title', 'Filter applied');

            buttonContainer.appendChild(indicator);
        }

        return buttonContainer;
    }

    /**
     * Create resize handle
     */
    createResizeHandle(field) {
        const handle = document.createElement('div');
        handle.className = 'snap-grid-resize-handle';
        handle.setAttribute('data-field', field);

        let isResizing = false;
        let startX = 0;
        let startWidth = 0;
        let minWidthDuringResize = 80; // compute once per drag for performance

        handle.addEventListener('mousedown', (e) => {
            isResizing = true;
            startX = e.clientX;
            startWidth = this.state.columnWidths.get(field);

            // Compute min width once per drag to avoid heavy DOM measurements on every mousemove
            const column = this.config.columns.find(col => col.field === field);
            minWidthDuringResize = column ? this.calculateMinHeaderWidth(column) : 80;

            // Add resizing class to handle and grid container
            handle.classList.add('resizing');
            this.container.classList.add('resizing');

            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
            e.preventDefault();
        });

        const handleMouseMove = (e) => {
            if (!isResizing) return;

            const diff = e.clientX - startX;
            const newWidth = Math.max(minWidthDuringResize, startWidth + diff);

            // Update state immediately for smooth resizing
            this.state.columnWidths.set(field, newWidth);

            // Update DOM directly for immediate visual feedback
            this.updateColumnWidthDOM(field, newWidth);

            // Throttle the event emission to avoid too many events
            if (!this.resizeThrottleId) {
                this.resizeThrottleId = requestAnimationFrame(() => {
                    this.emit('columnResized', { field, width: newWidth });
                    this.resizeThrottleId = null;
                });
            }
        };

        const handleMouseUp = () => {
            isResizing = false;

            // Cancel any pending resize updates
            if (this.resizeThrottleId) {
                cancelAnimationFrame(this.resizeThrottleId);
                this.resizeThrottleId = null;
            }

            // Remove resizing class from handle and grid container
            handle.classList.remove('resizing');
            this.container.classList.remove('resizing');

            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };

        return handle;
    }


    /**
     * Render grid body
     */
    renderBody() {
        // Clear all containers
        this.elements.canvas.innerHTML = '';
        this.elements.pinnedLeft.innerHTML = '';
        this.elements.pinnedRight.innerHTML = '';

        // FIXED: Ensure horizontal scrollbar is always available when needed
        // Calculate total width of all columns to ensure horizontal scrolling works even with 0 data
        const totalColumnsWidth = this.config.columns.reduce((total, column, index) => {
            return total + this.getColumnWidth(index);
        }, 0);

        console.log('📏 Total columns width calculated:', totalColumnsWidth, 'px for horizontal scrolling');


        if (this.config.virtualScrolling) {
            this.renderVirtualRows();
        } else {
            this.renderAllRows();
        }

        // Handle canvas width for horizontal scrolling
        if (this.state.displayData.length === 0) {
            // Calculate width of non-pinned columns only (main scrollable area)
            const scrollableColumnsWidth = this.config.columns
                .filter(column => !column.pinned || column.pinned === 'none')
                .reduce((total, column) => {
                    const originalIndex = this.config.columns.indexOf(column);
                    return total + this.getColumnWidth(originalIndex);
                }, 0);

            // Set canvas width to match the scrollable columns width for empty state
            this.elements.canvas.style.width = `${scrollableColumnsWidth}px`;
            this.elements.canvas.style.minHeight = '1px'; // Ensure some height for scrollbar

            console.log('📄 Empty state: Canvas width set to', scrollableColumnsWidth, 'px for horizontal scrolling');
        } else {
            // IMPORTANT: Reset canvas width when data is present to allow natural layout
            this.elements.canvas.style.width = '';
            this.elements.canvas.style.minHeight = '';

            console.log('📄 Data present: Canvas width reset to natural layout');
        }
    }

    /**
     * Render virtual rows
     */
    renderVirtualRows() {
        const { startIndex, endIndex } = this.state.visibleRange;

        for (let i = startIndex; i < endIndex; i++) {
            const rowData = this.state.displayData[i];
            if (rowData) {
                const mainRow = this.createRow(rowData, i);
                // Apply virtual positioning to all row parts
                const leftRows = this.elements.pinnedLeft.querySelectorAll(`[data-row-index="${i}"]`);
                const rightRows = this.elements.pinnedRight.querySelectorAll(`[data-row-index="${i}"]`);

                [mainRow, ...leftRows, ...rightRows].forEach(row => {
                    if (row) {
                        row.style.transform = `translateY(${i * this.config.rowHeight}px)`;
                        row.style.position = 'absolute';
                        row.style.width = '100%';
                    }
                });
            }
        }
    }

    /**
     * Render all rows (non-virtual)
     */
    renderAllRows() {
        this.state.displayData.forEach((rowData, index) => {
            this.createRow(rowData, index);
        });
    }

    /**
     * Create a row element
     */
    createRow(rowData, index) {
        // Handle group headers
        if (rowData.__isGroupHeader) {
            return this.createGroupHeaderRow(rowData, index);
        }

        // Create separate row containers for pinned and scrollable columns
        const mainRow = document.createElement('div');
        mainRow.className = 'snap-grid-row';
        mainRow.setAttribute('role', 'row');
        mainRow.setAttribute('data-row-index', index);
        mainRow.style.height = `${this.config.rowHeight}px`;

        const leftRow = document.createElement('div');
        leftRow.className = 'snap-grid-row snap-grid-pinned-row';
        leftRow.setAttribute('role', 'row');
        leftRow.setAttribute('data-row-index', index);
        leftRow.style.height = `${this.config.rowHeight}px`;

        const rightRow = document.createElement('div');
        rightRow.className = 'snap-grid-row snap-grid-pinned-row';
        rightRow.setAttribute('role', 'row');
        rightRow.setAttribute('data-row-index', index);
        rightRow.style.height = `${this.config.rowHeight}px`;

        // Add row selection class
        const rowKey = this.getRowId(rowData);
        if (this.state.selectedRowKeys.has(rowKey)) {
            mainRow.classList.add('selected');
            leftRow.classList.add('selected');
            rightRow.classList.add('selected');
        }

        // Add alternating row class
        if (index % 2 === 1) {
            mainRow.classList.add('odd');
            leftRow.classList.add('odd');
            rightRow.classList.add('odd');
        }

        // Create cells and distribute them to appropriate containers
        this.getOrderedColumns().forEach((column, colIndex) => {
            const cell = this.createCell(rowData, column, index, colIndex);

            if (this.state.pinnedColumns.left.includes(column.field)) {
                leftRow.appendChild(cell);
            } else if (this.state.pinnedColumns.right.includes(column.field)) {
                rightRow.appendChild(cell);
            } else {
                mainRow.appendChild(cell);
            }
        });

        // Add unified hover event listeners to coordinate hover across all row parts
        const addHoverListeners = (rowElement) => {
            rowElement.addEventListener('mouseenter', () => {
                this.handleRowHover(index, true);
            });
            rowElement.addEventListener('mouseleave', () => {
                this.handleRowHover(index, false);
            });
        };

        addHoverListeners(mainRow);
        addHoverListeners(leftRow);
        addHoverListeners(rightRow);

        // Append rows to their respective containers
        this.elements.canvas.appendChild(mainRow);
        if (leftRow.children.length > 0) {
            this.elements.pinnedLeft.appendChild(leftRow);
        }
        if (rightRow.children.length > 0) {
            this.elements.pinnedRight.appendChild(rightRow);
        }

        return mainRow; // Return main row for compatibility
    }

    /**
     * Create a group header row
     */
    createGroupHeaderRow(groupData, index) {
        const row = document.createElement('div');
        row.className = 'snap-grid-row snap-grid-group-row';
        row.setAttribute('role', 'row');
        row.setAttribute('data-row-index', index);
        row.setAttribute('data-group-key', groupData.__groupKey);
        row.style.height = `${this.config.rowHeight}px`;

        const header = document.createElement('div');
        header.className = 'snap-grid-group-header';

        // Toggle button
        const toggle = document.createElement('span');
        toggle.className = `snap-grid-group-toggle ${groupData.__expanded ? 'expanded' : ''}`;
        toggle.innerHTML = '▶';

        // Group title
        const title = document.createElement('span');
        title.className = 'snap-grid-group-title';
        title.textContent = `${groupData.__groupField}: ${groupData.__groupKey}`;

        // Group count
        const count = document.createElement('span');
        count.className = 'snap-grid-group-count';
        count.textContent = `(${groupData.__groupCount} items)`;

        header.appendChild(toggle);
        header.appendChild(title);
        header.appendChild(count);
        row.appendChild(header);

        // Click handler for expand/collapse
        row.addEventListener('click', () => {
            this.toggleGroup(groupData.__groupKey);
        });

        return row;
    }

    /**
     * Toggle group expand/collapse
     */
    toggleGroup(groupKey) {
        if (this.state.expandedGroups.has(groupKey)) {
            this.state.expandedGroups.delete(groupKey);
        } else {
            this.state.expandedGroups.add(groupKey);
        }

        this.processData();
        this.render();
        this.emit('groupToggled', { groupKey, expanded: this.state.expandedGroups.has(groupKey) });
    }

    /**
     * Create a cell element
     */
    createCell(rowData, column, rowIndex, colIndex) {
        const cell = document.createElement('div');
        cell.className = 'snap-grid-cell';
        cell.setAttribute('role', 'gridcell');
        cell.setAttribute('data-field', column.field);
        cell.setAttribute('data-row-index', rowIndex);
        cell.setAttribute('data-col-index', colIndex);
        cell.style.width = `${this.state.columnWidths.get(column.field)}px`;

        // Apply pinning styles
        this.applyPinningStyles(cell, column.field);

        // Get cell value
        const value = this.getCellValue(rowData, column);

        // Render cell content
        const content = this.renderCellContent(value, column, rowData, rowIndex);
        cell.appendChild(content);

        // Add editable class
        if (this.config.editable && column.editable !== false) {
            cell.classList.add('editable');
            cell.setAttribute('tabindex', '0');
        }

        // Add click handler for checkbox cells to improve UX
        if (column.field === 'checkbox') {
            cell.style.cursor = 'pointer';
            cell.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleRowSelection(rowIndex);
            });
        }

        return cell;
    }

    /**
     * Get cell value from row data
     */
    getCellValue(rowData, column) {
        if (column.valueGetter) {
            return column.valueGetter({ data: rowData, column });
        }

        if (column.field) {
            return this.getNestedValue(rowData, column.field);
        }

        return '';
    }

    /**
     * Get nested value from object using dot notation
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : '';
        }, obj);
    }

    /**
     * Render cell content based on column type and renderer
     */
    renderCellContent(value, column, rowData, rowIndex) {
        const container = document.createElement('div');
        container.className = 'snap-grid-cell-content';

        // Use custom renderer if provided
        if (column.cellRenderer) {
            const rendererResult = column.cellRenderer({
                value,
                data: rowData,
                column,
                rowIndex
            });

            if (typeof rendererResult === 'string') {
                // Use centralized cell content helper for HTML sanitization
                this.setCellContent(container, rendererResult, true);
            } else if (rendererResult instanceof HTMLElement) {
                container.appendChild(rendererResult);
            } else {
                container.textContent = String(rendererResult);
            }

            return container;
        }

        // Special handling for checkbox column
        if (column.field === 'checkbox') {
            const checkboxWrapper = document.createElement('div');
            checkboxWrapper.className = 'checkbox-wrapper';
            checkboxWrapper.style.display = 'flex';
            checkboxWrapper.style.alignItems = 'center';
            checkboxWrapper.style.justifyContent = 'center';

            const checkboxImg = document.createElement('img');
            checkboxImg.className = 'checkbox-icon';
            checkboxImg.draggable = false;
            checkboxImg.style.width = '16px';
            checkboxImg.style.height = '16px';
            checkboxImg.style.cursor = 'pointer';

            const isSelected = this.state.selectedRowKeys.has(this.getRowId(rowData));
            checkboxImg.src = isSelected ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
            checkboxImg.alt = isSelected ? 'Checked' : 'Unchecked';

            // Add click handler for row selection
            checkboxImg.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleRowSelection(rowIndex);
            });

            checkboxWrapper.appendChild(checkboxImg);
            container.appendChild(checkboxWrapper);
            return container;
        }

        // Special handling for preview column - product image placeholder
        if (column.field === 'preview') {
            const previewSquare = document.createElement('div');
            previewSquare.className = 'preview-square';
            previewSquare.style.width = '32px';
            previewSquare.style.height = '32px';
            previewSquare.style.borderRadius = '6px';
            previewSquare.style.background = 'var(--bg-secondary)';
            previewSquare.style.cursor = 'pointer';
            previewSquare.style.transition = 'all 0.15s ease';
            previewSquare.style.margin = '0 auto'; // Center in cell

            previewSquare.addEventListener('click', (e) => {
                e.stopPropagation();
                this.emit('previewClicked', { data: rowData, rowIndex });
            });

            previewSquare.addEventListener('mouseenter', () => {
                previewSquare.style.background = 'var(--btn-hover)';
            });

            previewSquare.addEventListener('mouseleave', () => {
                previewSquare.style.background = 'var(--bg-secondary)';
            });

            container.appendChild(previewSquare);
            return container;
        }

        // Special handling for actions column
        if (column.field === 'actions') {
            const actionsContainer = document.createElement('div');
            actionsContainer.className = 'listing-edit-analyse-row';
            actionsContainer.style.display = 'flex';
            actionsContainer.style.gap = '8px';
            actionsContainer.style.alignItems = 'center';

            // Analyse icon
            const analyseIcon = document.createElement('span');
            analyseIcon.className = 'listing-analyse-ic';
            analyseIcon.innerHTML = '<img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" />';

            analyseIcon.addEventListener('click', (e) => {
                e.stopPropagation();
                this.emit('analyseClicked', { data: rowData, rowIndex });
            });

            // Edit icon
            const editIcon = document.createElement('span');
            editIcon.className = 'listing-edit-ic';
            editIcon.innerHTML = '<img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" />';

            editIcon.addEventListener('click', (e) => {
                e.stopPropagation();
                this.emit('editClicked', { data: rowData, rowIndex });
            });

            actionsContainer.appendChild(analyseIcon);
            actionsContainer.appendChild(editIcon);

            container.appendChild(actionsContainer);
            return container;
        }

        // Special handling for marketplace field with flag icons
        if (column.field === 'marketplace') {
            const flagIcon = document.createElement('img');
            flagIcon.src = `./assets/${value}.svg`;
            flagIcon.alt = value;
            flagIcon.className = 'marketplace-flag';
            flagIcon.style.width = '16px';
            flagIcon.style.height = '16px';
            flagIcon.style.marginRight = '6px';
            flagIcon.style.verticalAlign = 'middle';
            flagIcon.draggable = false;

            const textSpan = document.createElement('span');
            textSpan.textContent = value;
            textSpan.style.verticalAlign = 'middle';

            container.appendChild(flagIcon);
            container.appendChild(textSpan);
            container.style.display = 'flex';
            container.style.alignItems = 'center';

            return container;
        }

        // Built-in renderers based on column type
        switch (column.type) {
            case 'number':
                container.textContent = this.formatNumber(value, column.numberFormat);
                // Removed text-right class - all cells should be left-aligned
                break;

            case 'currency':
                container.textContent = this.formatCurrency(value, column.currencyFormat);
                // Removed text-right class - all cells should be left-aligned
                break;

            case 'percentage':
                container.textContent = this.formatPercentage(value, column.percentageFormat);
                // Removed text-right class - all cells should be left-aligned
                break;

            case 'date':
                container.textContent = this.formatDate(value, column.dateFormat);
                break;

            case 'boolean':
                container.textContent = this.renderBoolean(value);
                container.classList.add('text-center');
                break;

            case 'link':
                // Links need special handling for safety
                const linkElement = this.renderLink(value, column.linkFormat);
                if (linkElement instanceof HTMLElement) {
                    container.appendChild(linkElement);
                } else {
                    container.textContent = String(value ?? '');
                }
                break;

            default:
                container.textContent = String(value ?? '');
        }

        return container;
    }

    /**
     * Centralized cell content assignment with HTML sanitization
     * @param {HTMLElement} el - Target element
     * @param {string} html - HTML content to set
     * @param {boolean} allowHTML - Whether to allow HTML (defaults to false)
     */
    setCellContent(el, html, allowHTML = false) {
        if (allowHTML && this.config.allowUnsafeHtml) {
            el.innerHTML = html;
        } else if (this.config.sanitizeHTML) {
            el.innerHTML = this.sanitizeHTML(html);
        } else {
            el.innerHTML = this.escapeHTML(html);
        }
    }

    /**
     * Format number value
     */
    formatNumber(value, format = {}) {
        if (value === null || value === undefined || value === '') return '';

        const num = Number(value);
        if (isNaN(num)) return String(value);

        return num.toLocaleString(format.locale || 'en-US', {
            minimumFractionDigits: format.minimumFractionDigits || 0,
            maximumFractionDigits: format.maximumFractionDigits || 2
        });
    }

    /**
     * Format currency value
     */
    formatCurrency(value, format = {}) {
        if (value === null || value === undefined || value === '') return '';

        const num = Number(value);
        if (isNaN(num)) return String(value);

        return num.toLocaleString(format.locale || 'en-US', {
            style: 'currency',
            currency: format.currency || 'USD'
        });
    }

    /**
     * Format percentage value
     */
    formatPercentage(value, format = {}) {
        if (value === null || value === undefined || value === '') return '';

        const num = Number(value);
        if (isNaN(num)) return String(value);

        const {
            minimumFractionDigits = 0,
            maximumFractionDigits = 2,
            locale = 'en-US'
        } = format;

        // Convert percentage value to decimal for proper formatting
        const decimalValue = num / 100;

        return decimalValue.toLocaleString(locale, {
            style: 'percent',
            minimumFractionDigits,
            maximumFractionDigits
        });
    }

    /**
     * Format date value
     */
    formatDate(value, format = {}) {
        if (!value) return '';

        const date = new Date(value);
        if (isNaN(date.getTime())) return String(value);

        if (format.custom) {
            return format.custom(date);
        }

        return date.toLocaleDateString(format.locale || 'en-US', format.options || {});
    }

    /**
     * Render boolean value (safe text-only)
     */
    renderBoolean(value) {
        if (value === null || value === undefined) return '';
        return Boolean(value) ? '✓' : '✗';
    }

    /**
     * Render link value (safe DOM element)
     */
    renderLink(value, format = {}) {
        if (!value) return null;

        const link = document.createElement('a');
        link.className = 'snap-grid-link';
        link.target = format.target || '_blank';
        link.rel = 'noopener noreferrer'; // Security best practice

        // Safely set href and text
        const href = format.href ? format.href(value) : value;
        const text = format.text ? format.text(value) : value;

        link.href = String(href);
        link.textContent = String(text);

        return link;
    }

    /**
     * Centralized checkbox state management methods
     */
    initializeCheckboxState(field, allValues) {
        if (!this.checkboxStates.has(field)) {
            // Initialize with all values checked by default
            this.checkboxStates.set(field, new Set(allValues));
        }
    }

    /**
     * Validate text/dropdown filter conditions
     */
    validateTextFilter(operator, value) {
        if (!operator || operator === 'pleaseSelect') {
            return false;
        }

        // Operators that don't need a value (date presets only)
        const noValueOperators = ['today', 'yesterday', 'thisWeek', 'lastWeek', 'thisMonth', 'lastMonth', 'thisYear', 'lastYear'];
        if (noValueOperators.includes(operator)) {
            return true;
        }

        // Operators that need a value - allow spaces
        return value && value.length > 0;
    }

    /**
     * Validate checkbox filter conditions
     */
    validateCheckboxFilter(field, checkedValues) {
        const totalUniqueValues = this.getUniqueColumnValues(field).length;
        // Checkbox filter is active when selection differs from "all selected"
        return checkedValues.length !== totalUniqueValues;
    }

    /**
     * Comprehensive filter state validation
     */
    validateFilterState(field, filterConfig) {
        if (!field || !filterConfig) {
            console.warn('⚠️ Invalid filter validation parameters:', { field, filterConfig });
            return false;
        }

        const { type, operator, value, secondOperator, secondValue, checkedValues } = filterConfig;

        // Validate filter type
        const validTypes = ['text', 'number', 'date', 'boolean', 'select'];
        if (type && !validTypes.includes(type)) {
            console.warn('⚠️ Invalid filter type:', type);
            return false;
        }

        // Validate operators
        const validOperators = [
            'equals', 'notEquals', 'contains', 'notContains', 'startsWith', 'endsWith',
            'greaterThan', 'lessThan', 'greaterThanOrEqual', 'lessThanOrEqual',
            'blank', 'notBlank', 'today', 'yesterday', 'thisWeek', 'lastWeek',
            'thisMonth', 'lastMonth', 'thisYear', 'lastYear'
        ];

        if (operator && !validOperators.includes(operator)) {
            console.warn('⚠️ Invalid primary operator:', operator);
            return false;
        }

        if (secondOperator && !validOperators.includes(secondOperator)) {
            console.warn('⚠️ Invalid secondary operator:', secondOperator);
            return false;
        }

        // Validate checkbox values
        if (checkedValues && !Array.isArray(checkedValues)) {
            console.warn('⚠️ Invalid checkedValues format:', checkedValues);
            return false;
        }

        // Validate value consistency - but be more lenient for partial input
        if (operator && operator !== 'pleaseSelect') {
            // Operators that don't need input values
            const noValueOperators = [
                'blank', 'notBlank',
                'today', 'yesterday', 'last7Days', 'last30Days', 'last90Days', 'last6Months',
                'currentMonth', 'lastMonth', 'currentYear', 'lastYear'
            ];

            const needsValue = !noValueOperators.includes(operator);
            if (needsValue && (!value || value.length === 0)) {
                // Allow empty values during typing - just log for debugging
                console.log('ℹ️ Operator requires value but none provided yet:', { operator, value });
                // Don't return false - allow partial states during user input
            }
        }

        console.log('✅ Filter state validation passed for field:', field);
        return true;
    }

    /**
     * Test combined filter scenarios for debugging
     */
    testFilterScenarios(field = null) {
        console.log('🧪 Testing filter scenarios...');

        const testField = field || (this.config.columns.length > 0 ? this.config.columns[0].field : 'test');

        // Test 1: Text filter only
        console.log('🧪 Test 1: Text filter only');
        const textFilter = {
            type: 'text',
            operator: 'contains',
            value: 'test',
            checkedValues: []
        };
        console.log('Text filter valid:', this.validateFilterState(testField, textFilter));

        // Test 2: Checkbox filter only
        console.log('🧪 Test 2: Checkbox filter only');
        const checkboxFilter = {
            type: 'text',
            operator: 'pleaseSelect',
            value: '',
            checkedValues: ['value1', 'value2']
        };
        console.log('Checkbox filter valid:', this.validateFilterState(testField, checkboxFilter));

        // Test 3: Combined filter
        console.log('🧪 Test 3: Combined filter');
        const combinedFilter = {
            type: 'text',
            operator: 'contains',
            value: 'test',
            checkedValues: ['value1']
        };
        console.log('Combined filter valid:', this.validateFilterState(testField, combinedFilter));

        // Test 4: Invalid filter
        console.log('🧪 Test 4: Invalid filter');
        const invalidFilter = {
            type: 'invalid',
            operator: 'badOperator',
            value: '',
            checkedValues: 'not-an-array'
        };
        console.log('Invalid filter valid:', this.validateFilterState(testField, invalidFilter));

        console.log('🧪 Filter scenario testing complete');
    }

    getCheckboxState(field) {
        return this.checkboxStates.get(field) || new Set();
    }

    setCheckboxValue(field, value, checked) {
        if (!this.checkboxStates.has(field)) {
            this.checkboxStates.set(field, new Set());
        }

        const state = this.checkboxStates.get(field);
        if (checked) {
            state.add(value);
        } else {
            state.delete(value);
        }
    }

    setAllCheckboxValues(field, values, checked) {
        if (!this.checkboxStates.has(field)) {
            this.checkboxStates.set(field, new Set());
        }

        const state = this.checkboxStates.get(field);
        if (checked) {
            values.forEach(value => state.add(value));
        } else {
            values.forEach(value => state.delete(value));
        }
    }

    /**
     * Debounced filter application to prevent race conditions
     */
    debouncedApplyFilter() {
        // Preserve current scroll position before debounced filtering
        const currentScrollLeft = this.elements.viewport ? this.elements.viewport.scrollLeft : 0;
        const currentScrollTop = this.elements.viewport ? this.elements.viewport.scrollTop : 0;

        // Clear existing timer
        if (this.filterDebounceTimer) {
            clearTimeout(this.filterDebounceTimer);
        }

        // Set new timer
        this.filterDebounceTimer = setTimeout(() => {
            // Store scroll position for render method
            this.state.scrollLeft = currentScrollLeft;
            this.state.scrollTop = currentScrollTop;

            this.applyFilters();
            this.render();
            this.updateFilterIndicators();
            this.filterDebounceTimer = null;
        }, this.filterDebounceDelay);
    }

    /**
     * Apply filters to data
     */
    applyFilters() {
        const startTime = performance.now();

        console.log('🔍 ApplyFilters called with config:', this.state.filterConfig);
        console.log('🔍 Total data rows:', this.state.data.length);

        // Show sample dates from data for debugging
        if (this.state.data.length > 0) {
            const sampleDates = this.state.data.slice(0, 3).map(row => {
                const dateFields = Object.keys(row).filter(key =>
                    row[key] instanceof Date ||
                    (typeof row[key] === 'string' && !isNaN(new Date(row[key]).getTime()))
                );
                return dateFields.reduce((acc, field) => {
                    acc[field] = {
                        value: row[field],
                        year: new Date(row[field]).getFullYear()
                    };
                    return acc;
                }, {});
            });
            console.log('🔍 Sample date fields from data:', sampleDates);
        }

        if (Object.keys(this.state.filterConfig).length === 0) {
            this.state.filteredData = [...this.state.data];
            console.log('✅ No filters active, showing all data');
        } else {
            let filteredCount = 0;
            this.state.filteredData = this.state.data.filter(row => {
                const passes = Object.entries(this.state.filterConfig).every(([field, filter]) => {
                    const value = this.getNestedValue(row, field);

                    // DEBUGGING: Log each filter application for date columns
                    if (filter.type === 'date') {
                        console.log('📅 Applying date filter to row:', {
                            field,
                            value,
                            valueType: typeof value,
                            filter
                        });
                    }

                    const result = this.applyFilter(value, filter);

                    if (!result) {
                        console.log('❌ Row filtered out by field:', field, 'value:', value, 'filter:', filter);
                    }

                    return result;
                });

                if (passes) {
                    filteredCount++;
                }

                return passes;
            });

            console.log(`✅ Filtering complete: ${filteredCount}/${this.state.data.length} rows passed`);
        }

        this.performance.filterTime = performance.now() - startTime;
        console.log(`⏱️ Filter processing time: ${this.performance.filterTime.toFixed(2)}ms`);
    }

    /**
     * Apply single filter to value
     */
    applyFilter(value, filter) {
        const {
            type,
            operator,
            value: filterValue,
            secondOperator,
            secondValue,
            logicOperator,
            checkedValues
        } = filter;

        console.log('🔍 ApplyFilter to value:', {
            value,
            operator,
            filterValue,
            type,
            hasCheckedValues: checkedValues && checkedValues.length > 0
        });

        // COMPLETELY REFACTORED: Simplified checkbox filtering logic
        let passesCheckboxFilter = true; // Default to true if no checkbox filter

        if (checkedValues && Array.isArray(checkedValues)) {
            const stringValue = String(value ?? '');

            console.log('📋 Checkbox filter processing:', {
                value: stringValue,
                checkedValuesCount: checkedValues ? checkedValues.length : 0,
                hasTextOperator: operator && operator !== 'pleaseSelect',
                checkedValues: checkedValues ? checkedValues.slice(0, 3) : []
            });

            // CRITICAL: Empty checkedValues array means "hide all rows"
            if (!checkedValues || checkedValues.length === 0) {
                console.log('❌ Empty checkedValues - hiding all rows');
                passesCheckboxFilter = false;
            } else {
                // Check if this value is in the allowed list
                const isValueAllowed = checkedValues.includes(stringValue);
                passesCheckboxFilter = isValueAllowed;

                // For checkbox-only filters (no text operators), return checkbox result immediately
                if (!operator || operator === 'pleaseSelect') {
                    console.log('📋 Checkbox-only result:', isValueAllowed);
                    return isValueAllowed;
                }

                // For mixed filters, checkbox is a prerequisite - value must be in allowed list
                if (!isValueAllowed) {
                    console.log('❌ Value not in checkbox allowlist, blocking');
                    return false;
                }

                console.log('✅ Value passes checkbox filter, checking text filter');
            }
        }

        // Handle text/number/date filtering
        let textFilterResult = true;

        // FIXED: Apply first condition if operator is valid
        if (operator && operator !== 'pleaseSelect') {
            console.log('🔍 Applying single condition:', {
                value,
                type,
                operator,
                filterValue,
                isDate: type === 'date',
                hasFilterValue: filterValue !== undefined && filterValue !== null
            });
            // For date filters, predefined ranges (like 'lastYear') have null filterValue but should still be processed
            if (type === 'date' || (filterValue !== undefined && filterValue !== null)) {
                textFilterResult = this.applySingleCondition(value, type, operator, filterValue);
                console.log('🔍 Single condition result:', textFilterResult);
            } else {
                console.log('🔍 Skipping single condition - no valid filterValue');
                textFilterResult = true;
            }
        }

        // Apply second condition if exists
        let secondResult = true;
        let hasSecondCondition = false;

        if (secondOperator && secondOperator !== 'pleaseSelect') {
            if (secondValue !== undefined && secondValue !== null && secondValue.length > 0) {
                secondResult = this.applySingleCondition(value, type, secondOperator, secondValue);
                hasSecondCondition = true;
            }
        }

        // FIXED: Only combine results if we actually have a second condition
        if (hasSecondCondition) {
            console.log('🔍 Applying logic operator:', logicOperator, 'first:', textFilterResult, 'second:', secondResult);

            if (logicOperator === 'AND') {
                textFilterResult = textFilterResult && secondResult;
                console.log('🔍 AND logic applied:', textFilterResult, '&&', secondResult, '=', textFilterResult);
            } else if (logicOperator === 'OR') {
                textFilterResult = textFilterResult || secondResult;
                console.log('🔍 OR logic applied:', textFilterResult, '||', secondResult, '=', textFilterResult);
            }

            console.log('🔍 Combined filter result:', {
                firstResult: textFilterResult,
                secondResult,
                logicOperator,
                finalResult: textFilterResult
            });
        } else {
            console.log('🔍 No second condition, using first result only:', textFilterResult);
        }

        // FIXED: Combine checkbox and text filter results
        // For mixed filters, BOTH checkbox and text filters must pass
        const finalResult = passesCheckboxFilter && textFilterResult;

        console.log('✅ Final filter result:', {
            passesCheckboxFilter,
            textFilterResult,
            finalResult,
            hasCheckboxValues: checkedValues && checkedValues.length > 0
        });

        return finalResult;
    }

    /**
     * Apply single condition to value
     */
    applySingleCondition(value, type, operator, filterValue) {
        switch (type) {
            case 'text':
                return this.applyTextFilter(value, operator, filterValue);
            case 'number':
                return this.applyNumberFilter(value, operator, filterValue);
            case 'date':
                return this.applyDateFilter(value, operator, filterValue);
            case 'boolean':
                return this.applyBooleanFilter(value, operator, filterValue);
            default:
                return true;
        }
    }

    /**
     * Apply text filter
     */
    applyTextFilter(value, operator, filterValue) {
        const str = String(value ?? '').toLowerCase();
        const filter = String(filterValue || '').toLowerCase();

        console.log('🔍 ApplyTextFilter:', {
            originalValue: value,
            str,
            operator,
            originalFilterValue: filterValue,
            filter
        });

        let result;
        switch (operator) {
            case 'contains':
                result = str.includes(filter);
                break;
            case 'notContains':
                result = !str.includes(filter);
                break;
            case 'equals':
                result = str === filter;
                break;
            case 'notEquals':
                result = str !== filter;
                break;
            case 'startsWith':
                result = str.startsWith(filter);
                break;
            case 'endsWith':
                result = str.endsWith(filter);
                break;
            case 'blank':
                result = !str || str.trim() === '';
                break;
            case 'notBlank':
                result = str && str.trim() !== '';
                break;
            default:
                console.warn('⚠️ Unknown text filter operator:', operator);
                result = true;
        }

        console.log('🔍 Text filter result:', result);
        return result;
    }

    /**
     * Apply number filter
     */
    applyNumberFilter(value, operator, filterValue) {
        // Helper: robust numeric parsing (handles currency strings like "$19.34", commas, etc.)
        const toNum = (v) => {
            if (v === null || v === undefined || v === '') return NaN;
            if (typeof v === 'number') return v;
            if (typeof v === 'string') {
                const cleaned = v.replace(/[^0-9.-]/g, '');
                return cleaned === '' || cleaned === '-' || cleaned === '.' ? NaN : Number(cleaned);
            }
            return Number(v);
        };

        // Handle blank/not blank first
        if (operator === 'blank') {
            return value === null || value === undefined || value === '';
        }
        if (operator === 'notBlank') {
            return value !== null && value !== undefined && value !== '';
        }

        const num = toNum(value);
        if (isNaN(num)) {
            // If cell value cannot be parsed as number, it cannot satisfy numeric filters
            return false;
        }

        // In-range is special: filterValue is an object, so don't coerce it to Number
        if (operator === 'inRange') {
            let from = NaN, to = NaN;
            if (filterValue && typeof filterValue === 'object') {
                from = toNum(filterValue.fromValue);
                to = toNum(filterValue.toValue);
            }
            // Both bounds provided
            if (!isNaN(from) && !isNaN(to)) return num >= from && num <= to;
            // Only lower bound
            if (!isNaN(from)) return num >= from;
            // Only upper bound
            if (!isNaN(to)) return num <= to;
            // No valid bounds -> do not filter out
            return true;
        }

        // For other operators, parse the filterValue as a number
        const filter = toNum(filterValue);
        console.log('🔢 applyNumberFilter debug:', {
            value,
            filterValue,
            num,
            filter,
            operator,
            isNaNFilter: isNaN(filter)
        });
        if (isNaN(filter)) {
            // If user hasn't entered a valid number yet, don't hide rows
            console.log('🔢 Filter value is NaN, returning true');
            return true;
        }

        const result = (() => {
            switch (operator) {
                case 'equals': return num === filter;
                case 'notEquals': return num !== filter;
                case 'lessThan': return num < filter;
                case 'lessThanOrEqual': return num <= filter;
                case 'greaterThan': return num > filter;
                case 'greaterThanOrEqual': return num >= filter;
                default: return true;
            }
        })();
        
        console.log('🔢 applyNumberFilter result:', result);
        return result;
    }

    /**
     * Apply date filter
     */
    applyDateFilter(value, operator, filterValue) {
        console.log('📅 Date filter debug:', {
            value,
            operator,
            filterValue,
            valueType: typeof value
        });

        // Try to parse the date value first - handle various formats
        let date;
        if (value instanceof Date) {
            date = value;
        } else if (typeof value === 'string') {
            // Handle common date formats
            date = new Date(value);
            // If that fails, try parsing as ISO date
            if (isNaN(date.getTime()) && value.includes('-')) {
                date = new Date(value + 'T00:00:00');
            }
        } else if (typeof value === 'number') {
            date = new Date(value);
        } else {
            console.log('❌ Invalid date value type:', typeof value, value);
            return false;
        }

        console.log('📅 Date parsing:', {
            originalValue: value,
            valueType: typeof value,
            parsedDate: date,
            isValidDate: !isNaN(date.getTime()),
            dateYear: date.getFullYear(),
            operator
        });

        if (isNaN(date.getTime())) {
            console.log('❌ Invalid date after parsing:', value);
            return false;
        }

        // Handle custom date range
        if (operator === 'inRange') {
            if (filterValue && typeof filterValue === 'object') {
                const { fromValue, toValue } = filterValue;
                const fromDate = fromValue ? new Date(fromValue) : null;
                const toDate = toValue ? new Date(toValue) : null;

                console.log('📅 Date range filter:', {
                    fromValue,
                    toValue,
                    fromDate,
                    toDate,
                    itemDate: date
                });

                // Both dates provided
                if (fromDate && toDate && !isNaN(fromDate.getTime()) && !isNaN(toDate.getTime())) {
                    return date >= fromDate && date <= toDate;
                }
                // Only from date
                if (fromDate && !isNaN(fromDate.getTime())) {
                    return date >= fromDate;
                }
                // Only to date
                if (toDate && !isNaN(toDate.getTime())) {
                    return date <= toDate;
                }
            }
            return true; // No valid range specified
        }

        // Get current date for comparisons - use simple Date() for clarity
        const now = new Date();
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Midnight for date-only comparisons
        const currentYear = now.getFullYear();

        console.log('📅 Date context:', {
            now: now.toString(),
            today: today.toString(),
            currentYear,
            itemDate: date.toString(),
            itemYear: date.getFullYear()
        });

        console.log('📅 Current year comparison:', {
            currentYear,
            dateYear: date.getFullYear(),
            operator
        });

        // Handle preset date ranges
        switch (operator) {
            case 'pleaseSelect':
                return true; // Show all when "Please Select" is chosen
            case 'today':
                // Compare dates only (ignore time)
                const itemDateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                const todayMatch = itemDateOnly.getTime() === today.getTime();

                console.log('📅 Today filter check:', {
                    itemDate: itemDateOnly.toDateString(),
                    today: today.toDateString(),
                    matches: todayMatch
                });
                return todayMatch;
            case 'yesterday':
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                const itemDateYesterday = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                return itemDateYesterday.getTime() === yesterday.getTime();
            case 'last7Days':
                const sevenDaysAgo = new Date(today);
                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                return date >= sevenDaysAgo && date <= now;
            case 'last30Days':
                const thirtyDaysAgo = new Date(today);
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                return date >= thirtyDaysAgo && date <= now;
            case 'last90Days':
                const ninetyDaysAgo = new Date(today);
                ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
                return date >= ninetyDaysAgo && date <= now;
            case 'last6Months':
                const sixMonthsAgo = new Date(now);
                sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
                return date >= sixMonthsAgo && date <= now;
            case 'currentMonth':
                return date.getFullYear() === now.getFullYear() && date.getMonth() === now.getMonth();
            case 'lastMonth':
                const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                return date.getFullYear() === lastMonth.getFullYear() && date.getMonth() === lastMonth.getMonth();
            case 'currentYear':
                const isCurrentYear = date.getFullYear() === currentYear;
                console.log('📅 Current year filter:', {
                    itemYear: date.getFullYear(),
                    currentYear,
                    matches: isCurrentYear
                });
                return isCurrentYear;

            case 'lastYear':
                const isLastYear = date.getFullYear() === (currentYear - 1);
                console.log('📅 Last year filter:', {
                    itemYear: date.getFullYear(),
                    lastYear: currentYear - 1,
                    matches: isLastYear
                });
                return isLastYear;
        }

        // Handle custom date comparisons (for equals, notEquals, lessThan, greaterThan)
        if (filterValue && typeof filterValue === 'string') {
            const filter = new Date(filterValue);
            if (!isNaN(filter.getTime())) {
                switch (operator) {
                    case 'equals':
                        // Compare dates without time
                        const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                        const filterOnly = new Date(filter.getFullYear(), filter.getMonth(), filter.getDate());
                        return dateOnly.getTime() === filterOnly.getTime();
                    case 'notEquals':
                        const dateOnly2 = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                        const filterOnly2 = new Date(filter.getFullYear(), filter.getMonth(), filter.getDate());
                        return dateOnly2.getTime() !== filterOnly2.getTime();
                    case 'lessThan': return date < filter;
                    case 'greaterThan': return date > filter;
                }
            }
        }

        return true; // Default: show all if no valid filter
    }

    /**
     * Apply boolean filter
     */
    applyBooleanFilter(value, operator, filterValue) {
        const bool = Boolean(value);
        const filter = Boolean(filterValue);

        switch (operator) {
            case 'equals': return bool === filter;
            case 'notEquals': return bool !== filter;
            default: return true;
        }
    }

    /**
     * Apply sorting to filtered data
     */
    applySorting() {
        const startTime = performance.now();

        if (this.state.sortConfig.length === 0) {
            this.state.sortedData = [...this.state.filteredData];
        } else {
            this.state.sortedData = [...this.state.filteredData].sort((a, b) => {
                for (const sort of this.state.sortConfig) {
                    const aValue = this.getNestedValue(a, sort.field);
                    const bValue = this.getNestedValue(b, sort.field);
                    const result = this.compareValues(aValue, bValue, sort.direction);

                    if (result !== 0) return result;
                }
                return 0;
            });
        }

        this.performance.sortTime = performance.now() - startTime;
    }

    /**
     * Compare two values for sorting
     */
    compareValues(a, b, direction = 'asc') {
        // Handle null/undefined values
        if (a == null && b == null) return 0;
        if (a == null) return direction === 'asc' ? -1 : 1;
        if (b == null) return direction === 'asc' ? 1 : -1;

        // Convert to comparable types
        const aVal = this.getComparableValue(a);
        const bVal = this.getComparableValue(b);

        let result = 0;
        if (aVal < bVal) result = -1;
        else if (aVal > bVal) result = 1;

        return direction === 'desc' ? -result : result;
    }

    /**
     * Get comparable value for sorting
     */
    getComparableValue(value) {
        if (typeof value === 'string') {
            return value.toLowerCase();
        }
        if (value instanceof Date) {
            return value.getTime();
        }
        return value;
    }

    /**
     * Get stable row ID for a row
     */
    getRowId(rowData) {
        if (this.config.getRowId) {
            return this.config.getRowId(rowData);
        }
        return rowData[this.config.rowIdField] || rowData.id || Math.random().toString(36);
    }

    /**
     * Update row key to index mapping
     */
    updateRowKeyMapping() {
        this.state.rowKeyToIndexMap.clear();
        this.state.displayData.forEach((rowData, index) => {
            if (!rowData.__isGroupHeader) {
                const key = this.getRowId(rowData);
                this.state.rowKeyToIndexMap.set(key, index);
            }
        });
    }

    /**
     * Apply grouping to sorted data
     */
    applyGrouping() {
        if (!this.state.groupConfig) {
            this.state.displayData = [...this.state.sortedData];
            this.updateRowKeyMapping();
            return;
        }

        // Group data by specified field
        const groups = new Map();
        this.state.sortedData.forEach(row => {
            const groupValue = this.getNestedValue(row, this.state.groupConfig.field);
            const key = String(groupValue || '');

            if (!groups.has(key)) {
                groups.set(key, []);
            }
            groups.get(key).push(row);
        });

        // Create display data with group headers and expand/collapse
        this.state.displayData = [];
        groups.forEach((rows, groupKey) => {
            // Add group header
            const groupHeader = {
                __isGroupHeader: true,
                __groupKey: groupKey,
                __groupCount: rows.length,
                __groupField: this.state.groupConfig.field,
                __expanded: this.state.expandedGroups.has(groupKey)
            };
            this.state.displayData.push(groupHeader);

            // Add group rows only if expanded
            if (this.state.expandedGroups.has(groupKey)) {
                this.state.displayData.push(...rows);
            }
        });

        // Apply pagination if enabled
        this.applyPagination();

        // Update row key mapping after all processing
        this.updateRowKeyMapping();
    }

    /**
     * Apply pagination to display data
     */
    applyPagination() {
        if (!this.config.pagination) return;

        const pageSize = this.config.pageSize;
        const startIndex = this.state.pageIndex * pageSize;
        const endIndex = startIndex + pageSize;

        this.state.displayData = this.state.displayData.slice(startIndex, endIndex);
    }

    /**
     * Handle scroll events
     */
    handleScroll(event) {
        const startTime = performance.now();

        this.state.scrollTop = this.elements.viewport.scrollTop;
        this.state.scrollLeft = this.elements.viewport.scrollLeft;

        if (this.config.virtualScrolling) {
            this.updateVirtualScrolling();
            this.renderBody();
        }

        // Sync header viewport scroll with body viewport scroll
        if (this.elements.headerViewport) {
            this.elements.headerViewport.scrollLeft = this.state.scrollLeft;
        }

        // Compensate for scrollbar width in header to maintain alignment
        this.compensateHeaderScrollbar();
        this.compensatePinnedColumnsHeight();

        // Sync vertical scroll of pinned columns with main viewport
        if (this.elements.pinnedLeft) {
            this.elements.pinnedLeft.scrollTop = this.state.scrollTop;
        }
        if (this.elements.pinnedRight) {
            this.elements.pinnedRight.scrollTop = this.state.scrollTop;
        }

        // Update active menu position if it exists, with collision detection
        if (this.activeMenu && this.activeMenuTarget) {
            try {
                this.positionMenuWithCollisionDetection(this.activeMenu, this.activeMenuTarget);
            } catch (error) {
                console.warn('Error repositioning menu during scroll:', error);
                // Don't close the menu, just skip repositioning this time
            }
        }

        this.performance.scrollTime = performance.now() - startTime;
        this.emit('scroll', {
            scrollTop: this.state.scrollTop,
            scrollLeft: this.state.scrollLeft,
            firstRow: this.state.visibleRange.start,
            lastRow: this.state.visibleRange.end,
            visibleRange: this.state.visibleRange
        });
    }

    /**
     * Compensate for scrollbar width in header to maintain column alignment
     */
    compensateHeaderScrollbar() {
        if (!this.elements.headerViewport || !this.elements.viewport) return;

        // Calculate if there's a vertical scrollbar in the body viewport
        const hasVerticalScrollbar = this.elements.viewport.scrollHeight > this.elements.viewport.clientHeight;

        if (hasVerticalScrollbar) {
            // Get the scrollbar width by comparing offsetWidth and clientWidth
            const scrollbarWidth = this.elements.viewport.offsetWidth - this.elements.viewport.clientWidth;

            // Apply padding-right to header viewport to compensate for scrollbar
            this.elements.headerViewport.style.paddingRight = `${scrollbarWidth}px`;
        } else {
            // Remove padding if no scrollbar
            this.elements.headerViewport.style.paddingRight = '0px';
        }
    }

    /**
     * Compensate for horizontal scrollbar height in pinned columns to maintain row alignment
     */
    compensatePinnedColumnsHeight() {
        if (!this.elements.pinnedLeft || !this.elements.pinnedRight || !this.elements.viewport) return;

        // Calculate if there's a horizontal scrollbar in the main viewport
        const hasHorizontalScrollbar = this.elements.viewport.scrollWidth > this.elements.viewport.clientWidth;
        
        if (hasHorizontalScrollbar) {
            // Get the scrollbar height by comparing offsetHeight and clientHeight
            const scrollbarHeight = this.elements.viewport.offsetHeight - this.elements.viewport.clientHeight;
            
            // Apply height compensation to pinned columns to match the main viewport's available height
            const compensatedHeight = `calc(100% - ${scrollbarHeight}px)`;
            this.elements.pinnedLeft.style.height = compensatedHeight;
            this.elements.pinnedRight.style.height = compensatedHeight;
            
            console.log('🔧 Compensated pinned columns height for horizontal scrollbar:', scrollbarHeight, 'px');
        } else {
            // Reset to full height when no horizontal scrollbar
            this.elements.pinnedLeft.style.height = '100%';
            this.elements.pinnedRight.style.height = '100%';
        }
    }

    /**
     * Handle scroll events from pinned columns - sync with main viewport
     */
    handlePinnedScroll(event) {
        // Sync the main viewport with pinned column scroll
        const scrollTop = event.target.scrollTop;
        if (this.elements.viewport.scrollTop !== scrollTop) {
            this.elements.viewport.scrollTop = scrollTop;
        }
    }

    /**
     * Handle resize events
     */
    handleResize() {
        if (this.config.virtualScrolling) {
            this.setupVirtualScrolling();
            this.render();
        } else {
            // Compensate for scrollbar changes on resize
            this.compensateHeaderScrollbar();
            this.compensatePinnedColumnsHeight();
        }
        this.emit('resize');
    }

    /**
     * Handle click events
     */
    handleClick(event) {
        const target = event.target;

        // Header cell click (sorting) - disabled for fixed columns
        const headerCell = target.closest('.snap-grid-header-cell');
        if (headerCell && this.config.sortable) {
            const field = headerCell.getAttribute('data-field');
            const column = this.config.columns.find(col => col.field === field);
            const columnType = this.getColumnType(field);

            if (column && column.sortable !== false && columnType !== 'fixed') {
                this.toggleSort(field, event.ctrlKey || event.metaKey);
                return;
            }
        }

        // Cell click (editing only)
        const cell = target.closest('.snap-grid-cell');
        if (cell) {
            const rowIndex = parseInt(cell.getAttribute('data-row-index'));
            const colIndex = parseInt(cell.getAttribute('data-col-index'));

            if (this.config.editable && cell.classList.contains('editable')) {
                this.startCellEdit(rowIndex, colIndex);
            }

            return;
        }

        // Row click (row selection)
        const row = target.closest('.snap-grid-row');
        if (row && this.config.selectable) {
            const rowIndex = parseInt(row.getAttribute('data-row-index'));
            this.selectRow(rowIndex, event.ctrlKey || event.metaKey);
        }
    }

    /**
     * Handle keydown events
     */
    handleKeydown(event) {
        const { key, ctrlKey, metaKey, shiftKey } = event;

        // Navigation keys
        if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(key)) {
            this.handleArrowKey(key, shiftKey);
            event.preventDefault();
            return;
        }

        // Enter key (edit/confirm)
        if (key === 'Enter') {
            if (this.state.editingCell) {
                this.confirmCellEdit();
            } else if (this.config.editable) {
                this.startCellEditFromSelection();
            }
            event.preventDefault();
            return;
        }

        // Escape key (cancel edit or close open menus/dialogs)
        if (key === 'Escape') {
            if (this.currentColumnMenu) {
                this.hideColumnMenu();
                event.preventDefault();
                return;
            }
            if (this.state.editingCell) {
                this.cancelCellEdit();
                event.preventDefault();
                return;
            }
            return;
        }

        // Space key (selection) - but not when typing in input fields
        if (key === ' ' && this.config.selectable) {
            // Don't interfere with space key in input fields
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                return; // Let the input handle the space normally
            }
            this.toggleSelectionFromFocus();
            event.preventDefault();
            return;
        }

        // Ctrl/Cmd + A (select all)
        if ((ctrlKey || metaKey) && key === 'a' && this.config.selectable) {
            this.selectAll();
            event.preventDefault();
            return;
        }
    }



    /**
     * Show column menu with tabbed interface
     */
    showColumnMenu(column, button) {
        // Add error checking
        if (!button) {
            console.error('showColumnMenu: button parameter is undefined');
            return;
        }
        if (!column) {
            console.error('showColumnMenu: column parameter is undefined');
            return;
        }

        // Remove existing menu
        this.hideColumnMenu();

        const menu = document.createElement('div');
        menu.className = 'snap-grid-column-menu tabbed-menu';
        menu.setAttribute('role', 'menu');
        menu.setAttribute('aria-label', `Column menu for ${column.headerName || column.field}`);
        menu.setAttribute('data-field', column.field);

        // Create tab header
        const tabHeader = document.createElement('div');
        tabHeader.className = 'menu-tab-header';

        // Tab buttons with icons
        const filterTab = this.createTabButton('filter', 'filters', 'Filter');
        const managementTab = this.createTabButton('management', 'column-man', 'Column Management');
        const visibilityTab = this.createTabButton('visibility', 'show-hide-col', 'Show/Hide Columns');

        tabHeader.appendChild(filterTab);
        tabHeader.appendChild(managementTab);
        tabHeader.appendChild(visibilityTab);

        // Create tab content container
        const tabContent = document.createElement('div');
        tabContent.className = 'menu-tab-content';

        // Create tab panels
        const filterPanel = this.createFilterTab(column);
        const managementPanel = this.createManagementTab(column);
        const visibilityPanel = this.createVisibilityTab();

        tabContent.appendChild(filterPanel);
        tabContent.appendChild(managementPanel);
        tabContent.appendChild(visibilityPanel);

        menu.appendChild(tabHeader);
        menu.appendChild(tabContent);

        // Prevent menu from closing when clicking inside it
        menu.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Set initial active tab
        this.setActiveTab(menu, 'filter');

        // Setup tab switching
        this.setupTabSwitching(menu);

        // Track active menu for scroll repositioning
        const headerCell = button.closest('.snap-grid-header-cell');
        const targetElement = headerCell || button;

        this.activeMenu = menu;
        this.activeMenuTarget = targetElement;

        // Add menu-active class to header cell to show it's the active one
        if (headerCell) {
            headerCell.classList.add('menu-active');
        }

        // Append to grid container for proper relative positioning
        this.container.appendChild(menu);
        this.currentColumnMenu = menu;
        this.activeDialogs.add(menu);

        // Position menu after it's added to DOM for accurate positioning
        try {
            this.positionMenuWithCollisionDetection(menu, targetElement);
        } catch (error) {
            console.error('Error positioning menu:', error);
            // Fallback positioning
            menu.style.position = 'fixed';
            menu.style.top = '100px';
            menu.style.left = '100px';
            menu.style.zIndex = '1001';
        }

        // Ensure menu is properly positioned after being added to DOM
        // This helps with accurate positioning calculations
        requestAnimationFrame(() => {
            if (this.activeMenu === menu && this.activeMenuTarget) {
                this.positionMenuWithCollisionDetection(menu, this.activeMenuTarget);
            }
        });

        // Close menu when clicking outside (with small delay to prevent immediate closure)
        setTimeout(() => {
            const handleOutsideClick = (e) => {
                // Don't close if clicking on the menu itself or the menu button
                if (!menu.contains(e.target) && !button.contains(e.target)) {
                    this.hideColumnMenu();
                    document.removeEventListener('click', handleOutsideClick);
                    document.removeEventListener('keydown', handleEscapeKey);
                }
            };

            const handleEscapeKey = (e) => {
                if (e.key === 'Escape') {
                    this.hideColumnMenu();
                    document.removeEventListener('click', handleOutsideClick);
                    document.removeEventListener('keydown', handleEscapeKey);
                }
            };

            document.addEventListener('click', handleOutsideClick);
            document.addEventListener('keydown', handleEscapeKey);
        }, 100);
    }

    /**
     * Hide column menu
     */
    hideColumnMenu() {
        if (this.currentColumnMenu) {
            this.releaseFocus();
            this.activeDialogs.delete(this.currentColumnMenu);
            this.currentColumnMenu.remove();
            this.currentColumnMenu = null;
        }

        // Remove menu-active class from all header cells
        const activeHeaders = this.container.querySelectorAll('.snap-grid-header-cell.menu-active');
        activeHeaders.forEach(header => header.classList.remove('menu-active'));

        // Clear active menu tracking
        this.activeMenu = null;
        this.activeMenuTarget = null;
    }

    /**
     * Refresh active menu target reference after re-rendering
     * This fixes the issue where filtering causes menu positioning to break
     */
    refreshActiveMenuTarget() {
        if (!this.activeMenu || !this.activeMenuTarget) {
            return; // No active menu to refresh
        }

        // Get the field from the old target element's data-field attribute
        const field = this.activeMenuTarget.getAttribute('data-field');
        if (!field) {
            return; // Can't identify which column this was for
        }

        // Find the new header cell with the same data-field
        const newHeaderCell = this.container.querySelector(`.snap-grid-header-cell[data-field="${field}"]`);
        if (newHeaderCell) {
            // Update the reference to point to the new DOM element
            this.activeMenuTarget = newHeaderCell;

            // Also update the menu-active class
            const activeHeaders = this.container.querySelectorAll('.snap-grid-header-cell.menu-active');
            activeHeaders.forEach(header => header.classList.remove('menu-active'));
            newHeaderCell.classList.add('menu-active');
        }
    }

    /**
     * Release focus from dialog elements and restore to grid
     */
    releaseFocus() {
        // Remove focus from any focused elements within dialogs
        const activeElement = document.activeElement;
        if (activeElement && (
            activeElement.closest('.snap-grid-column-menu') ||
            activeElement.closest('.snap-grid-column-chooser')
        )) {
            activeElement.blur();
        }

        // Restore focus to the grid container if needed
        if (this.container && this.container.tabIndex >= 0) {
            this.container.focus();
        }
    }

    /**
     * Toggle selection of the currently focused row
     */
    toggleSelectionFromFocus() {
        if (!this.config.selectable) return;

        // Find the currently focused row
        const activeElement = document.activeElement;
        const focusedRow = activeElement?.closest('.snap-grid-row[data-row-index]');

        if (focusedRow) {
            const rowIndex = parseInt(focusedRow.getAttribute('data-row-index'));
            if (!isNaN(rowIndex) && rowIndex >= 0 && rowIndex < this.data.length) {
                this.toggleRowSelection(rowIndex);
            }
        }
    }

    /**
     * Create tab button for menu
     */
    createTabButton(tabId, iconName, label) {
        const button = document.createElement('button');
        button.className = 'menu-tab-btn';
        button.setAttribute('data-tab', tabId);
        button.setAttribute('aria-label', label);

        // Create icon element
        const icon = document.createElement('img');
        icon.className = 'tab-icon';
        icon.src = `./assets/${iconName}-inactive-ic.svg`;
        icon.alt = label;
        icon.draggable = false;

        button.appendChild(icon);
        return button;
    }

    /**
     * Create filter tab content
     */
    createFilterTab(column) {
        const panel = document.createElement('div');
        panel.className = 'menu-tab-panel';
        panel.setAttribute('data-tab-panel', 'filter');

        // Get existing filter state for restoration
        const existingFilter = this.state.filterConfig[column.field];

        // Get column type using the new type system
        const columnType = this.getColumnType(column.field);

        // Declare applyFilter function early so it can be used by createLogicToggle
        let applyFilter;

        // Determine UI layout based on column type
        const isTextSpecial = columnType === 'text-special';
        const isDateOnly = columnType === 'date';
        const isNumericOnly = columnType === 'numeric';
        const isFixed = columnType === 'fixed';

        // First dropdown (full width) - hidden for checkbox-only columns, shown for date-only columns
        const filterOptions = this.getFilterTypeOptions(column);

        // FIXED: Get the first option value correctly from the options array
        const defaultOperator = filterOptions.length > 0 ? filterOptions[0].value : 'pleaseSelect';

        const firstDropdown = this.createCustomDropdown(
            filterOptions,
            defaultOperator
        );
        firstDropdown.classList.add('filter-type-dropdown');
        if (isTextSpecial || isFixed) {
            firstDropdown.style.display = 'none';
        }

        // Restore first dropdown state if it exists
        if (existingFilter && existingFilter.operator) {
            this.setCustomDropdownValue(firstDropdown, existingFilter.operator);
            console.log('🔄 Restored first dropdown operator:', existingFilter.operator);
        }

        console.log('🔍 Created first dropdown with default operator:', defaultOperator, 'options:', filterOptions);

        // First filter input (full width with filter icon) - hidden for text-special, date-only, and fixed columns
        const firstFilterWrapper = document.createElement('div');
        firstFilterWrapper.className = 'filter-input-wrapper';
        if (isTextSpecial || isDateOnly || isFixed) {
            firstFilterWrapper.style.display = 'none';
        }

        const firstFilterIcon = document.createElement('img');
        firstFilterIcon.src = './assets/filters-active-ic.svg';
        firstFilterIcon.className = 'filter-icon';
        firstFilterIcon.alt = 'Filter';

        const firstInput = document.createElement('input');
        firstInput.type = 'text';
        firstInput.className = 'filter-input-with-icon';
        firstInput.placeholder = 'Filter..';

        firstFilterWrapper.appendChild(firstFilterIcon);
        firstFilterWrapper.appendChild(firstInput);

        // Restore first filter state if it exists
        if (existingFilter && existingFilter.value) {
            firstInput.value = existingFilter.value;
            console.log('🔄 Restored first filter value:', existingFilter.value);
        }

        // AND/OR Logic toggles (initially hidden, always hidden for checkbox-only columns)
        const logicSection = document.createElement('div');
        logicSection.className = 'filter-logic-section';
        logicSection.style.display = 'none';


        // Second dropdown (initially hidden, always hidden for checkbox-only columns)
        const secondDropdown = this.createCustomDropdown(
            filterOptions,
            defaultOperator
        );
        secondDropdown.classList.add('filter-type-dropdown');
        secondDropdown.style.display = 'none';

        // Restore second dropdown state if it exists
        if (existingFilter && existingFilter.secondOperator) {
            this.setCustomDropdownValue(secondDropdown, existingFilter.secondOperator);
            console.log('🔄 Restored second dropdown operator:', existingFilter.secondOperator);
        }

        // Second filter input (initially hidden, always hidden for checkbox-only columns)
        const secondFilterWrapper = document.createElement('div');
        secondFilterWrapper.className = 'filter-input-wrapper';
        secondFilterWrapper.style.display = 'none';

        const secondFilterIcon = document.createElement('img');
        secondFilterIcon.src = './assets/filters-active-ic.svg';
        secondFilterIcon.className = 'filter-icon';
        secondFilterIcon.alt = 'Filter';

        const secondInput = document.createElement('input');
        secondInput.type = 'text';
        secondInput.className = 'filter-input-with-icon';
        secondInput.placeholder = 'Filter..';

        secondFilterWrapper.appendChild(secondFilterIcon);
        secondFilterWrapper.appendChild(secondInput);

        // Restore second filter state if it exists
        if (existingFilter && existingFilter.secondValue) {
            secondInput.value = existingFilter.secondValue;
            console.log('🔄 Restored second filter value:', existingFilter.secondValue);
        }

        // Show second filter and logic section if there's existing data
        if (existingFilter && (existingFilter.secondValue || existingFilter.secondOperator)) {
            logicSection.style.display = 'flex';
            secondDropdown.style.display = 'block';
            secondFilterWrapper.style.display = 'flex';
            console.log('🔄 Showing second filter and logic section due to existing data');
        }

        // Define applyFilter function here so it can be used by createLogicToggle
        applyFilter = () => {
            const firstOperator = this.getCustomDropdownValue(firstDropdown);

            console.log('🔍 ApplyFilter called:', {
                column: column.field,
                firstOperator,
                columnType,
                isDateOnly: columnType === 'date',
                isTextSpecial: columnType === 'text-special',
                firstInputValue: firstInput.value,
                secondInputValue: secondInput.value
            });

            if (columnType === 'date') {
                // For date-only columns, handle dropdown selection
                if (firstOperator === 'inRange') {
                    // Date range: use From/To date inputs
                    const fromDate = firstInput.value;
                    const toDate = secondInput.value;

                    if (fromDate || toDate) {
                        console.log('📅 Applying date range filter:', { fromDate, toDate });
                        this.setFilter(column.field, {
                            type: this.getFilterType(column),
                            operator: 'inRange',
                            value: { fromValue: fromDate, toValue: toDate }
                        });
                    } else {
                        console.log('📅 Clearing date range filter - no dates provided');
                        this.clearFilter(column.field);
                    }
                } else if (['equals', 'notEquals', 'lessThan', 'greaterThan'].includes(firstOperator)) {
                    // Date comparison operators: use single date input
                    const dateValue = firstInput.value;

                    if (dateValue) {
                        console.log('📅 Applying date comparison filter:', { operator: firstOperator, date: dateValue });
                        this.setFilter(column.field, {
                            type: this.getFilterType(column),
                            operator: firstOperator,
                            value: dateValue
                        });
                    } else {
                        console.log('📅 Clearing date comparison filter - no date provided');
                        this.clearFilter(column.field);
                    }
                } else if (firstOperator && firstOperator !== 'pleaseSelect') {
                    // Predefined date range (Today, Yesterday, etc.)
                    console.log('📅 Applying predefined date filter:', firstOperator);

                    // DEBUGGING: Show sample data values for this column
                    const sampleValues = this.config.data.slice(0, 3).map(row => ({
                        value: row[column.field],
                        type: typeof row[column.field]
                    }));
                    console.log('📅 Sample date values from data:', sampleValues);

                    this.setFilter(column.field, {
                        type: this.getFilterType(column),
                        operator: firstOperator,
                        value: null // No input value needed for preset date ranges
                    });
                } else {
                    console.log('📅 Clearing date filter');
                    this.clearFilter(column.field);
                }
                return;
            }

            // For other column types, use the existing logic
            const firstValue = firstInput.value;
            const secondOperator = this.getCustomDropdownValue(secondDropdown);
            const secondValue = secondInput.value;

            // Get logic operator (AND/OR) - Fixed detection
            const andToggle = logicSection.querySelector('[data-logic="AND"] .logic-checkbox');
            const orToggle = logicSection.querySelector('[data-logic="OR"] .logic-checkbox');
            
            // Check which toggle is actually checked
            const isAndChecked = andToggle && andToggle.src.includes('checkbox-ic.svg');
            const isOrChecked = orToggle && orToggle.src.includes('checkbox-ic.svg');
            
            // Default to AND if neither is checked (shouldn't happen with proper UI)
            const logicOperator = isOrChecked ? 'OR' : 'AND';
            
            console.log('🔍 Logic toggle detection:', {
                andToggleExists: !!andToggle,
                orToggleExists: !!orToggle,
                isAndChecked,
                isOrChecked,
                logicOperator
            });

            // Get checked values using centralized state
            const checkedValues = getCheckedValues();

            console.log('🔍 Filter values:', {
                firstOperator,
                firstValue,
                secondOperator,
                secondValue,
                logicOperator,
                checkedValues: checkedValues,
                checkedCount: checkedValues ? checkedValues.length : 0,
                columnType
            });

            // REFACTORED: Use separate validation methods for clear boundaries
            let shouldApplyFilter = false;
            let filterConfig = {
                type: this.getFilterType(column),
                operator: firstOperator,
                value: firstValue,
                secondOperator: null,
                secondValue: null,
                logicOperator: null,
                // Include checkbox state for all column types that support it
                checkedValues: checkedValues
            };

            // Simple validation for text/dropdown filters - allow spaces in values
            const hasValidFirstFilter = firstOperator && firstOperator !== 'pleaseSelect' &&
                (firstValue.length > 0);
            const hasValidSecondFilter = secondOperator && secondOperator !== 'pleaseSelect' &&
                (secondValue.length > 0);

            // Simple validation for checkbox filters
            const totalUniqueValues = this.getUniqueColumnValues(column.field).length;
            const hasCheckboxFilter = checkedValues && Array.isArray(checkedValues) && 
                checkedValues.length !== totalUniqueValues;

            // SPECIAL CASE: Numeric "In range" should IGNORE second operator and logic
            if (firstOperator === 'inRange' && columnType === 'numeric') {
                filterConfig.value = { fromValue: firstValue, toValue: secondValue };
                filterConfig.secondOperator = null;
                filterConfig.secondValue = null;
                filterConfig.logicOperator = null;

                shouldApplyFilter = (firstValue.length > 0 || secondValue.length > 0);

                console.log('🔢 Range filter config (numeric-only):', filterConfig.value);
            } else if (columnType === 'text-special') {
                // Text-special columns: only checkbox filter matters
                shouldApplyFilter = hasCheckboxFilter;
                console.log('✅ Text-special column filter decision:', shouldApplyFilter);
            } else {
                // Combined columns: either text OR checkbox filter can trigger
                if (hasValidFirstFilter) {
                    shouldApplyFilter = true;
                    console.log('✅ First text filter active');
                }

                if (hasValidSecondFilter) {
                    shouldApplyFilter = true;
                    filterConfig.secondOperator = secondOperator;
                    filterConfig.secondValue = secondValue;
                    filterConfig.logicOperator = logicOperator;
                    console.log('✅ Second text filter active');
                }
            }

                if (hasCheckboxFilter) {
                    shouldApplyFilter = true;
                    console.log('✅ Checkbox filter active');
                }

            if (shouldApplyFilter) {
                console.log('🎯 Applying filter:', filterConfig);
                this.setFilter(column.field, filterConfig);
            } else {
                console.log('🗑️ Clearing filter - no valid conditions');
                this.clearFilter(column.field);
            }

            // Regenerate checkbox lists for other columns when text filters change
            // This ensures checkbox lists show only values available in current filtered data
            if (hasValidFirstFilter || hasValidSecondFilter) {
                setTimeout(() => {
                    this.regenerateAllCheckboxLists(column.field);
                }, 50); // Small delay to ensure filter is applied first
            }
        };

        // Create AND/OR toggles after all inputs and applyFilter are defined
        const andToggle = this.createLogicToggle('AND', true, logicSection, applyFilter, firstInput, secondInput, firstDropdown, secondDropdown);
        const orToggle = this.createLogicToggle('OR', false, logicSection, applyFilter, firstInput, secondInput, firstDropdown, secondDropdown);
        const separator = document.createElement('span');
        separator.textContent = '/';
        separator.className = 'logic-separator';

        logicSection.appendChild(andToggle);
        logicSection.appendChild(separator);
        logicSection.appendChild(orToggle);

        // Restore logic operator state if it exists
        if (existingFilter && existingFilter.logicOperator) {
            const andCheckbox = andToggle.querySelector('.logic-checkbox');
            const orCheckbox = orToggle.querySelector('.logic-checkbox');
            
            if (existingFilter.logicOperator === 'OR') {
                andCheckbox.src = './assets/uncheckedbox-ic.svg';
                andCheckbox.alt = 'Unchecked';
                orCheckbox.src = './assets/checkbox-ic.svg';
                orCheckbox.alt = 'Checked';
            } else {
                andCheckbox.src = './assets/checkbox-ic.svg';
                andCheckbox.alt = 'Checked';
                orCheckbox.src = './assets/uncheckedbox-ic.svg';
                orCheckbox.alt = 'Unchecked';
            }
            console.log('🔄 Restored logic operator:', existingFilter.logicOperator);
        }

        // Divider
        const divider = document.createElement('div');
        divider.className = 'filter-divider';

        // Search input with icon - need wrapper for proper positioning
        const searchWrapper = document.createElement('div');
        searchWrapper.className = 'search-input-wrapper';

        const searchIcon = document.createElement('img');
        searchIcon.src = './assets/search-ic.svg';
        searchIcon.className = 'search-icon';
        searchIcon.alt = 'Search';

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'search-input';
        searchInput.placeholder = 'Search..';

        searchWrapper.appendChild(searchIcon);
        searchWrapper.appendChild(searchInput);

        // Checkbox list container
        const checkboxList = document.createElement('div');
        checkboxList.className = 'filter-checkbox-list';

        // Get unique values from current column data (filtered by other active filters)
        const uniqueValues = this.getUniqueColumnValues(column.field, true);

        // Initialize checkbox state for this field
        this.initializeCheckboxState(column.field, uniqueValues);

        // Note: Filter state restoration is handled earlier in the method

        // Ensure correct UI for In range mode immediately on open/restore
        const opAtOpen = this.getCustomDropdownValue(firstDropdown);
        if (opAtOpen === 'inRange' && isNumericOnly) {
            logicSection.style.display = 'none';
            secondDropdown.style.display = 'none';
            secondFilterWrapper.style.display = 'flex';
            firstInput.placeholder = 'From (min value)';
            secondInput.placeholder = 'To (max value)';
        } else if (opAtOpen === 'inRange' && isDateOnly) {
            logicSection.style.display = 'none';
            secondDropdown.style.display = 'none';
            firstFilterWrapper.style.display = 'flex';
            secondFilterWrapper.style.display = 'flex';
            firstInput.type = 'date';
            secondInput.type = 'date';
            firstInput.placeholder = 'From date';
            secondInput.placeholder = 'To date';
        }




        // Helper function to get checked values using centralized state
        const getCheckedValues = () => {
            const state = this.getCheckboxState(column.field);
            const values = Array.from(state);

            console.log('📋 getCheckedValues from state:', {
                field: column.field,
                checkedValues: values,
                checkedCount: values.length
            });

            return values;
        };

        // Function to regenerate checkbox list based on current filtered data
        const regenerateCheckboxList = () => {
            // Get current search term
            const searchTerm = searchInput.value;

            // Get updated unique values based on current filters
            const updatedUniqueValues = this.getUniqueColumnValues(column.field, true);

            // Clear existing checkboxes (except Select All)
            const existingItems = checkboxList.querySelectorAll('.filter-checkbox-item:not(.select-all)');
            existingItems.forEach(item => item.remove());

            // Preserve current checkbox state
            const currentState = this.getCheckboxState(column.field);

            // Create new checkboxes for updated values
            updatedUniqueValues.forEach(value => {
                const isChecked = currentState.has(value);
                const checkboxItem = this.createCheckboxItem(value, isChecked, false, column.field, applyFilter);
                checkboxList.appendChild(checkboxItem);
            });

            // Reapply search filter if there was one
            if (searchTerm) {
                this.filterCheckboxList(checkboxList, searchTerm);
            }

            // Update Select All state
            const selectAllIcon = checkboxList.querySelector('.checkbox-icon[data-value="Select All"]');
            if (selectAllIcon) {
                const allValues = updatedUniqueValues;
                if (currentState.size === 0) {
                    selectAllIcon.src = './assets/uncheckedbox-ic.svg';
                    selectAllIcon.alt = 'Unchecked';
                } else if (currentState.size === allValues.length) {
                    selectAllIcon.src = './assets/checkbox-ic.svg';
                    selectAllIcon.alt = 'Checked';
                } else {
                    selectAllIcon.src = './assets/indeterminate-ic.svg';
                    selectAllIcon.alt = 'Indeterminate';
                }
            }

            console.log('🔄 Regenerated checkbox list for', column.field, 'with', updatedUniqueValues.length, 'values');
        };

        // Event listeners with stopPropagation to prevent menu closing

        // Create "Select All" checkbox
        var selectAllItem = this.createCheckboxItem('Select All', true, true, column.field, applyFilter);
        checkboxList.appendChild(selectAllItem);

        // Create checkboxes for each unique value using centralized state
        uniqueValues.forEach(value => {
            const isChecked = this.getCheckboxState(column.field).has(value);
            const checkboxItem = this.createCheckboxItem(value, isChecked, false, column.field, applyFilter);
            checkboxList.appendChild(checkboxItem);
        });

        // Search functionality - no stopPropagation needed for search
        searchInput.addEventListener('input', (e) => {
            this.filterCheckboxList(checkboxList, e.target.value);
        });

        // Function to toggle second filter visibility (disabled for text-special, date-only, and fixed columns)
        const toggleSecondFilter = (show) => {
            if (isTextSpecial || isDateOnly || isFixed) return; // Don't show second filter for special column types

            // Respect "In range" mode: only show two inputs, never show logic or second dropdown
            const currentOperator = this.getCustomDropdownValue(firstDropdown);
            if (currentOperator === 'inRange' && isNumericOnly) {
                logicSection.style.display = 'none';
                secondDropdown.style.display = 'none';
                secondFilterWrapper.style.display = 'flex'; // Always show To input in range mode
                return;
            }

            // Also ensure that when typing into the first input while inRange is selected, we do not reveal AND/OR or the second dropdown
            if (currentOperator === 'inRange') {
                logicSection.style.display = 'none';
                secondDropdown.style.display = 'none';
            } else {
                // Show logic section and second filter when there's a valid first filter
                if (show && firstInput.value.length > 0) {
                    logicSection.style.display = 'flex';
                    secondDropdown.style.display = 'block';
                    secondFilterWrapper.style.display = 'flex';
                    console.log('🔍 Showing second filter and logic section');
                } else {
                    logicSection.style.display = 'none';
                    secondDropdown.style.display = 'none';
                    secondFilterWrapper.style.display = 'none';
                    console.log('🔍 Hiding second filter and logic section');
                }
            }

            // Default behavior for other operators
            const displayValue = show ? 'block' : 'none';
            logicSection.style.display = show ? 'flex' : 'none';
            secondDropdown.style.display = displayValue;
            secondFilterWrapper.style.display = displayValue;
        };



        // Consolidated filter input events
        firstInput.addEventListener('input', (e) => {
            const hasValue = e.target.value.length > 0;
            console.log('🔍 First input changed, hasValue:', hasValue);
            toggleSecondFilter(hasValue);
            applyFilter();
            // Regenerate current checkbox list to reflect filtered data
            setTimeout(() => regenerateCheckboxList(), 100);
        });

        firstInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                // Clear debounce timer and apply immediately on Enter
                if (this.filterDebounceTimer) {
                    clearTimeout(this.filterDebounceTimer);
                    this.filterDebounceTimer = null;
                }
                applyFilter();
            }
        });

        // Consolidated second input events
        secondInput.addEventListener('input', () => {
            console.log('🔍 Second input changed');
            applyFilter();
            // Regenerate current checkbox list to reflect filtered data
            setTimeout(() => regenerateCheckboxList(), 100);
        });

        secondInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                // Clear debounce timer and apply immediately on Enter
                if (this.filterDebounceTimer) {
                    clearTimeout(this.filterDebounceTimer);
                    this.filterDebounceTimer = null;
                }
                applyFilter();
            }
        });

        // Enhanced dropdown change events to handle "In range" selection
        firstDropdown.addEventListener('change', () => {
            const selectedOperator = this.getCustomDropdownValue(firstDropdown);
            console.log('🔍 First dropdown changed to:', selectedOperator);

            // Handle "In range" selection for numeric columns
            if (selectedOperator === 'inRange' && isNumericOnly) {
                firstInput.placeholder = 'From (min value)';
                secondFilterWrapper.style.display = 'flex';
                secondInput.placeholder = 'To (max value)';
                logicSection.style.display = 'none';
                secondDropdown.style.display = 'none';
                console.log('🔢 Numeric In range mode: showing only From/To inputs');
            }
            // Handle "In range" selection for date columns
            else if (selectedOperator === 'inRange' && isDateOnly) {
                firstFilterWrapper.style.display = 'flex';
                secondFilterWrapper.style.display = 'flex';
                firstInput.type = 'date';
                secondInput.type = 'date';
                firstInput.placeholder = 'From date';
                secondInput.placeholder = 'To date';
                logicSection.style.display = 'none';
                secondDropdown.style.display = 'none';
                console.log('📅 Date In range mode: showing date pickers');
            }


            // Handle date comparison operators that need input
            else if (isDateOnly && ['equals', 'notEquals', 'lessThan', 'greaterThan'].includes(selectedOperator)) {
                firstFilterWrapper.style.display = 'flex';
                secondFilterWrapper.style.display = 'none';
                firstInput.type = 'date';
                firstInput.placeholder = 'Select date';
                logicSection.style.display = 'none';
                secondDropdown.style.display = 'none';
                console.log('📅 Date comparison operator selected:', selectedOperator);



            }
            // Handle predefined date options
            else if (isDateOnly) {
                firstFilterWrapper.style.display = 'none';
                secondFilterWrapper.style.display = 'none';
                logicSection.style.display = 'none';
                secondDropdown.style.display = 'none';
                console.log('📅 Predefined date filter selected:', selectedOperator);
                // Apply filter immediately for predefined date ranges (including "Please Select" to clear filter)
                applyFilter();
            }
            else {
                // Reset to normal mode for other column types
                firstInput.placeholder = 'Filter..';
                secondInput.placeholder = 'Filter..';
                logicSection.style.display = 'flex';
                secondDropdown.style.display = 'block';

                // Show/hide second filter based on first input value
                const hasValue = firstInput.value.length > 0;
                toggleSecondFilter(hasValue);
            }

            // Always apply filter when dropdown changes, regardless of input value
            console.log('🔍 Applying filter due to dropdown change');
            applyFilter();
            // Regenerate current checkbox list to reflect filtered data
            setTimeout(() => regenerateCheckboxList(), 100);
        });

        secondDropdown.addEventListener('change', () => {
            const selectedOperator = this.getCustomDropdownValue(secondDropdown);
            console.log('🔍 Second dropdown changed to:', selectedOperator);
            applyFilter();
            // Regenerate current checkbox list to reflect filtered data
            setTimeout(() => regenerateCheckboxList(), 100);
        });

        // Assemble panel components based on column type
        if (columnType === 'text-special') {
            // text-special: Only search input and checkbox list
            panel.appendChild(searchWrapper);
            panel.appendChild(checkboxList);
        } else if (columnType === 'fixed') {
            // fixed: No filter components at all
            const noFilterMessage = document.createElement('div');
            noFilterMessage.className = 'no-filter-message';
            noFilterMessage.textContent = 'No filtering available for this column';
            panel.appendChild(noFilterMessage);
        } else if (columnType === 'date') {
            // date: Dropdown + conditional inputs (no checkbox list)
            panel.appendChild(firstDropdown);
            panel.appendChild(firstFilterWrapper);
            panel.appendChild(secondFilterWrapper);
        } else {
            // text, numeric, text-numeric: Full filter UI
            panel.appendChild(firstDropdown);
            panel.appendChild(firstFilterWrapper);
            panel.appendChild(logicSection);
            panel.appendChild(secondDropdown);
            panel.appendChild(secondFilterWrapper);
            panel.appendChild(divider);
            panel.appendChild(searchWrapper);
            panel.appendChild(checkboxList);
        }

        return panel;
    }

    /**
     * Create snap dropdown component
     */
    createCustomDropdown(options, defaultValue = '') {
        const dropdown = document.createElement('div');
        dropdown.className = 'snap-dropdown';

        const trigger = document.createElement('div');
        trigger.className = 'dropdown-header';

        const triggerText = document.createElement('span');
        triggerText.textContent = defaultValue;

        const arrow = document.createElement('img');
        arrow.src = './assets/dropdown-ic.svg';
        arrow.alt = 'Dropdown';
        arrow.className = 'dropdown-arrow';

        trigger.appendChild(triggerText);
        trigger.appendChild(arrow);

        const menu = document.createElement('div');
        menu.className = 'dropdown-menu hidden';

        // Parse options (can be HTML string or array)
        let optionsList = [];
        if (typeof options === 'string') {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = options;
            const optionElements = tempDiv.querySelectorAll('option');
            optionsList = Array.from(optionElements).map(opt => ({
                value: opt.value,
                text: opt.textContent
            }));
        } else if (Array.isArray(options)) {
            optionsList = options;
        }

        // Check if defaultValue matches any option value
        const matchingOption = optionsList.find(option => option.value === defaultValue);
        
        optionsList.forEach(option => {
            // Check if this is a divider
            if (option.divider) {
                const dividerElement = this.createMenuSeparator();
                menu.appendChild(dividerElement);
                return;
            }

            const optionElement = document.createElement('div');
            optionElement.className = 'dropdown-item';
            optionElement.textContent = option.text || option.value;
            optionElement.setAttribute('data-value', option.value);

            // Only add selected class if defaultValue matches an actual option value
            if (matchingOption && option.value === defaultValue) {
                optionElement.classList.add('selected');
                triggerText.textContent = option.text || option.value;
            }

            optionElement.addEventListener('click', (e) => {
                e.stopPropagation();

                // Update selected option
                menu.querySelectorAll('.dropdown-item').forEach(opt => {
                    opt.classList.remove('selected');
                });
                optionElement.classList.add('selected');
                triggerText.textContent = optionElement.textContent;

                // Close dropdown
                dropdown.classList.remove('focused');
                menu.classList.add('hidden');

                // Dispatch change event
                const changeEvent = new CustomEvent('change', {
                    detail: { value: option.value, text: option.text || option.value }
                });
                dropdown.dispatchEvent(changeEvent);
            });

            menu.appendChild(optionElement);
        });

        // Toggle dropdown
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();
            const isOpen = !menu.classList.contains('hidden');

            // Close all other dropdowns
            document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                d.classList.remove('focused');
                d.querySelector('.dropdown-menu').classList.add('hidden');
            });

            if (!isOpen) {
                dropdown.classList.add('focused');
                menu.classList.remove('hidden');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', () => {
            dropdown.classList.remove('focused');
            menu.classList.add('hidden');
        });

        dropdown.appendChild(trigger);
        dropdown.appendChild(menu);

        return dropdown;
    }

    /**
     * Get snap dropdown value
     */
    getCustomDropdownValue(dropdown) {
        const selectedOption = dropdown.querySelector('.dropdown-item.selected');
        return selectedOption ? selectedOption.getAttribute('data-value') : '';
    }

    /**
     * Set snap dropdown value
     */
    setCustomDropdownValue(dropdown, value) {
        const trigger = dropdown.querySelector('.dropdown-header span');
        const options = dropdown.querySelectorAll('.dropdown-item');

        options.forEach(option => {
            option.classList.remove('selected');
            if (option.getAttribute('data-value') === value) {
                option.classList.add('selected');
                trigger.textContent = option.textContent;
            }
        });
    }

    /**
     * Create checkbox item
     */
    createCheckboxItem(value, checked = false, isSelectAll = false, fieldName = null, applyFilterCallback = null) {
        const item = document.createElement('div');
        item.className = 'filter-checkbox-item';
        if (isSelectAll) {
            item.classList.add('select-all');
        }

        // Create checkbox wrapper using the app's checkbox system (same as Show/Hide)
        const checkboxWrapper = document.createElement('div');
        checkboxWrapper.className = 'checkbox-wrapper';

        const checkboxImg = document.createElement('img');
        checkboxImg.className = 'checkbox-icon';
        checkboxImg.draggable = false;
        checkboxImg.setAttribute('data-value', value);

        // Set appropriate icon based on state
        if (isSelectAll) {
            // Calculate proper initial state for Select All checkbox
            if (fieldName) {
                const currentState = this.getCheckboxState(fieldName);
                const allValues = this.getUniqueColumnValues(fieldName, true);

                if (currentState.size === 0) {
                    checkboxImg.src = './assets/uncheckedbox-ic.svg';
                    checkboxImg.alt = 'Unchecked';
                } else if (currentState.size === allValues.length) {
                    checkboxImg.src = './assets/checkbox-ic.svg';
                    checkboxImg.alt = 'Checked';
                } else {
                    checkboxImg.src = './assets/indeterminate-ic.svg';
                    checkboxImg.alt = 'Indeterminate';
                }
            } else {
                // Fallback to indeterminate if no field name
                checkboxImg.src = './assets/indeterminate-ic.svg';
                checkboxImg.alt = 'Select All';
            }
        } else {
            checkboxImg.src = checked ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
            checkboxImg.alt = checked ? 'Checked' : 'Unchecked';
        }

        const label = document.createElement('label');
        label.className = 'filter-checkbox-label';
        // Show value exactly like the cell displays, but keep raw value for matching
        label.textContent = isSelectAll ? String(value) : this.formatValueForField(fieldName, value);

        // Track checked state
        let isChecked = checked;

        // For Select All, set isChecked based on actual state
        if (isSelectAll && fieldName) {
            const currentState = this.getCheckboxState(fieldName);
            const allValues = this.getUniqueColumnValues(fieldName, true);
            isChecked = currentState.size === allValues.length;
        }

        // Toggle function using centralized state
        const toggleCheckbox = () => {
            if (isSelectAll) {
                // Select All logic using centralized state
                if (!fieldName) return;

                const currentState = this.getCheckboxState(fieldName);
                const allValues = this.getUniqueColumnValues(fieldName, true);
                const shouldCheckAll = currentState.size < allValues.length;

                console.log('🔄 Select All clicked:', {
                    field: fieldName,
                    totalValues: allValues.length,
                    currentlyChecked: currentState.size,
                    shouldCheckAll,
                    willApplyFilter: !!applyFilterCallback
                });

                // Update centralized state
                this.setAllCheckboxValues(fieldName, allValues, shouldCheckAll);

                // Update all visual checkboxes
                const checkboxList = item.parentElement;
                const allIcons = checkboxList.querySelectorAll('.checkbox-icon:not([data-value="Select All"])');
                allIcons.forEach(icon => {
                    icon.src = shouldCheckAll ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                    icon.alt = shouldCheckAll ? 'Checked' : 'Unchecked';
                });

                // Update Select All icon
                checkboxImg.src = shouldCheckAll ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                checkboxImg.alt = shouldCheckAll ? 'Checked' : 'Unchecked';
                isChecked = shouldCheckAll;

                // Apply filter using callback
                if (applyFilterCallback) {
                    console.log('🎯 Triggering filter application after Select All');
                    applyFilterCallback();
                } else {
                    console.warn('⚠️ No applyFilterCallback available');
                }
            } else {
                // Regular checkbox toggle using centralized state
                if (!fieldName) return;

                const currentlyChecked = this.getCheckboxState(fieldName).has(value);
                isChecked = !currentlyChecked;

                // Update centralized state
                this.setCheckboxValue(fieldName, value, isChecked);

                // Update visual state
                checkboxImg.src = isChecked ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                checkboxImg.alt = isChecked ? 'Checked' : 'Unchecked';

                // Update Select All visual state
                const checkboxList = item.parentElement;
                const selectAllIcon = checkboxList.querySelector('.checkbox-icon[data-value="Select All"]');
                if (selectAllIcon) {
                    const currentState = this.getCheckboxState(fieldName);
                    const allValues = this.getUniqueColumnValues(fieldName, true);

                    if (currentState.size === 0) {
                        selectAllIcon.src = './assets/uncheckedbox-ic.svg';
                        selectAllIcon.alt = 'Unchecked';
                    } else if (currentState.size === allValues.length) {
                        selectAllIcon.src = './assets/checkbox-ic.svg';
                        selectAllIcon.alt = 'Checked';
                    } else {
                        selectAllIcon.src = './assets/indeterminate-ic.svg';
                        selectAllIcon.alt = 'Indeterminate';
                    }
                }

                // Apply filter using callback
                if (applyFilterCallback) {
                    console.log('🎯 Triggering filter application after checkbox change');
                    applyFilterCallback();
                } else {
                    console.warn('⚠️ No applyFilterCallback available');
                }
            }
        };

        // Store checkbox state reference for Select All updates
        item._checkboxState = { isChecked };

        // Add single click handler to the entire item to prevent double-click issues
        item.addEventListener('click', (e) => {
            e.stopPropagation();
            toggleCheckbox();
        });

        checkboxWrapper.appendChild(checkboxImg);
        item.appendChild(checkboxWrapper);
        item.appendChild(label);

        return item;
    }

    /**
     * Get unique values from column data
     */
    getUniqueColumnValues(field, useFilteredData = false) {
        const values = new Set();
        const dataSource = useFilteredData ? this.getFilteredDataExcludingCheckboxes(field) : this.state.data;

        dataSource.forEach(row => {
            const value = row[field];
            if (value !== null && value !== undefined && value !== '') {
                values.add(String(value));
            }
        });
        
        const uniqueValues = Array.from(values);
        return this.sortValuesForField(field, uniqueValues);
    }

    /**
     * Sort values for specific fields based on predefined order
     */
    sortValuesForField(field, values) {
        // Define custom sort orders for specific fields
        const sortOrders = {
            'marketplace': ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'],
            'status': ['Draft', 'Translating', 'Under Review', 'Declined', 'Rejected', 'Processing', 'Timed out', 'Auto-uploaded', 'Live', 'Removed', 'Locked'],
            'productType': ['Standard t-shirt', 'Premium t-shirt', 'V-neck t-shirt', 'Tank top', 'Long sleeve t-shirt', 'Raglan', 'Sweatshirt', 'Pullover hoodie', 'Zip hoodie', 'PopSockets', 'iPhone cases', 'Samsung Galaxy cases', 'Tote bag', 'Throw pillows', 'Tumbler']
        };

        const sortOrder = sortOrders[field];
        if (!sortOrder) {
            // No custom sort order, return alphabetically sorted
            return values.sort();
        }

        // Sort values according to the predefined order
        const sortedValues = [];
        const remainingValues = [...values];

        // Add values in the predefined order
        sortOrder.forEach(orderValue => {
            if (remainingValues.includes(orderValue)) {
                sortedValues.push(orderValue);
                const index = remainingValues.indexOf(orderValue);
                remainingValues.splice(index, 1);
            }
        });

        // Add any remaining values that weren't in the predefined order (alphabetically sorted)
        sortedValues.push(...remainingValues.sort());

        console.log(`🔄 Sorted values for ${field}:`, sortedValues);
        return sortedValues;
    }

    /**
     * Get filtered data excluding checkbox filters for a specific field
     * This is used to populate checkbox lists based on other active filters
     */
    getFilteredDataExcludingCheckboxes(excludeField) {
        console.log('🔍 Getting filtered data excluding checkboxes for field:', excludeField);

        if (Object.keys(this.state.filterConfig).length === 0) {
            return [...this.state.data];
        }

        return this.state.data.filter(row => {
            return Object.entries(this.state.filterConfig).every(([field, filter]) => {
                // Skip checkbox filtering for the field we're generating checkboxes for
                if (field === excludeField) {
                    // FIXED: Apply only text/dropdown filters, completely ignore checkbox filters
                    const {
                        type,
                        operator,
                        value: filterValue,
                        secondOperator,
                        secondValue,
                        logicOperator
                    } = filter;

                    console.log('🔍 Applying text-only filter for excluded field:', {
                        field,
                        operator,
                        filterValue,
                        secondOperator,
                        secondValue
                    });

                    // Apply first condition only if operator and value are valid
                    let firstResult = true;
                    if (operator && operator !== 'pleaseSelect') {
                        if (operator === 'blank' || operator === 'notBlank') {
                            const value = this.getNestedValue(row, field);
                            firstResult = this.applySingleCondition(value, type, operator, filterValue);
                        } else if (filterValue !== undefined && filterValue !== null && filterValue.length > 0) {
                            const value = this.getNestedValue(row, field);
                            firstResult = this.applySingleCondition(value, type, operator, filterValue);
                        }
                    }

                    // Apply second condition only if operator and value are valid
                    let secondResult = true;
                    if (secondOperator && secondOperator !== 'pleaseSelect') {
                        if (secondOperator === 'blank' || secondOperator === 'notBlank') {
                            const value = this.getNestedValue(row, field);
                            secondResult = this.applySingleCondition(value, type, secondOperator, secondValue);
                        } else if (secondValue !== undefined && secondValue !== null && secondValue.length > 0) {
                            const value = this.getNestedValue(row, field);
                            secondResult = this.applySingleCondition(value, type, secondOperator, secondValue);
                        }
                    }

                    // FIXED: Combine results with logic operator if second condition exists
                    if (secondOperator && secondOperator !== 'pleaseSelect' &&
                        ((secondValue && secondValue.length > 0) || secondOperator === 'blank' || secondOperator === 'notBlank')) {

                        console.log('🔍 Applying logic in checkbox filter:', {
                            firstResult,
                            secondResult,
                            logicOperator,
                            operator,
                            value: filterValue,
                            secondOperator,
                            secondValue
                        });

                        const result = logicOperator === 'AND' ? (firstResult && secondResult) : (firstResult || secondResult);
                        console.log('🔍 Combined text filter result:', { firstResult, secondResult, logicOperator, result });
                        return result;
                    }

                    console.log('🔍 Single text filter result:', firstResult);
                    return firstResult;
                } else {
                    // Apply full filter (including checkboxes) for other fields
                    const value = this.getNestedValue(row, field);
                    return this.applyFilter(value, filter);
                }
            });
        });
    }





    /**
     * Regenerate checkbox lists for all open column menus except the specified field
     */
    regenerateAllCheckboxLists(excludeField) {
        // Find all open column menus with checkbox lists
        const openMenus = document.querySelectorAll('.snap-grid-column-menu');

        openMenus.forEach(menu => {
            const fieldName = menu.getAttribute('data-field');
            if (fieldName && fieldName !== excludeField) {
                const checkboxList = menu.querySelector('.filter-checkbox-list');
                if (checkboxList) {
                    this.regenerateCheckboxListForField(fieldName, checkboxList);
                }
            }
        });
    }

    /**
     * Regenerate checkbox list for a specific field
     */
    regenerateCheckboxListForField(fieldName, checkboxList) {
        // Get current search term from the menu
        const menu = checkboxList.closest('.snap-grid-column-menu');
        const searchInput = menu.querySelector('.filter-search-input');
        const searchTerm = searchInput ? searchInput.value : '';

        // Get updated unique values based on current filters
        const updatedUniqueValues = this.getUniqueColumnValues(fieldName, true);

        // Clear existing checkboxes (except Select All)
        const existingItems = checkboxList.querySelectorAll('.filter-checkbox-item:not(.select-all)');
        existingItems.forEach(item => item.remove());

        // Preserve current checkbox state
        const currentState = this.getCheckboxState(fieldName);

        // Create new checkboxes for updated values
        updatedUniqueValues.forEach(value => {
            const isChecked = currentState.has(value);
            const checkboxItem = this.createCheckboxItem(value, isChecked, false, fieldName, () => {
                // Find the applyFilter function for this menu
                const applyFilterEvent = new CustomEvent('applyFilter', { detail: { field: fieldName } });
                menu.dispatchEvent(applyFilterEvent);
            });
            checkboxList.appendChild(checkboxItem);
        });

        // Reapply search filter if there was one
        if (searchTerm) {
            this.filterCheckboxList(checkboxList, searchTerm);
        }

        // Update Select All state
        const selectAllIcon = checkboxList.querySelector('.checkbox-icon[data-value="Select All"]');
        if (selectAllIcon) {
            const allValues = updatedUniqueValues;
            if (currentState.size === 0) {
                selectAllIcon.src = './assets/uncheckedbox-ic.svg';
                selectAllIcon.alt = 'Unchecked';
            } else if (currentState.size === allValues.length) {
                selectAllIcon.src = './assets/checkbox-ic.svg';
                selectAllIcon.alt = 'Checked';
            } else {
                selectAllIcon.src = './assets/indeterminate-ic.svg';
                selectAllIcon.alt = 'Indeterminate';
            }
        }

        console.log('🔄 Regenerated checkbox list for', fieldName, 'with', updatedUniqueValues.length, 'values');
    }

    /**
     * Filter checkbox list based on search term
     */
    filterCheckboxList(checkboxList, searchTerm) {
        const items = checkboxList.querySelectorAll('.filter-checkbox-item:not(.select-all)');
        const lowerSearchTerm = searchTerm.toLowerCase();

        items.forEach(item => {
            const label = item.querySelector('.filter-checkbox-label');
            const text = label.textContent.toLowerCase();

            if (text.includes(lowerSearchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    /**
     * Create column management tab content
     */
    createManagementTab(column) {
        const panel = document.createElement('div');
        panel.className = 'menu-tab-panel';
        panel.setAttribute('data-tab-panel', 'management');

        // Sort Ascending option
        const sortAscOption = this.createMenuOptionWithIcon('Sort Ascending', './assets/ascending-ic.svg', () => {
            this.sortColumn(column.field, 'asc');
        });
        panel.appendChild(sortAscOption);

        // Sort Descending option
        const sortDescOption = this.createMenuOptionWithIcon('Sort Descending', './assets/descending-ic.svg', () => {
            this.sortColumn(column.field, 'desc');
        });
        panel.appendChild(sortDescOption);

        // First divider
        const divider1 = document.createElement('div');
        divider1.className = 'column-management-divider';
        panel.appendChild(divider1);

        // Pin Column option with submenu
        const pinContainer = document.createElement('div');
        pinContainer.className = 'pin-column-container';

        const pinOption = document.createElement('div');
        pinOption.className = 'menu-option pin-column-option';

        const pinMain = document.createElement('div');
        pinMain.className = 'pin-column-main';

        const pinIcon = document.createElement('img');
        pinIcon.src = './assets/pin-col-ic.svg';
        pinIcon.alt = 'Pin';
        pinIcon.className = 'menu-option-icon';

        const pinText = document.createElement('span');
        pinText.textContent = 'Pin Column';

        const pinArrow = document.createElement('img');
        pinArrow.src = './assets/arrow-ic.svg';
        pinArrow.alt = 'Arrow';
        pinArrow.className = 'pin-arrow';

        pinMain.appendChild(pinIcon);
        pinMain.appendChild(pinText);
        pinOption.appendChild(pinMain);
        pinOption.appendChild(pinArrow);

        // Pin submenu
        const pinSubmenu = document.createElement('div');
        pinSubmenu.className = 'pin-submenu hidden';

        const currentPinState = this.getColumnPinState(column.field);

        // Pin Left option
        const pinLeftOption = document.createElement('div');
        pinLeftOption.className = 'pin-option';
        const pinLeftCheck = document.createElement('img');
        pinLeftCheck.src = './assets/checked-option-ic.svg';
        pinLeftCheck.className = `check-icon ${currentPinState === 'left' ? '' : 'hidden'}`;
        const pinLeftText = document.createElement('span');
        pinLeftText.textContent = 'Pin Left';
        pinLeftOption.appendChild(pinLeftCheck);
        pinLeftOption.appendChild(pinLeftText);
        pinLeftOption.addEventListener('click', () => {
            this.pinColumn(column.field, 'left');
            this.updatePinSubmenu(column.field, pinSubmenu);
        });

        // Pin Right option
        const pinRightOption = document.createElement('div');
        pinRightOption.className = 'pin-option';
        const pinRightCheck = document.createElement('img');
        pinRightCheck.src = './assets/checked-option-ic.svg';
        pinRightCheck.className = `check-icon ${currentPinState === 'right' ? '' : 'hidden'}`;
        const pinRightText = document.createElement('span');
        pinRightText.textContent = 'Pin Right';
        pinRightOption.appendChild(pinRightCheck);
        pinRightOption.appendChild(pinRightText);
        pinRightOption.addEventListener('click', () => {
            this.pinColumn(column.field, 'right');
            this.updatePinSubmenu(column.field, pinSubmenu);
        });

        // Don't Pin option
        const dontPinOption = document.createElement('div');
        dontPinOption.className = 'pin-option';
        const dontPinCheck = document.createElement('img');
        dontPinCheck.src = './assets/checked-option-ic.svg';
        dontPinCheck.className = `check-icon ${currentPinState === 'none' ? '' : 'hidden'}`;
        const dontPinText = document.createElement('span');
        dontPinText.textContent = "Don't Pin";
        dontPinOption.appendChild(dontPinCheck);
        dontPinOption.appendChild(dontPinText);
        dontPinOption.addEventListener('click', () => {
            this.unpinColumn(column.field);
            this.updatePinSubmenu(column.field, pinSubmenu);
        });

        pinSubmenu.appendChild(pinLeftOption);
        pinSubmenu.appendChild(pinRightOption);
        pinSubmenu.appendChild(dontPinOption);

        // Pin option hover/click behavior - only on pin-column-main
        pinMain.addEventListener('mouseenter', () => {
            pinSubmenu.classList.remove('hidden');
        });
        pinContainer.addEventListener('mouseleave', () => {
            pinSubmenu.classList.add('hidden');
        });

        pinContainer.appendChild(pinOption);
        pinContainer.appendChild(pinSubmenu);
        panel.appendChild(pinContainer);

        // Second divider
        const divider2 = document.createElement('div');
        divider2.className = 'column-management-divider';
        panel.appendChild(divider2);

        // Autosize This Column option
        const autosizeThisOption = this.createMenuOptionWithIcon('Autosize This Column', './assets/autoresize-ic.svg', () => {
            this.autosizeColumn(column.field);
        });
        panel.appendChild(autosizeThisOption);

        // Autosize All Columns option
        const autosizeAllOption = this.createMenuOptionWithIcon('Autosize All Columns', './assets/autoresize-ic.svg', () => {
            this.autosizeAllColumns();
        });
        panel.appendChild(autosizeAllOption);

        // Third divider
        const divider3 = document.createElement('div');
        divider3.className = 'column-management-divider';
        panel.appendChild(divider3);

        // Reset Columns option (no icon)
        const resetOption = document.createElement('div');
        resetOption.className = 'menu-option reset-option';
        const resetText = document.createElement('span');
        resetText.textContent = 'Reset Columns';
        resetOption.appendChild(resetText);
        resetOption.addEventListener('click', () => {
            this.resetColumns();
        });
        panel.appendChild(resetOption);

        return panel;
    }

    /**
     * Create column visibility tab content
     */
    createVisibilityTab() {
        const panel = document.createElement('div');
        panel.className = 'menu-tab-panel';
        panel.setAttribute('data-tab-panel', 'visibility');

        // Search input with icon (matching Filter tab style) - need wrapper for proper positioning
        const searchWrapper = document.createElement('div');
        searchWrapper.className = 'search-input-wrapper';

        const searchIcon = document.createElement('img');
        searchIcon.src = './assets/search-ic.svg';
        searchIcon.className = 'search-icon';
        searchIcon.alt = 'Search';

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'search-input';
        searchInput.placeholder = 'Search..';

        searchWrapper.appendChild(searchIcon);
        searchWrapper.appendChild(searchInput);

        // Add stopPropagation to search input
        searchInput.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        searchInput.addEventListener('keydown', (e) => {
            e.stopPropagation();
        });

        // Column list container
        const columnList = document.createElement('div');
        columnList.className = 'column-list';

        // Add drag and drop support to the column list container
        columnList.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            // Update drag indicator position based on mouse position
            const dragIndicator = columnList.querySelector('.drag-indicator');
            if (dragIndicator) {
                const afterElement = this.getDragAfterElement(columnList, e.clientY);
                this.updateDragIndicatorPosition(dragIndicator, columnList, afterElement);
            }
        });

        columnList.addEventListener('drop', (e) => {
            e.preventDefault();
            e.stopPropagation();
            const draggedField = e.dataTransfer.getData('text/plain');

            if (draggedField && this.reorderColumnByIndex) {
                try {
                    // Don't allow dropping fixed columns
                    if (this.isFixedColumn(draggedField)) {
                        return;
                    }

                    // Calculate insertion position based on drop location
                    const afterElement = this.getDragAfterElement(columnList, e.clientY);
                    const validRange = this.getValidInsertionRange();

                    let insertionIndex;
                    if (afterElement == null) {
                        // Insert at the end, but respect valid range
                        insertionIndex = Math.min(validRange.maxIndex, this.state.columnOrder.length);
                    } else {
                        // Insert before the afterElement
                        const afterField = afterElement.getAttribute('data-field');
                        insertionIndex = this.state.columnOrder.indexOf(afterField);

                        // Validate that the insertion position is within valid range
                        if (insertionIndex < validRange.minIndex || insertionIndex > validRange.maxIndex) {
                            // Invalid drop position, don't perform the reorder
                            return;
                        }
                    }

                    this.reorderColumnByIndex(draggedField, insertionIndex);
                } catch (error) {
                    console.error('Error during column reordering:', error);
                    // Don't re-throw the error to prevent it from bubbling up
                }
            }

            // Always remove any existing drag indicator after drop
            const di = columnList.querySelector('.drag-indicator');
            if (di) di.remove();
        });

        // Add Select All checkbox as first item in the column list
        const selectAllItem = document.createElement('div');
        selectAllItem.className = 'column-item select-all-item';
        
        const selectAllCheckboxWrapper = document.createElement('div');
        selectAllCheckboxWrapper.className = 'checkbox-wrapper';
        
        const selectAllCheckbox = document.createElement('img');
        selectAllCheckbox.className = 'checkbox-icon';
        selectAllCheckbox.draggable = false;
        // Don't set initial src - let updateSelectAllStateInList set the correct state
        selectAllCheckbox.alt = 'Select All';
        
        const selectAllLabel = document.createElement('label');
        selectAllLabel.textContent = 'Select All';
        selectAllLabel.className = 'column-label';
        
        selectAllCheckboxWrapper.appendChild(selectAllCheckbox);
        selectAllItem.appendChild(selectAllCheckboxWrapper);
        selectAllItem.appendChild(selectAllLabel);
        
        // Add Select All functionality
        selectAllItem.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleSelectAllColumnsInList(selectAllCheckbox, columnList);
        });
        
        // Add Select All as first item in the list
        columnList.appendChild(selectAllItem);

        // Create column items in current order (exclude fixed columns and marketplace)
        this.getOrderedColumns().forEach((column, index) => {
            if (this.getColumnType(column.field) === 'fixed' || column.field === 'marketplace') return;
            const item = this.createVisibilityColumnItem(column);
            item.setAttribute('data-column-index', index);
            columnList.appendChild(item);
        });
        
        // Initialize Select All state based on current column visibility (after all items are created)
        this.updateSelectAllStateInList(selectAllCheckbox, columnList);

        // Search functionality
        searchInput.addEventListener('input', (e) => {
            e.stopPropagation();
            const searchTerm = e.target.value.toLowerCase();
            const items = columnList.querySelectorAll('.column-item');

            items.forEach(item => {
                const label = item.querySelector('label').textContent.toLowerCase();
                item.style.display = label.includes(searchTerm) ? 'flex' : 'none';
            });
        });

        panel.appendChild(searchWrapper);
        panel.appendChild(columnList);

        return panel;
    }

    /**
     * Toggle Select All columns functionality for columns in the list
     */
    toggleSelectAllColumnsInList(selectAllCheckbox, columnList) {
        const isCurrentlyAllSelected = this.areAllColumnsInListVisible(columnList);
        
        if (isCurrentlyAllSelected) {
            // Deselect all columns in the list (hide them)
            this.hideAllColumnsInList(columnList);
            selectAllCheckbox.src = './assets/uncheckedbox-ic.svg';
            selectAllCheckbox.alt = 'Unchecked';
        } else {
            // Select all columns in the list (show them)
            this.showAllColumnsInList(columnList);
            selectAllCheckbox.src = './assets/checkbox-ic.svg';
            selectAllCheckbox.alt = 'Checked';
        }
        
        // Update individual column checkboxes in the list
        this.updateColumnCheckboxesInList(columnList);
    }

    /**
     * Update Select All checkbox state based on current column visibility
     */
    updateSelectAllState(selectAllCheckbox) {
        const visibleCount = this.getVisibleColumnsCount();
        const totalCount = this.getTotalColumnsCount();
        
        if (visibleCount === 0) {
            selectAllCheckbox.src = './assets/uncheckedbox-ic.svg';
            selectAllCheckbox.alt = 'Unchecked';
        } else if (visibleCount === totalCount) {
            selectAllCheckbox.src = './assets/checkbox-ic.svg';
            selectAllCheckbox.alt = 'Checked';
        } else {
            selectAllCheckbox.src = './assets/indeterminate-ic.svg';
            selectAllCheckbox.alt = 'Indeterminate';
        }
    }

    /**
     * Check if all columns are currently visible
     */
    areAllColumnsVisible() {
        const visibleCount = this.getVisibleColumnsCount();
        const totalCount = this.getTotalColumnsCount();
        return visibleCount === totalCount;
    }

    /**
     * Get count of visible columns (excluding fixed columns)
     */
    getVisibleColumnsCount() {
        return this.config.columns.filter(col => 
            !this.state.hiddenColumns.has(col.field) && 
            col.field !== 'checkbox' && 
            col.field !== 'preview' && 
            col.field !== 'actions'
        ).length;
    }

    /**
     * Get total count of selectable columns (excluding fixed columns)
     */
    getTotalColumnsCount() {
        return this.config.columns.filter(col => 
            col.field !== 'checkbox' && 
            col.field !== 'preview' && 
            col.field !== 'actions'
        ).length;
    }

    /**
     * Hide all selectable columns
     */
    hideAllColumns() {
        this.config.columns.forEach(col => {
            if (col.field !== 'checkbox' && col.field !== 'preview' && col.field !== 'actions') {
                this.hideColumn(col.field);
            }
        });
    }

    /**
     * Show all selectable columns
     */
    showAllColumns() {
        this.config.columns.forEach(col => {
            if (col.field !== 'checkbox' && col.field !== 'preview' && col.field !== 'actions') {
                this.showColumn(col.field);
            }
        });
    }

    /**
     * Update individual column checkboxes in visibility tab
     */
    updateVisibilityColumnCheckboxes() {
        const visibilityTab = document.querySelector('[data-tab-panel="visibility"]');
        if (!visibilityTab) return;
        
        const columnItems = visibilityTab.querySelectorAll('.column-item:not(.select-all-item)');
        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field) {
                const checkbox = item.querySelector('.checkbox-icon');
                if (checkbox) {
                    const isVisible = !this.state.hiddenColumns.has(field);
                    checkbox.src = isVisible ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                    checkbox.alt = isVisible ? 'Checked' : 'Unchecked';
                }
            }
        });
    }

    /**
     * Check if all columns in the list are currently visible
     */
    areAllColumnsInListVisible(columnList) {
        const columnItems = columnList.querySelectorAll('.column-item:not(.select-all-item)');
        let visibleCount = 0;
        
        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field && !this.state.hiddenColumns.has(field)) {
                visibleCount++;
            }
        });
        
        return visibleCount === columnItems.length;
    }

    /**
     * Hide all columns that are in the list
     */
    hideAllColumnsInList(columnList) {
        const columnItems = columnList.querySelectorAll('.column-item:not(.select-all-item)');
        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field) {
                this.hideColumn(field);
            }
        });
    }

    /**
     * Show all columns that are in the list
     */
    showAllColumnsInList(columnList) {
        const columnItems = columnList.querySelectorAll('.column-item:not(.select-all-item)');
        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field) {
                this.showColumn(field);
            }
        });
    }

    /**
     * Update individual column checkboxes in the list
     */
    updateColumnCheckboxesInList(columnList) {
        const columnItems = columnList.querySelectorAll('.column-item:not(.select-all-item)');
        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field) {
                const checkbox = item.querySelector('.checkbox-icon');
                if (checkbox) {
                    const isVisible = !this.state.hiddenColumns.has(field);
                    checkbox.src = isVisible ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                    checkbox.alt = isVisible ? 'Checked' : 'Unchecked';
                }
            }
        });
    }

    /**
     * Update Select All state in visibility tab
     */
    updateSelectAllStateInVisibilityTab() {
        const visibilityTab = document.querySelector('[data-tab-panel="visibility"]');
        if (!visibilityTab) return;
        
        const columnList = visibilityTab.querySelector('.column-list');
        const selectAllCheckbox = visibilityTab.querySelector('.select-all-item .checkbox-icon');
        if (selectAllCheckbox && columnList) {
            this.updateSelectAllStateInList(selectAllCheckbox, columnList);
        }
    }

    /**
     * Update Select All state based on columns in the list
     */
    updateSelectAllStateInList(selectAllCheckbox, columnList) {
        const columnItems = columnList.querySelectorAll('.column-item:not(.select-all-item)');
        let visibleCount = 0;
        
        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field && !this.state.hiddenColumns.has(field)) {
                visibleCount++;
            }
        });
        
        if (visibleCount === 0) {
            selectAllCheckbox.src = './assets/uncheckedbox-ic.svg';
            selectAllCheckbox.alt = 'Unchecked';
        } else if (visibleCount === columnItems.length) {
            selectAllCheckbox.src = './assets/checkbox-ic.svg';
            selectAllCheckbox.alt = 'Checked';
        } else {
            selectAllCheckbox.src = './assets/indeterminate-ic.svg';
            selectAllCheckbox.alt = 'Indeterminate';
        }
    }

    /**
     * Add drag and drop listeners to column item
     */
    addColumnDragListeners(item, column) {
        item.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', column.field);
            e.dataTransfer.effectAllowed = 'move';
            item.classList.add('dragging');

            // Create and add drag indicator if it doesn't exist
            const columnList = item.parentElement;
            let dragIndicator = columnList.querySelector('.drag-indicator');
            if (!dragIndicator) {
                dragIndicator = document.createElement('div');
                dragIndicator.className = 'drag-indicator';
                columnList.appendChild(dragIndicator);
            }

            // Show the indicator immediately
            dragIndicator.classList.add('active');
        });

        item.addEventListener('dragend', () => {
            item.classList.remove('dragging');
            // Remove drag indicator
            const columnList = item.parentElement;
            if (columnList) {
                const dragIndicator = columnList.querySelector('.drag-indicator');
                if (dragIndicator) {
                    dragIndicator.remove();
                }
                // Remove all drag-over classes
                columnList.querySelectorAll('.column-item').forEach(el => {
                    el.classList.remove('drag-over');
                });
            }
        });

        item.addEventListener('dragenter', (e) => {
            e.preventDefault();
            item.classList.add('drag-over');
        });

        item.addEventListener('dragleave', (e) => {
            if (!item.contains(e.relatedTarget)) {
                item.classList.remove('drag-over');
            }
        });


    }

    /**
     * Get the element after which the dragged item should be inserted
     */
    getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.column-item:not(.dragging)')];

        // Get valid insertion range
        const validRange = this.getValidInsertionRange();

        // Filter elements to only include those in valid positions
        const validElements = draggableElements.filter((_, index) => {
            // Check if this position would be within valid range
            return index >= validRange.minIndex - 1 && index < validRange.maxIndex;
        });

        return validElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;

            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    /**
     * Update drag indicator position based on drop target
     */
    updateDragIndicatorPosition(dragIndicator, columnList, afterElement) {
        if (!dragIndicator) return;

        const columnListRect = columnList.getBoundingClientRect();
        const validRange = this.getValidInsertionRange();

        // Account for scroll position
        const scrollTop = columnList.scrollTop;

        // Get all column items (excluding the one being dragged)
        const allItems = [...columnList.querySelectorAll('.column-item:not(.dragging)')];

        if (afterElement == null) {
            // Position at the end, but respect valid range
            const maxValidIndex = Math.min(validRange.maxIndex - 1, allItems.length - 1);
            const lastValidItem = allItems[maxValidIndex];

            if (lastValidItem) {
                const rect = lastValidItem.getBoundingClientRect();
                dragIndicator.style.top = (rect.bottom - columnListRect.top + scrollTop) + 'px';
            } else {
                // No valid items, position at minimum valid position
                const minValidItem = allItems[validRange.minIndex - 1];
                if (minValidItem) {
                    const rect = minValidItem.getBoundingClientRect();
                    dragIndicator.style.top = (rect.bottom - columnListRect.top + scrollTop) + 'px';
                } else {
                    dragIndicator.style.top = scrollTop + 'px';
                }
            }
        } else {
            // Position before the afterElement, but ensure it's within valid range
            const afterIndex = allItems.indexOf(afterElement);
            if (afterIndex >= validRange.minIndex - 1 && afterIndex < validRange.maxIndex) {
                const rect = afterElement.getBoundingClientRect();
                dragIndicator.style.top = (rect.top - columnListRect.top + scrollTop) + 'px';
            } else {
                // Invalid position, don't show indicator
                dragIndicator.classList.remove('active');
                return;
            }
        }

        dragIndicator.style.left = '0px';
        dragIndicator.style.right = '0px';
        dragIndicator.classList.add('active');
    }

    /**
     * Reorder column by insertion index
     */
    reorderColumnByIndex(draggedField, insertionIndex) {
        if (this.isFixedColumn(draggedField)) return;

        const currentOrder = [...this.state.columnOrder];
        const draggedIndex = currentOrder.indexOf(draggedField);

        if (draggedIndex === -1) return;

        // Remove the dragged item from its current position
        currentOrder.splice(draggedIndex, 1);

        // Adjust insertion index if we removed an item before it
        let adjustedInsertionIndex = insertionIndex;
        if (draggedIndex < insertionIndex) {
            adjustedInsertionIndex--;
        }

        // Recalculate valid range after removing the dragged item
        const updatedValidRange = this.getValidInsertionRangeForOrder(currentOrder);

        // Ensure insertion index is within valid bounds (respecting locked columns)
        adjustedInsertionIndex = Math.max(updatedValidRange.minIndex, Math.min(adjustedInsertionIndex, updatedValidRange.maxIndex));

        // Insert at the new position
        currentOrder.splice(adjustedInsertionIndex, 0, draggedField);

        // Update the state
        this.state.columnOrder = currentOrder;

        // Re-render the grid to reflect the new order
        this.render();

        // Update any open column menus' visibility lists to reflect new order
        this.updateAllOpenVisibilityLists();

        // Emit event
        this.emit('columnReordered', { draggedField, insertionIndex: adjustedInsertionIndex, newOrder: currentOrder });
    }

    /**
     * Reorder column in the grid (legacy method for backward compatibility)
     */
    reorderColumn(draggedField, targetField) {
        if (this.isFixedColumn(draggedField) || this.isFixedColumn(targetField)) return;

        const currentOrder = [...this.state.columnOrder];
        const draggedIndex = currentOrder.indexOf(draggedField);
        const targetIndex = currentOrder.indexOf(targetField);

        if (draggedIndex === -1 || targetIndex === -1) return;

        // Remove the dragged item
        currentOrder.splice(draggedIndex, 1);

        // Insert at the new position
        const newTargetIndex = draggedIndex < targetIndex ? targetIndex : targetIndex + 1;
        currentOrder.splice(newTargetIndex, 0, draggedField);

        // Update the state
        this.state.columnOrder = currentOrder;

        // Re-render the grid to reflect the new order
        this.render();

        // Update any open column menus' visibility lists to reflect new order
        this.updateAllOpenVisibilityLists();

        // Emit event
        this.emit('columnReordered', { draggedField, targetField, newOrder: currentOrder });
    }

    /**
     * Update the column management list to reflect current order
     */
    updateColumnManagementList(columnList) {
        // Clear existing items
        columnList.innerHTML = '';

        // Rebuild the list in the new order using the same format as createVisibilityTab (exclude checkbox, preview, and actions)
        this.state.columnOrder.forEach(field => {
            const column = this.config.columns.find(col => col.field === field);
            if (column && column.field !== 'checkbox' && column.field !== 'preview' && column.field !== 'actions') {
                const item = this.createVisibilityColumnItem(column);
                columnList.appendChild(item);
            }
        });
    }

    /**
     * Update all open visibility lists (in any open column menus)
     * Keeps the Show/Hide list in sync with header moves
     */
    updateAllOpenVisibilityLists() {
        if (!this.container) return;
        const menus = this.container.querySelectorAll('.snap-grid-column-menu');
        menus.forEach(menu => {
            const visPanel = menu.querySelector('[data-tab-panel="visibility"].menu-tab-panel');
            if (visPanel) {
                const list = visPanel.querySelector('.column-list');
                if (list) {
                    // Completely rebuild, using the same builder used initially
                    this.updateColumnManagementList(list);
                }
            }
        });
    }

    /**
     * Create a column item for the visibility tab (matching the original format)
     */
    createVisibilityColumnItem(column) {
        const item = document.createElement('div');
        item.className = 'column-item';
        item.setAttribute('data-field', column.field);

        const isFixed = this.isFixedColumn(column.field);
        item.draggable = !isFixed;
        if (isFixed) item.classList.add('fixed');

        // Create checkbox wrapper using the app's checkbox system
        const checkboxWrapper = document.createElement('div');
        checkboxWrapper.className = 'checkbox-wrapper';

        const checkboxImg = document.createElement('img');
        checkboxImg.className = 'checkbox-icon';
        checkboxImg.draggable = false;
        const isVisible = !this.state.hiddenColumns.has(column.field);
        checkboxImg.src = isVisible ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
        checkboxImg.alt = isVisible ? 'Checked' : 'Unchecked';

        // Add grip handle for reordering
        const gripHandle = document.createElement('img');
        gripHandle.src = './assets/grip-ic.svg';
        gripHandle.className = 'grip-handle';
        gripHandle.alt = isFixed ? 'Reordering disabled' : 'Drag to reorder';
        gripHandle.draggable = false;
        if (isFixed) {
            gripHandle.style.opacity = '0.35';
            gripHandle.style.cursor = 'not-allowed';
        }

        const label = document.createElement('label');
        label.textContent = column.headerName || column.field;

        // Toggle functionality
        const toggleColumn = (e) => {
            e.stopPropagation();
            const currentlyVisible = !this.state.hiddenColumns.has(column.field);
            if (currentlyVisible) {
                this.hideColumn(column.field);
                checkboxImg.src = './assets/uncheckedbox-ic.svg';
                checkboxImg.alt = 'Unchecked';
            } else {
                this.showColumn(column.field);
                checkboxImg.src = './assets/checkbox-ic.svg';
                checkboxImg.alt = 'Checked';
            }
            
            // Update Select All state in the visibility tab
            this.updateSelectAllStateInVisibilityTab();
        };

        checkboxWrapper.addEventListener('click', toggleColumn);
        label.addEventListener('click', toggleColumn);

        // Add drag and drop event listeners only if not fixed
        if (!isFixed) {
            this.addColumnDragListeners(item, column);
        }

        checkboxWrapper.appendChild(checkboxImg);
        item.appendChild(checkboxWrapper);
        item.appendChild(gripHandle);
        item.appendChild(label);

        return item;
    }

    /**
     * Determine if a column is fixed (non-draggable/non-reorderable)
     */
    isFixedColumn(field) {
        return this.getColumnType(field) === 'fixed';
    }

    /**
     * Get the valid insertion range for draggable columns
     * Returns {minIndex, maxIndex} where columns can be inserted
     */
    getValidInsertionRange() {
        return this.getValidInsertionRangeForOrder(this.state.columnOrder);
    }

    /**
     * Get the valid insertion range for a specific column order
     * Returns {minIndex, maxIndex} where columns can be inserted
     */
    getValidInsertionRangeForOrder(columnOrder) {
        // Since Preview and Actions are now hidden from the Show/Hide list,
        // we allow full reordering of visible columns without restrictions
        return {
            minIndex: 0,
            maxIndex: columnOrder.length
        };
    }

    /**
     * Create a single column management item
     */
    createColumnManagementItem(column) {
        const item = document.createElement('div');
        item.className = 'column-item';
        const isFixed = this.isFixedColumn(column.field);
        item.draggable = !isFixed;
        if (isFixed) item.classList.add('fixed');
        item.setAttribute('data-field', column.field);

        const gripHandle = document.createElement('div');
        gripHandle.className = 'grip-handle';
        gripHandle.innerHTML = '⋮⋮';

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = !this.state.hiddenColumns.has(column.field);
        checkbox.addEventListener('change', (e) => {
            if (e.target.checked) {
                this.showColumn(column.field);
            } else {
                this.hideColumn(column.field);
            }
        });

        const label = document.createElement('span');
        label.textContent = column.headerName || column.field;

        item.appendChild(gripHandle);
        item.appendChild(checkbox);
        item.appendChild(label);

        // Add drag and drop listeners
        this.addColumnDragListeners(item, column);

        return item;
    }

    /**
     * Setup tab switching functionality
     */
    setupTabSwitching(menu) {
        const tabButtons = menu.querySelectorAll('.menu-tab-btn');

        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                const tabId = button.getAttribute('data-tab');
                this.setActiveTab(menu, tabId);
            });
        });
    }

    /**
     * Set active tab
     */
    setActiveTab(menu, tabId) {
        // Update tab buttons
        const tabButtons = menu.querySelectorAll('.menu-tab-btn');
        tabButtons.forEach(btn => {
            const isActive = btn.getAttribute('data-tab') === tabId;
            btn.classList.toggle('active', isActive);

            // Update icon based on active state
            const icon = btn.querySelector('.tab-icon');
            if (icon) {
                const iconName = this.getIconNameFromTab(btn.getAttribute('data-tab'));
                const state = isActive ? 'active' : 'inactive';
                icon.src = `./assets/${iconName}-${state}-ic.svg`;
            }
        });

        // Update tab panels
        const tabPanels = menu.querySelectorAll('.menu-tab-panel');
        tabPanels.forEach(panel => {
            panel.classList.toggle('active', panel.getAttribute('data-tab-panel') === tabId);
        });
    }

    /**
     * Get icon name from tab ID
     */
    getIconNameFromTab(tabId) {
        const iconMap = {
            'filter': 'filters',
            'management': 'column-man',
            'visibility': 'show-hide-col'
        };
        return iconMap[tabId] || 'filters';
    }

    /**
     * Create logic toggle for AND/OR filter logic
     */
    createLogicToggle(text, isActive, logicSection, applyFilter, firstInput, secondInput, firstDropdown, secondDropdown) {
        
        const wrapper = document.createElement('div');
        wrapper.className = 'logic-toggle';
        wrapper.setAttribute('data-logic', text);

        const checkbox = document.createElement('img');
        checkbox.className = 'logic-checkbox';
        checkbox.src = isActive ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
        checkbox.alt = isActive ? 'Checked' : 'Unchecked';
        checkbox.draggable = false;

        const label = document.createElement('span');
        label.textContent = text;
        label.className = 'logic-label';

        wrapper.appendChild(checkbox);
        wrapper.appendChild(label);

        // Toggle functionality - mutually exclusive
        wrapper.addEventListener('click', (e) => {
            e.stopPropagation();

            // Get both toggles
            const andToggle = logicSection.querySelector('[data-logic="AND"]');
            const orToggle = logicSection.querySelector('[data-logic="OR"]');

            // Reset both to unchecked
            const andCheckbox = andToggle.querySelector('.logic-checkbox');
            const orCheckbox = orToggle.querySelector('.logic-checkbox');

            andCheckbox.src = './assets/uncheckedbox-ic.svg';
            andCheckbox.alt = 'Unchecked';
            
            orCheckbox.src = './assets/uncheckedbox-ic.svg';
            orCheckbox.alt = 'Unchecked';

            // Set clicked one to checked
            checkbox.src = './assets/checkbox-ic.svg';
            checkbox.alt = 'Checked';
            
            console.log('🔍 Logic toggle clicked:', text, 'is now active');
            
            // Trigger filter immediately when AND/OR logic changes
            if (applyFilter && typeof applyFilter === 'function') {
                console.log('🔍 Applying filter due to logic change to:', text);
                applyFilter();
            }
        });

        return wrapper;
    }

    /**
     * Create menu option with icon
     */
    createMenuOptionWithIcon(text, iconSrc, action) {
        const option = document.createElement('div');
        option.className = 'menu-option-with-icon';

        const iconWrapper = document.createElement('div');
        iconWrapper.className = 'menu-option-icon-wrapper';

        const icon = document.createElement('img');
        icon.src = iconSrc;
        icon.className = 'menu-option-icon';
        icon.alt = text;
        icon.draggable = false;

        const label = document.createElement('span');
        label.textContent = text;
        label.className = 'menu-option-label';

        iconWrapper.appendChild(icon);
        option.appendChild(iconWrapper);
        option.appendChild(label);

        option.addEventListener('click', (e) => {
            e.stopPropagation();
            action();
        });

        return option;
    }


    /**
     * Get the first filter option value from the options string
     */
    getFirstFilterOptionValue(optionsString) {
        if (!optionsString) return '';

        // Parse the HTML options string to get the first option value
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = optionsString;
        const firstOption = tempDiv.querySelector('option');

        return firstOption ? firstOption.textContent : '';
    }

    /**
     * Column type mapping system - defines the type for each field
     */
    getColumnType(field) {
        const columnTypes = {
            // text: Pure text columns
            'brand': 'text',
            'productTitle': 'text',
            'product_title': 'text',
            'title': 'text',

            // numeric: Pure numeric columns
            'price': 'numeric',
            'sales': 'numeric',
            'returns': 'numeric',
            'royalties': 'numeric',
            'bsr': 'numeric',
            'reviews': 'numeric',

            // percentage: Formatted percentage columns
            'returnRate': 'percentage',

            // text-numeric: Mixed content (can be filtered as text or numbers)
            'asin': 'text-numeric',
            'designId': 'text-numeric',
            'design_id': 'text-numeric',

            // text-special: Checkbox-only columns
            'marketplace': 'text-special',
            'productType': 'text-special',
            'product_type': 'text-special',
            'producttype': 'text-special',
            'status': 'text-special',

            // date: Date columns
            'firstSold': 'date',
            'lastSold': 'date',
            'firstPublished': 'date',
            'lastUpdated': 'date',
            'firstsold': 'date',
            'lastsold': 'date',
            'firstpublished': 'date',
            'lastupdated': 'date',
            'first_sold': 'date',
            'last_sold': 'date',
            'first_published': 'date',
            'last_updated': 'date',

            // fixed: Special UI columns (no filtering, no sorting, no resizing)
            'actions': 'fixed',
            'checkbox': 'fixed',
            'preview': 'fixed'
        };

        const fieldLower = field.toLowerCase();
        return columnTypes[fieldLower] || columnTypes[field] || 'text'; // Default to text
    }

    /**
     * Get filter type options based on column type
     */
    getFilterTypeOptions(column) {
        const columnType = this.getColumnType(column.field);

        // Define filter options for each column type
        const filterOptionsByType = {
            text: [
                { value: 'contains', label: 'Contains' },
                { value: 'notContains', label: 'Not Contains' },
                { value: 'startsWith', label: 'Starts with' },
                { value: 'endsWith', label: 'Ends with' }
            ],

            numeric: [
                { value: 'equals', label: 'Equals' },
                { value: 'notEquals', label: 'Not equal' },
                { value: 'lessThan', label: 'Less than' },
                { value: 'lessThanOrEqual', label: 'Less than or equals' },
                { value: 'greaterThan', label: 'Greater than' },
                { value: 'greaterThanOrEqual', label: 'Greater than or equals' },
                { value: 'inRange', label: 'In range' }
            ],

            percentage: [
                { value: 'equals', label: 'Equals' },
                { value: 'notEquals', label: 'Not equal' },
                { value: 'lessThan', label: 'Less than' },
                { value: 'lessThanOrEqual', label: 'Less than or equals' },
                { value: 'greaterThan', label: 'Greater than' },
                { value: 'greaterThanOrEqual', label: 'Greater than or equals' },
                { value: 'inRange', label: 'In range' }
            ],

            'text-numeric': [
                { value: 'contains', label: 'Contains' },
                { value: 'notContains', label: 'Not Contains' },
                { value: 'startsWith', label: 'Starts with' },
                { value: 'endsWith', label: 'Ends with' }
            ],

            date: [
                { value: 'pleaseSelect', label: 'Please Select' },
                { value: 'today', label: 'Today' },
                { value: 'yesterday', label: 'Yesterday' },
                { value: 'last7Days', label: 'Last 7 Days' },
                { value: 'last30Days', label: 'Last 30 Days' },
                { value: 'last90Days', label: 'Last 90 Days' },
                { value: 'last6Months', label: 'Last 6 Months' },
                { value: 'currentMonth', label: 'Current Month' },
                { value: 'lastMonth', label: 'Last Month' },
                { value: 'currentYear', label: 'Current Year' },
                { value: 'lastYear', label: 'Last Year' },
                { value: 'equals', label: 'Equals' },
                { value: 'notEquals', label: 'Not equal' },
                { value: 'lessThan', label: 'Less than' },
                { value: 'greaterThan', label: 'Greater than' },
                { value: 'inRange', label: 'In range' }
            ],

            'text-special': [], // No dropdown - checkbox only

            fixed: [] // No filtering at all
        };

        const options = filterOptionsByType[columnType] || filterOptionsByType.text;

        // Return in the format expected by createCustomDropdown
        return options.map(opt => ({
            value: opt.value,
            text: opt.label
        }));
    }

    /**
     * Get filter type based on column type (for internal filtering logic)
     */
    getFilterType(column) {
        const columnType = this.getColumnType(column.field);

        // Map our column types to internal filter types
        switch (columnType) {
            case 'numeric':
            case 'percentage':
                return 'number';
            case 'date':
                return 'date';
            case 'text':
            case 'text-numeric':
            case 'text-special':
                return 'text';
            case 'fixed':
                return 'text'; // Fallback, though fixed columns shouldn't be filtered
            default:
                return 'text';
        }
    }

    /**
     * Validate the new column type system - for testing and debugging
     */
    validateColumnTypeSystem() {
        console.log('🧪 Testing Column Type System...');

        const testFields = [
            // text
            'brand', 'productTitle', 'title',
            // numeric
            'price', 'sales', 'returns', 'royalties', 'bsr', 'reviews',
            // percentage
            'returnRate',
            // text-numeric
            'asin', 'designId', 'design_id',
            // text-special
            'marketplace', 'productType', 'status',
            // date
            'firstSold', 'lastSold', 'firstPublished', 'lastUpdated',
            // fixed
            'actions', 'checkbox', 'preview'
        ];

        const results = {};
        testFields.forEach(field => {
            const columnType = this.getColumnType(field);
            const filterOptions = this.getFilterTypeOptions({ field });
            const isFixed = this.isFixedColumn(field);

            results[field] = {
                type: columnType,
                optionCount: filterOptions.length,
                isFixed,
                hasOptions: filterOptions.length > 0
            };
        });

        console.table(results);

        // Validate expected behaviors
        const validations = [
            { field: 'brand', expectedType: 'text', shouldHaveOptions: true },
            { field: 'price', expectedType: 'numeric', shouldHaveOptions: true },
            { field: 'asin', expectedType: 'text-numeric', shouldHaveOptions: true },
            { field: 'marketplace', expectedType: 'text-special', shouldHaveOptions: false },
            { field: 'firstSold', expectedType: 'date', shouldHaveOptions: true },
            { field: 'actions', expectedType: 'fixed', shouldHaveOptions: false, shouldBeFixed: true }
        ];

        let allPassed = true;
        validations.forEach(test => {
            const actual = results[test.field];
            const typeMatch = actual?.type === test.expectedType;
            const optionsMatch = actual?.hasOptions === test.shouldHaveOptions;
            const fixedMatch = test.shouldBeFixed ? actual?.isFixed === true : true;

            const passed = typeMatch && optionsMatch && fixedMatch;
            if (!passed) {
                console.error(`❌ Test failed for ${test.field}:`, {
                    expected: test,
                    actual
                });
                allPassed = false;
            }
        });

        if (allPassed) {
            console.log('✅ All column type system tests passed!');
        } else {
            console.error('❌ Some column type system tests failed!');
        }

        return { results, allPassed };
    }

    /**
     * Debug date filtering - call this in console to test
     */
    debugDateFiltering(field = 'firstSold') {
        console.log('🔍 DEBUG: Date Filtering Analysis');
        console.log('================================');

        // Show current filter config
        console.log('1. Current filter config:', this.state.filterConfig);

        // Show sample data
        const sampleData = this.config.data.slice(0, 5);
        console.log('2. Sample data for field "' + field + '":');
        sampleData.forEach((row, i) => {
            const value = row[field];
            console.log(`   Row ${i}:`, {
                value,
                type: typeof value,
                parsed: new Date(value),
                isValid: !isNaN(new Date(value).getTime())
            });
        });

        // Test today's date
        const today = new Date();
        const todayString = today.toDateString();
        console.log('3. Today is:', todayString);

        // Test filtering manually
        if (this.state.filterConfig[field]) {
            const filter = this.state.filterConfig[field];
            console.log('4. Testing filter manually:');
            sampleData.forEach((row, i) => {
                const value = row[field];
                const result = this.applyDateFilter(value, filter.operator, filter.value);
                console.log(`   Row ${i} (${value}) → ${result ? 'PASS' : 'FAIL'}`);
            });
        } else {
            console.log('4. No filter active for field:', field);
        }

        console.log('================================');
    }

    /**
     * Get Pacific Time using Snap's timezone utility
     * Falls back to local time if utility not available
     */
    getPacificTime() {
        if (window.SnapTimezone && typeof window.SnapTimezone.getPacificTime === 'function') {
            return window.SnapTimezone.getPacificTime();
        }
        // Fallback to local time
        return new Date();
    }

    /**
     * Get Pacific Date (midnight) using Snap's timezone utility
     * Falls back to local date if utility not available
     */
    getPacificDate() {
        if (window.SnapTimezone && typeof window.SnapTimezone.getPacificDate === 'function') {
            return window.SnapTimezone.getPacificDate();
        }
        // Fallback to local date at midnight
        const localDate = new Date();
        localDate.setHours(0, 0, 0, 0);
        return localDate;
    }

    /**
     * Fallback method to check if a date is today (local time)
     */
    isLocalToday(date, today) {
        const itemDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        return itemDate.getTime() === today.getTime();
    }



    /**
     * Create menu option
     */
    createMenuOption(text, onClick) {
        const option = document.createElement('div');
        option.className = 'snap-grid-menu-option';
        option.setAttribute('role', 'menuitem');
        option.textContent = text;
        option.addEventListener('click', (e) => {
            e.stopPropagation();
            onClick(e);
        });
        return option;
    }

    /**
     * Create menu separator
     */
    createMenuSeparator() {
        const separator = document.createElement('div');
        separator.className = 'snap-grid-menu-separator';
        return separator;
    }

    /**
     * Create menu submenu
     */
    createMenuSubmenu(text, items) {
        const submenu = document.createElement('div');
        submenu.className = 'snap-grid-menu-submenu';

        const trigger = document.createElement('div');
        trigger.className = 'snap-grid-menu-option submenu-trigger';
        trigger.innerHTML = `${text} <span class="submenu-arrow">▶</span>`;

        const submenuContent = document.createElement('div');
        submenuContent.className = 'snap-grid-submenu-content';

        items.forEach(item => {
            const option = document.createElement('div');
            option.className = 'snap-grid-menu-option';
            if (item.checked) {
                option.classList.add('checked');
            }
            option.textContent = item.label;
            option.addEventListener('click', (e) => {
                e.stopPropagation();
                item.action(e);
            });
            submenuContent.appendChild(option);
        });

        submenu.appendChild(trigger);
        submenu.appendChild(submenuContent);

        // Show/hide submenu on hover
        let hoverTimeout;
        trigger.addEventListener('mouseenter', () => {
            clearTimeout(hoverTimeout);
            submenuContent.style.display = 'block';
        });

        submenu.addEventListener('mouseleave', () => {
            hoverTimeout = setTimeout(() => {
                submenuContent.style.display = 'none';
            }, 200);
        });

        return submenu;
    }

    /**
     * Check if column is pinned
     */
    isColumnPinned(field, side = null) {
        if (side) {
            return this.state.pinnedColumns[side].includes(field);
        }
        return this.state.pinnedColumns.left.includes(field) ||
               this.state.pinnedColumns.right.includes(field);
    }

    /**
     * Get column pin state
     */
    getColumnPinState(field) {
        if (this.state.pinnedColumns.left.includes(field)) {
            return 'left';
        } else if (this.state.pinnedColumns.right.includes(field)) {
            return 'right';
        } else {
            return 'none';
        }
    }

    /**
     * Sort column
     */
    sortColumn(field, direction) {
        // Update the sortConfig array instead of the old sortBy/sortDirection
        this.state.sortConfig = [{ field, direction }];
        this.processData(); // Apply the sorting to the data
        this.render();
        this.emit('columnSorted', { field, direction });
    }

    /**
     * Toggle row selection
     */
    toggleRowSelection(rowIndex) {
        const rowData = this.state.displayData[rowIndex];
        if (!rowData) return;

        const rowKey = this.getRowId(rowData);

        if (this.state.selectedRowKeys.has(rowKey)) {
            this.state.selectedRowKeys.delete(rowKey);
        } else {
            this.state.selectedRowKeys.add(rowKey);
        }

        this.render();
        this.emit('selectionChanged', {
            selectedKeys: Array.from(this.state.selectedRowKeys),
            selectedRows: this.getSelectedRows()
        });
    }

    /**
     * Get selected row data
     */
    getSelectedRows() {
        return this.state.displayData.filter((rowData) => {
            const rowKey = this.getRowId(rowData);
            return this.state.selectedRowKeys.has(rowKey);
        });
    }

    /**
     * Pin column to left or right
     */
    pinColumn(field, side) {
        // Remove from other side first
        this.unpinColumn(field);

        if (!this.state.pinnedColumns[side].includes(field)) {
            this.state.pinnedColumns[side].push(field);
            this.render();
            this.emit('columnPinned', { field, side });
        }
    }

    /**
     * Unpin column
     */
    unpinColumn(field) {
        this.state.pinnedColumns.left = this.state.pinnedColumns.left.filter(f => f !== field);
        this.state.pinnedColumns.right = this.state.pinnedColumns.right.filter(f => f !== field);
        this.render();
        this.emit('columnUnpinned', { field });
    }

    /**
     * Update pin submenu check icons based on current pin state
     */
    updatePinSubmenu(field, pinSubmenu) {
        if (!pinSubmenu) return;

        const currentPinState = this.getColumnPinState(field);

        // Find all check icons in the pin submenu
        const pinLeftCheck = pinSubmenu.querySelector('.pin-option:nth-child(1) .check-icon');
        const pinRightCheck = pinSubmenu.querySelector('.pin-option:nth-child(2) .check-icon');
        const dontPinCheck = pinSubmenu.querySelector('.pin-option:nth-child(3) .check-icon');

        // Update visibility based on current pin state
        if (pinLeftCheck) {
            pinLeftCheck.className = `check-icon ${currentPinState === 'left' ? '' : 'hidden'}`;
        }
        if (pinRightCheck) {
            pinRightCheck.className = `check-icon ${currentPinState === 'right' ? '' : 'hidden'}`;
        }
        if (dontPinCheck) {
            dontPinCheck.className = `check-icon ${currentPinState === 'none' ? '' : 'hidden'}`;
        }
    }

    /**
     * Autosize column to fit content
     */
    autosizeColumn(field) {
        const column = this.config.columns.find(col => col.field === field);
        if (!column) return;

        // Skip autosize for fixed-width columns (Checkbox, Preview, Actions)
        const excludedColumns = ['checkbox', 'preview', 'actions'];
        if (excludedColumns.includes(field)) {
            return;
        }

        // Get computed font styles from actual DOM elements
        const fonts = this.getComputedFonts();

        // Measure header width
        const headerText = column.headerName || column.field;
        let maxWidth = this.measureTextWidth(headerText, fonts.header, column.field);

        // Sample visible rows for content measurement (more efficient)
        const visibleData = this.config.virtualScrolling ?
            this.state.displayData.slice(this.state.visibleRange.start, this.state.visibleRange.end) :
            this.state.displayData.slice(0, 100); // Limit to first 100 for performance

        // Measure content widths
        visibleData.forEach(rowData => {
            if (rowData && !rowData.__isGroupHeader) {
                const value = this.getCellValue(rowData, column);
                let displayValue = String(value ?? '');

                // Apply formatting for accurate measurement
                if (column.type === 'currency') {
                    displayValue = this.formatCurrency(value, column.currencyFormat);
                } else if (column.type === 'number') {
                    displayValue = this.formatNumber(value, column.numberFormat);
                } else if (column.type === 'percentage') {
                    displayValue = this.formatPercentage(value, column.percentageFormat);
                } else if (column.type === 'date') {
                    displayValue = this.formatDate(value, column.dateFormat);
                } else if (column.type === 'boolean') {
                    displayValue = value ? '✓' : '✗';
                }

                const contentWidth = this.measureTextWidth(displayValue, fonts.cell, column.field);
                maxWidth = Math.max(maxWidth, contentWidth);
            }
        });

        // Add padding for cell content (left + right padding + borders)
        const padding = 24; // 12px each side
        const scrollbarBuffer = 20; // Buffer for potential scrollbars

        // Apply constraints: min 60px, max 600px
        const optimalWidth = Math.max(60, Math.min(600, maxWidth + padding + scrollbarBuffer));

        // Update column width and trigger re-render
        this.setColumnWidth(field, optimalWidth);

        // If column is pinned, recalculate pin offsets
        if (this.isColumnPinned(field)) {
            this.render();
        }
    }

    /**
     * Autosize all columns
     */
    autosizeAllColumns() {
        // Columns that should not be autosized
        const excludedColumns = ['checkbox', 'preview', 'actions'];

        this.config.columns.forEach(column => {
            if (!this.state.hiddenColumns.has(column.field) && !excludedColumns.includes(column.field)) {
                this.autosizeColumn(column.field);
            }
        });
    }

    /**
     * Get computed font styles from DOM elements
     */
    getComputedFonts() {
        // Cache fonts to avoid recomputation
        if (this._cachedFonts) {
            return this._cachedFonts;
        }

        // Create temporary elements to get computed styles
        const headerElement = document.createElement('div');
        headerElement.className = 'snap-grid-header-cell';
        headerElement.style.visibility = 'hidden';
        headerElement.style.position = 'absolute';
        headerElement.style.top = '-9999px';

        const cellElement = document.createElement('div');
        cellElement.className = 'snap-grid-cell';
        cellElement.style.visibility = 'hidden';
        cellElement.style.position = 'absolute';
        cellElement.style.top = '-9999px';

        // Append to container to get computed styles
        this.container.appendChild(headerElement);
        this.container.appendChild(cellElement);

        // Get computed styles
        const headerStyle = window.getComputedStyle(headerElement);
        const cellStyle = window.getComputedStyle(cellElement);

        this._cachedFonts = {
            header: `${headerStyle.fontWeight} ${headerStyle.fontSize} ${headerStyle.fontFamily}`,
            cell: `${cellStyle.fontWeight} ${cellStyle.fontSize} ${cellStyle.fontFamily}`
        };

        // Clean up
        this.container.removeChild(headerElement);
        this.container.removeChild(cellElement);

        return this._cachedFonts;
    }

    /**
     * Invalidate measurement cache when fonts or sizes change
     */
    invalidateMeasurementCache() {
        this.measurementCache.clear();
        this._cachedFonts = null;
    }

    /**
     * Get column width by index
     */
    getColumnWidth(colIndex) {
        const column = this.config.columns[colIndex];
        if (!column) return 100; // Default width

        // Check if we have a stored width
        if (this.state.columnWidths.has(column.field)) {
            return this.state.columnWidths.get(column.field);
        }

        // Return configured width or default
        return column.width || 100;
    }

    /**
     * Measure text width with per-column measurement cache
     */
    measureTextWidth(text, font, columnField = null) {
        // Create cache key for this measurement
        const cacheKey = columnField ? `${columnField}:${text}:${font}` : `${text}:${font}`;

        // Check cache first
        if (this.measurementCache.has(cacheKey)) {
            return this.measurementCache.get(cacheKey);
        }

        // Create canvas context if not exists
        if (!this._measureCtx) {
            const canvas = document.createElement('canvas');
            this._measureCtx = canvas.getContext('2d');
        }

        this._measureCtx.font = font;
        const width = Math.min(600, this._measureCtx.measureText(text).width); // Cap at 600px

        // Cache the result
        this.measurementCache.set(cacheKey, width);

        // Limit cache size to prevent memory issues
        if (this.measurementCache.size > 1000) {
            const firstKey = this.measurementCache.keys().next().value;
            this.measurementCache.delete(firstKey);
        }

        return width;
    }

    /**
     * Format currency value
     */
    formatCurrency(value, format = {}) {
        const { currency = 'USD', locale = 'en-US' } = format;
        if (value == null || isNaN(value)) return '';

        try {
            return new Intl.NumberFormat(locale, {
                style: 'currency',
                currency: currency
            }).format(Number(value));
        } catch (e) {
            return `$${Number(value).toFixed(2)}`;
        }
    }

    /**
     * Format number value
     */
    formatNumber(value, format = {}) {
        const { decimals = 0, locale = 'en-US' } = format;
        if (value == null || isNaN(value)) return '';

        try {
            return new Intl.NumberFormat(locale, {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            }).format(Number(value));
        } catch (e) {
            return Number(value).toFixed(decimals);
        }
    }

    /**
     * Format date value
     */
    formatDate(value, format = {}) {
        const { locale = 'en-US', options = {} } = format;
        if (!value) return '';

        try {
            const date = new Date(value);
            if (isNaN(date.getTime())) return String(value);

            return new Intl.DateTimeFormat(locale, {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                ...options
            }).format(date);
        } catch (e) {
            return String(value);
        }
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHTML(str) {
        if (typeof str !== 'string') return str;

        const div = document.createElement('div');
        div.textContent = str;
        return div.innerHTML;
    }


    /**
     * Format a raw value for display in checkbox filter list based on the column's type
     * Only affects label text; does not alter underlying raw values used for filtering
     */
    formatValueForField(fieldName, rawValue) {
        const rawStr = String(rawValue ?? '');
        if (!fieldName) return rawStr;
        const col = this.config.columns.find(c => c.field === fieldName);
        if (!col) return rawStr;
        switch (col.type) {
            case 'currency':
                return this.formatCurrency(rawStr, col.currencyFormat);
            case 'number':
                return this.formatNumber(rawStr, col.numberFormat);
            case 'percentage':
                return this.formatPercentage(rawStr, col.percentageFormat);
            case 'date':
                return this.formatDate(rawStr, col.dateFormat);
            default:
                return rawStr;
        }
    }

    /**
     * Simple HTML sanitizer - allows basic formatting tags
     */
    sanitizeHTML(str) {
        if (typeof str !== 'string') return str;

        // Allow only basic formatting tags
        const allowedTags = ['b', 'i', 'em', 'strong', 'span', 'br'];
        const allowedAttributes = ['class', 'style'];

        // Create a temporary element to parse HTML

        // Create a temporary element to parse HTML
        const temp = document.createElement('div');
        temp.innerHTML = str;

        // Recursively clean elements
        const cleanElement = (element) => {
            const tagName = element.tagName?.toLowerCase();

            // Remove disallowed tags
            if (tagName && !allowedTags.includes(tagName)) {
                // Replace with text content
                const textNode = document.createTextNode(element.textContent || '');
                element.parentNode?.replaceChild(textNode, element);
                return;
            }

            // Clean attributes
            if (element.attributes) {
                const attrs = Array.from(element.attributes);
                attrs.forEach(attr => {
                    if (!allowedAttributes.includes(attr.name.toLowerCase())) {
                        element.removeAttribute(attr.name);
                    }
                });
            }

            // Recursively clean children
            Array.from(element.children).forEach(cleanElement);
        };

        Array.from(temp.children).forEach(cleanElement);
        return temp.innerHTML;
    }

    /**
     * Show column chooser dialog
     */
    showColumnChooser() {
        // Remove existing dialog
        this.hideColumnChooser();

        const dialog = document.createElement('div');
        dialog.className = 'snap-grid-column-chooser';
        dialog.setAttribute('role', 'dialog');
        dialog.setAttribute('aria-label', 'Choose Columns');

        dialog.innerHTML = `
            <div class="column-chooser-header">
                <h3>Choose Columns</h3>
                <button class="column-chooser-close" aria-label="Close column chooser">×</button>
            </div>
            <div class="column-chooser-body">
                <div class="column-list">
                    ${this.config.columns
                        .filter(column => column.field !== 'marketplace') // Exclude marketplace column
                        .map(column => `
                        <label class="column-item">
                            <input type="checkbox"
                                   value="${column.field}"
                                   ${!this.state.hiddenColumns.has(column.field) ? 'checked' : ''}>
                            <span>${column.headerName || column.field}</span>


                        </label>
                    `).join('')}
                </div>
                <div class="column-chooser-actions">
                    <button class="btn btn-secondary" id="select-all">Select All</button>
                    <button class="btn btn-secondary" id="select-none">Select None</button>
                    <button class="btn btn-primary" id="apply-columns">Apply</button>
                </div>
            </div>
        `;

        // Position dialog
        dialog.style.position = 'fixed';
        dialog.style.top = '50%';
        dialog.style.left = '50%';
        dialog.style.transform = 'translate(-50%, -50%)';
        dialog.style.zIndex = '1001';

        // Position dialog in center of viewport
        this.positionDialog(dialog);

        document.body.appendChild(dialog);
        this.currentColumnChooser = dialog;
        this.activeDialogs.add(dialog);

        // Event listeners
        this.setupColumnChooserEvents(dialog);
    }

    /**
     * Setup column chooser events
     */
    setupColumnChooserEvents(dialog) {
        const closeBtn = dialog.querySelector('.column-chooser-close');
        const selectAllBtn = dialog.querySelector('#select-all');
        const selectNoneBtn = dialog.querySelector('#select-none');
        const applyBtn = dialog.querySelector('#apply-columns');
        const checkboxes = dialog.querySelectorAll('input[type="checkbox"]');

        closeBtn.addEventListener('click', () => this.hideColumnChooser());

        selectAllBtn.addEventListener('click', () => {
            checkboxes.forEach(cb => cb.checked = true);
        });

        selectNoneBtn.addEventListener('click', () => {
            checkboxes.forEach(cb => cb.checked = false);
        });

        applyBtn.addEventListener('click', () => {
            // Update column visibility (excluding marketplace column)
            this.config.columns
                .filter(column => column.field !== 'marketplace')
                .forEach(column => {
                    const checkbox = dialog.querySelector(`input[value="${column.field}"]`);
                    if (checkbox.checked) {
                        this.showColumn(column.field);
                    } else {
                        this.hideColumn(column.field);
                    }
                });

            this.hideColumnChooser();
        });

        // Close on outside click
        setTimeout(() => {
            document.addEventListener('click', (e) => {
                if (!dialog.contains(e.target)) {
                    this.hideColumnChooser();
                }
            }, { once: true });
        }, 100);
    }

    /**
     * Hide column chooser
     */
    hideColumnChooser() {
        if (this.currentColumnChooser) {
            this.activeDialogs.delete(this.currentColumnChooser);
            this.currentColumnChooser.remove();
            this.currentColumnChooser = null;
        }
    }

    /**
     * Reset columns to default state
     */
    resetColumns() {
        // Columns that should not be affected by reset
        const excludedColumns = ['checkbox', 'preview', 'actions'];

        // Reset column widths to their minimum width (excluding special columns)
        this.config.columns.forEach(column => {
            if (!excludedColumns.includes(column.field)) {
                const minWidth = this.calculateMinHeaderWidth(column);
                this.state.columnWidths.set(column.field, minWidth);
            }
        });

        // Reset column visibility (excluding special columns)
        const hiddenColumnsToKeep = new Set();
        this.state.hiddenColumns.forEach(field => {
            if (excludedColumns.includes(field)) {
                hiddenColumnsToKeep.add(field);
            }
        });
        this.state.hiddenColumns = hiddenColumnsToKeep;

        // Reset column pinning to empty state first
        this.state.pinnedColumns = { left: [], right: [] };

        // Reset column order (excluding special columns - they maintain their positions)
        const regularColumns = this.config.columns
            .filter(col => !excludedColumns.includes(col.field))
            .map(col => col.field);

        // Rebuild order with special columns in their default positions
        this.state.columnOrder = [];

        // Add left-pinned special columns first
        if (this.config.columns.find(col => col.field === 'checkbox')) {
            this.state.columnOrder.push('checkbox');
        }
        if (this.config.columns.find(col => col.field === 'preview')) {
            this.state.columnOrder.push('preview');
        }

        // Add regular columns
        this.state.columnOrder.push(...regularColumns);

        // Add right-pinned special columns last
        if (this.config.columns.find(col => col.field === 'actions')) {
            this.state.columnOrder.push('actions');
        }

        // Re-establish default pinning (checkbox, preview to left; actions to right)
        this.setupDefaultPinning();

        this.render();
        this.emit('columnsReset');
    }

    /**
     * Apply pinning styles to cell or header
     */
    applyPinningStyles(element, field) {
        const isLeftPinned = this.state.pinnedColumns.left.includes(field);
        const isRightPinned = this.state.pinnedColumns.right.includes(field);

        // Remove all pinning classes first
        element.classList.remove('pinned-left', 'pinned-right');

        if (isLeftPinned) {
            element.classList.add('pinned-left');
            element.style.position = 'sticky';
            element.style.left = this.calculateLeftPinOffset(field) + 'px';
            element.style.zIndex = '3';
            element.style.backgroundColor = 'var(--grid-header-bg)';
        } else if (isRightPinned) {
            element.classList.add('pinned-right');
            element.style.position = 'sticky';
            element.style.right = this.calculateRightPinOffset(field) + 'px';
            element.style.zIndex = '3';
            element.style.backgroundColor = 'var(--grid-header-bg)';
        } else {
            // Remove pinning styles
            element.style.position = '';
            element.style.left = '';
            element.style.right = '';
            element.style.zIndex = '';
            element.style.backgroundColor = '';
        }
    }

    /**
     * Calculate left pin offset for a field
     */
    calculateLeftPinOffset(field) {
        let offset = 0;
        const leftPinnedIndex = this.state.pinnedColumns.left.indexOf(field);

        for (let i = 0; i < leftPinnedIndex; i++) {
            const pinnedField = this.state.pinnedColumns.left[i];
            offset += this.state.columnWidths.get(pinnedField) || 150;
        }

        return offset;
    }

    /**
     * Calculate right pin offset for a field
     */
    calculateRightPinOffset(field) {
        let offset = 0;
        const rightPinnedIndex = this.state.pinnedColumns.right.indexOf(field);

        for (let i = rightPinnedIndex + 1; i < this.state.pinnedColumns.right.length; i++) {
            const pinnedField = this.state.pinnedColumns.right[i];
            offset += this.state.columnWidths.get(pinnedField) || 150;
        }

        return offset;
    }

    /**
     * Calculate total width of pinned columns on specified side
     */
    calculateTotalPinnedWidth(side) {
        const pinnedColumns = this.state.pinnedColumns[side] || [];
        let totalWidth = 0;

        pinnedColumns.forEach(field => {
            const width = this.state.columnWidths.get(field) || 0;
            totalWidth += width;
        });

        return totalWidth;
    }

    /**
     * Position dialog in viewport center with edge detection
     */
    positionDialog(dialog) {
        dialog.style.position = 'fixed';
        dialog.style.top = '50%';
        dialog.style.left = '50%';
        dialog.style.transform = 'translate(-50%, -50%)';
        dialog.style.zIndex = '1001';
        dialog.style.maxHeight = '90vh';
        dialog.style.maxWidth = '90vw';
    }

    /**
     * Position menu with collision detection during scrolling
     */
    positionMenuWithCollisionDetection(menu, targetElement) {
        // Ensure menu has proper positioning styles
        menu.style.position = 'absolute';
        menu.style.zIndex = '1001';

        const rect = targetElement.getBoundingClientRect();
        const containerRect = this.container.getBoundingClientRect();
        const menuRect = menu.getBoundingClientRect();
        const menuWidth = menuRect.width || 281; // Fallback to CSS width if not rendered yet
        const menuHeight = menuRect.height || 400; // Fallback to CSS height if not rendered yet
        const padding = 8;

        // Get pinned column boundaries in viewport coordinates
        const pinnedLeftWidth = this.calculateTotalPinnedWidth('left');
        const pinnedRightWidth = this.calculateTotalPinnedWidth('right');
        const containerWidth = this.container.offsetWidth;

        // Calculate pinned boundaries in viewport coordinates
        const leftPinnedEnd = containerRect.left + pinnedLeftWidth;
        const rightPinnedStart = containerRect.right - pinnedRightWidth;

        // Calculate scroll-adjusted positions
        const scrollLeft = this.container.scrollLeft || 0;
        const scrollTop = this.container.scrollTop || 0;

        const targetRelativeToContainer = {
            left: rect.left - containerRect.left + scrollLeft,
            right: rect.right - containerRect.left + scrollLeft,
            top: rect.top - containerRect.top + scrollTop,
            bottom: rect.bottom - containerRect.top + scrollTop,
            width: rect.width
        };

        // Determine original menu position preference
        const containerCenter = containerWidth / 2;
        const columnCenter = targetRelativeToContainer.left + (targetRelativeToContainer.width / 2);
        const isLeftSide = columnCenter < containerCenter;

        let left;

        if (isLeftSide) {
            // Prefer right side of column
            left = targetRelativeToContainer.right;

            // Check collision with right pinned columns using viewport coordinates
            const menuRightEdge = containerRect.left + left + menuWidth - scrollLeft;
            if (menuRightEdge > rightPinnedStart && pinnedRightWidth > 0) {
                // Stop at right pinned boundary (convert back to container coordinates)
                left = rightPinnedStart - containerRect.left + scrollLeft - menuWidth;
            }
        } else {
            // Prefer left side of column (account for 1.5px border)
            left = targetRelativeToContainer.left - menuWidth + 1.5;

            // Check collision with left pinned columns using viewport coordinates
            const menuLeftEdge = containerRect.left + left - scrollLeft;
            if (menuLeftEdge < leftPinnedEnd && pinnedLeftWidth > 0) {
                // Stop at left pinned boundary (convert back to container coordinates)
                left = leftPinnedEnd - containerRect.left + scrollLeft;
            }
        }

        // Ensure menu stays within container bounds
        if (left < 0) left = 0;
        if (left + menuWidth > containerWidth) left = containerWidth - menuWidth;

        // Vertical positioning
        let top = targetRelativeToContainer.bottom + 2;

        if (top + menuHeight > this.container.offsetHeight - padding) {
            top = targetRelativeToContainer.top - menuHeight - 2;
            if (top < padding) {
                top = padding;
            }
        }

        // Apply positioning
        menu.style.left = `${left}px`;
        menu.style.top = `${top}px`;
    }

    /**
     * Position menu relative to target element with smart left/right positioning
     */
    positionMenu(menu, targetElement) {
        const rect = targetElement.getBoundingClientRect();
        const containerRect = this.container.getBoundingClientRect();
        const padding = 8; // Container padding

        // Position menu absolutely within the grid container
        menu.style.position = 'absolute';
        menu.style.zIndex = '1001';

        // Force menu to be visible to get accurate dimensions
        menu.style.visibility = 'hidden';
        menu.style.display = 'block';

        // Get actual menu dimensions
        const menuRect = menu.getBoundingClientRect();
        const menuWidth = menuRect.width || 300; // Fallback to 300 if not rendered yet
        const menuHeight = menuRect.height || 400; // Fallback to 400 if not rendered yet

        // Calculate positions relative to container's scroll position
        const scrollLeft = this.container.scrollLeft || 0;
        const scrollTop = this.container.scrollTop || 0;

        const targetRelativeToContainer = {
            left: Math.round(rect.left - containerRect.left + scrollLeft),
            right: Math.round(rect.right - containerRect.left + scrollLeft),
            top: Math.round(rect.top - containerRect.top + scrollTop),
            bottom: Math.round(rect.bottom - containerRect.top + scrollTop),
            width: rect.width
        };

        // Determine if column is on left or right side of container
        const containerCenter = this.container.offsetWidth / 2;
        const columnCenter = targetRelativeToContainer.left + (targetRelativeToContainer.width / 2);
        const isLeftSide = columnCenter < containerCenter;

        // Smart horizontal positioning with collision detection for pinned columns
        let left;

        // Get pinned column boundaries
        const pinnedLeftWidth = this.calculateTotalPinnedWidth('left');
        const pinnedRightWidth = this.calculateTotalPinnedWidth('right');
        const containerWidth = this.container.offsetWidth;

        if (isLeftSide) {
            // Column is on left side → open menu to the right
            left = targetRelativeToContainer.right; // No gap - exactly at column edge

            // Check collision with right pinned columns
            const rightPinnedStart = containerWidth - pinnedRightWidth;
            if (left + menuWidth > rightPinnedStart && pinnedRightWidth > 0) {
                // Menu would collide with right pinned columns, position it to the left instead
                left = rightPinnedStart - menuWidth;
            }

            // Ensure menu doesn't go off right edge of container
            if (left + menuWidth > containerWidth - padding) {
                left = containerWidth - menuWidth - padding;
            }
        } else {
            // Column is on right side → open menu to the left
            // Position menu's right edge exactly at column's left edge (account for 1.5px border)
            left = targetRelativeToContainer.left - menuWidth + 1.5; // +1.5 to account for border

            // Check collision with left pinned columns
            if (left < pinnedLeftWidth && pinnedLeftWidth > 0) {
                // Menu would collide with left pinned columns, position it to the right instead
                left = pinnedLeftWidth;
            }

            // Ensure menu doesn't go off left edge of container
            if (left < padding) {
                left = padding;
            }
        }

        // Vertical positioning relative to container
        let top = targetRelativeToContainer.bottom + 2; // Small gap below the column header

        // Ensure menu doesn't go off bottom edge of container
        if (top + menuHeight > this.container.offsetHeight - padding) {
            top = targetRelativeToContainer.top - menuHeight - 2; // Position above if no space below
            if (top < padding) {
                top = padding; // Fallback to top of container
            }
        }

        // Apply positioning with pixel-perfect rounding
        menu.style.left = `${Math.round(left)}px`;
        menu.style.top = `${Math.round(top)}px`;

        // Make menu visible again
        menu.style.visibility = 'visible';

        // Set position attribute for debugging
        const position = isLeftSide ? 'left-column-right-menu' : 'right-column-left-menu';
        menu.setAttribute('data-position', position);
    }

    /**
     * Toggle sort for a field
     */
    toggleSort(field, multiSort = false) {
        const existingSort = this.state.sortConfig.find(s => s.field === field);

        if (!multiSort) {
            // Single column sort
            if (existingSort) {
                if (existingSort.direction === 'asc') {
                    this.state.sortConfig = [{ field, direction: 'desc' }];
                } else {
                    this.state.sortConfig = [];
                }
            } else {
                this.state.sortConfig = [{ field, direction: 'asc' }];
            }
        } else {
            // Multi-column sort
            if (existingSort) {
                if (existingSort.direction === 'asc') {
                    existingSort.direction = 'desc';
                } else {
                    this.state.sortConfig = this.state.sortConfig.filter(s => s.field !== field);
                }
            } else {
                this.state.sortConfig.push({ field, direction: 'asc' });
            }
        }

        this.processData();
        this.render();
        this.emit('sortChanged', { sortConfig: this.state.sortConfig });
    }

    /**
     * Set sort configuration
     */
    setSortConfig(sortConfig) {
        this.state.sortConfig = sortConfig;
        this.processData();
        this.render();
        this.emit('sortChanged', { sortConfig: this.state.sortConfig });
    }

    /**
     * Set filter for a field with debouncing
     */
    setFilterDebounced(field, filter) {
        if (filter) {
            this.state.filterConfig[field] = filter;
            console.log('✅ Filter applied:', { field, filter });
        } else {
            delete this.state.filterConfig[field];
        }

        this.debouncedApplyFilter();
        this.emit('filterChanged', { filterConfig: this.state.filterConfig });
    }

    /**
     * Set filter for a field (immediate - for internal use)
     */
    setFilter(field, filter) {
        console.log('🎯 setFilter called:', { field, filter });

        // Preserve current scroll position before filtering
        const currentScrollLeft = this.elements.viewport ? this.elements.viewport.scrollLeft : 0;
        const currentScrollTop = this.elements.viewport ? this.elements.viewport.scrollTop : 0;

        console.log('📍 Preserving scroll position:', { currentScrollLeft, currentScrollTop });

        if (filter) {
            this.state.filterConfig[field] = filter;
        } else {
            delete this.state.filterConfig[field];
        }

        console.log('🎯 Filter config updated:', this.state.filterConfig);

        // Store scroll position in state for render method to use
        this.state.scrollLeft = currentScrollLeft;
        this.state.scrollTop = currentScrollTop;

        this.processData();
        this.render();
        this.updateFilterIndicators(); // Ensure filter indicators are updated
        this.emit('filterChanged', { filterConfig: this.state.filterConfig });
    }

    /**
     * Clear filter for a field with debouncing
     */
    clearFilterDebounced(field) {
        delete this.state.filterConfig[field];
        this.debouncedApplyFilter();
        this.emit('filterChanged', { filterConfig: this.state.filterConfig });
    }

    /**
     * Clear filter for a field (immediate - for internal use)
     */
    clearFilter(field) {
        // Preserve current scroll position before clearing filter
        const currentScrollLeft = this.elements.viewport ? this.elements.viewport.scrollLeft : 0;
        const currentScrollTop = this.elements.viewport ? this.elements.viewport.scrollTop : 0;

        console.log('📍 Preserving scroll position for clear filter:', { currentScrollLeft, currentScrollTop });

        delete this.state.filterConfig[field];

        // Reset checkbox states for this field (check all checkboxes)
        if (this.checkboxStates.has(field)) {
            const allValues = this.getUniqueColumnValues(field, true);
            this.setAllCheckboxValues(field, allValues, true);
            console.log('🔄 Reset checkbox states for field:', field, 'to all checked');
        }

        // Store scroll position in state for render method to use
        this.state.scrollLeft = currentScrollLeft;
        this.state.scrollTop = currentScrollTop;

        this.processData();
        this.render();
        this.updateFilterIndicators(); // Ensure filter indicators are updated
        
        // Regenerate checkbox UI to reflect the reset state
        setTimeout(() => {
            const headerCell = this.container.querySelector(`.snap-grid-header-cell[data-field="${field}"]`);
            if (headerCell) {
                const menu = headerCell.querySelector('.snap-grid-column-menu');
                if (menu) {
                    const checkboxList = menu.querySelector('.filter-checkbox-list');
                    if (checkboxList) {
                        this.regenerateCheckboxListForField(field, checkboxList);
                        console.log('🔄 Regenerated checkbox UI for field:', field);
                    }
                }
            }
        }, 100);

        this.emit('filterChanged', { filterConfig: this.state.filterConfig });
    }

    /**
     * Update filter indicators for all columns
     */
    updateFilterIndicators() {
        // Use setTimeout to ensure DOM is fully rendered
        setTimeout(() => {
            // Find all header cells and update their filter indicators
            const headerCells = this.container.querySelectorAll('.snap-grid-header-cell');

            console.log('🔍 Updating filter indicators for', headerCells.length, 'header cells');
            console.log('🔍 Current filterConfig:', this.state.filterConfig);

            headerCells.forEach(cell => {
                const field = cell.getAttribute('data-field');
                if (!field) return;

                const menuContainer = cell.querySelector('.snap-grid-column-menu-container');
                if (!menuContainer) {
                    console.log('⚠️ No menu container found for field:', field);
                    return;
                }

                // Remove existing indicator
                const existingIndicator = menuContainer.querySelector('.snap-grid-filter-indicator');
                if (existingIndicator) {
                    existingIndicator.remove();
                }

                // FIXED: Better logic to determine if filter is actually active
                const filterConfig = this.state.filterConfig[field];
                let hasActiveFilter = false;

                if (filterConfig) {
                    const { operator, value, checkedValues, secondOperator, secondValue } = filterConfig;

                    // Check if first filter condition is active
                    // For date filters, predefined ranges (like 'lastYear') have null value but are still active
                    const isDatePredefinedFilter = filterConfig.type === 'date' &&
                        ['today', 'yesterday', 'last7Days', 'last30Days', 'last90Days', 'last6Months',
                         'currentMonth', 'lastMonth', 'currentYear', 'lastYear'].includes(operator);

                    // For date range filters, check if range values exist
                    const isDateRangeFilter = operator === 'inRange' && value && typeof value === 'object' &&
                        (value.fromValue || value.toValue);

                    const hasFirstFilter = operator && operator !== 'pleaseSelect' &&
                        (isDatePredefinedFilter || isDateRangeFilter || (value !== null && value !== undefined && value !== '') || operator === 'blank' || operator === 'notBlank');

                    // Check if second filter condition is active
                    const hasSecondFilter = secondOperator && secondOperator !== 'pleaseSelect' &&
                        ((secondValue !== null && secondValue !== undefined && secondValue !== '') || secondOperator === 'blank' || secondOperator === 'notBlank');

                    // Check if checkbox filter is active (some items unchecked OR all items unchecked)
                    const totalUniqueValues = this.getUniqueColumnValues(field).length;
                    const currentCheckboxState = this.getCheckboxState(field);
                    const hasCheckboxFilter = currentCheckboxState.size > 0 && 
                        currentCheckboxState.size < totalUniqueValues;

                    hasActiveFilter = hasFirstFilter || hasSecondFilter || hasCheckboxFilter;

                    console.log(`🔍 Field ${field} filter analysis:`, {
                        hasFirstFilter,
                        hasSecondFilter,
                        hasCheckboxFilter,
                        hasActiveFilter,
                        operator,
                        value,
                        checkedValuesCount: currentCheckboxState.size,
                        totalUniqueValues: totalUniqueValues
                    });
                }

                // Add new indicator if filter is actually active
                if (hasActiveFilter) {
                    console.log('✅ Adding filter indicator for field:', field);
                    const indicator = document.createElement('div');
                    indicator.className = 'snap-grid-filter-indicator';
                    indicator.style.position = 'absolute';
                    indicator.style.top = '2px';
                    indicator.style.right = '2px';
                    indicator.style.width = '6px';
                    indicator.style.height = '6px';
                    indicator.style.borderRadius = '50%';
                    indicator.style.background = 'var(--color-primary-600, #470CED)';
                    indicator.style.pointerEvents = 'none';
                    indicator.style.zIndex = '1';
                    indicator.setAttribute('title', 'Filter applied');

                    menuContainer.appendChild(indicator);
                } else {
                    console.log('❌ No active filter for field:', field);
                }
            });
            
            // Update clear button state after updating all filter indicators
            this.updateClearButtonState();
        }, 0);
    }

    /**
     * Set group configuration
     */
    setGroupConfig(groupConfig) {
        this.state.groupConfig = groupConfig;

        // Initialize all groups as expanded by default
        if (groupConfig) {
            this.state.expandedGroups.clear();
            // Get unique group values and expand all
            const uniqueGroups = new Set();
            this.state.sortedData.forEach(row => {
                const groupValue = this.getNestedValue(row, groupConfig.field);
                const key = String(groupValue || '');
                uniqueGroups.add(key);
            });
            uniqueGroups.forEach(key => this.state.expandedGroups.add(key));
        } else {
            this.state.expandedGroups.clear();
        }

        this.processData();
        this.render();
        this.emit('groupChanged', { groupConfig: this.state.groupConfig });
    }

    /**
     * Set column width
     */
    setColumnWidth(field, width) {
        // Special handling for Actions column - prevent resizing and maintain fixed width
        if (field === 'actions') {
            const actionsWidth = 28 + 8 + 28 + 32; // 96px - fixed width for 2 icons with more breathing room
            this.state.columnWidths.set(field, actionsWidth);
            this.updateColumnWidthDOM(field, actionsWidth);
            this.emit('columnResized', { field, width: actionsWidth });
            return;
        }

        // Enforce minimum width based on header text
        const column = this.config.columns.find(col => col.field === field);
        const minWidth = column ? this.calculateMinHeaderWidth(column) : 80;
        const finalWidth = Math.max(minWidth, width);

        this.state.columnWidths.set(field, finalWidth);

        // Directly update all cells for this column to ensure synchronization
        this.updateColumnWidthDOM(field, finalWidth);

        this.emit('columnResized', { field, width: finalWidth });
    }

    /**
     * Handle unified row hover - coordinate hover across pinned and normal columns
     */
    handleRowHover(rowIndex, isHovering) {
        if (isHovering) {
            this.hoveredRowIndex = rowIndex;
            this.addRowHoverClass(rowIndex);
        } else {
            this.hoveredRowIndex = null;
            this.removeRowHoverClass(rowIndex);
        }
    }

    /**
     * Add hover class to all parts of a row (pinned and normal)
     */
    addRowHoverClass(rowIndex) {
        // Add hover to normal row
        const normalRow = this.elements.canvas.querySelector(`[data-row-index="${rowIndex}"]`);
        if (normalRow) {
            normalRow.classList.add('row-hovered');
        }

        // Add hover to pinned left row
        const pinnedLeftRow = this.elements.pinnedLeft?.querySelector(`[data-row-index="${rowIndex}"]`);
        if (pinnedLeftRow) {
            pinnedLeftRow.classList.add('row-hovered');
        }

        // Add hover to pinned right row
        const pinnedRightRow = this.elements.pinnedRight?.querySelector(`[data-row-index="${rowIndex}"]`);
        if (pinnedRightRow) {
            pinnedRightRow.classList.add('row-hovered');
        }
    }

    /**
     * Remove hover class from all parts of a row (pinned and normal)
     */
    removeRowHoverClass(rowIndex) {
        // Remove hover from normal row
        const normalRow = this.elements.canvas.querySelector(`[data-row-index="${rowIndex}"]`);
        if (normalRow) {
            normalRow.classList.remove('row-hovered');
        }

        // Remove hover from pinned left row
        const pinnedLeftRow = this.elements.pinnedLeft?.querySelector(`[data-row-index="${rowIndex}"]`);
        if (pinnedLeftRow) {
            pinnedLeftRow.classList.remove('row-hovered');
        }

        // Remove hover from pinned right row
        const pinnedRightRow = this.elements.pinnedRight?.querySelector(`[data-row-index="${rowIndex}"]`);
        if (pinnedRightRow) {
            pinnedRightRow.classList.remove('row-hovered');
        }
    }

    /**
     * Update column width in DOM directly for efficiency and synchronization
     */
    updateColumnWidthDOM(field, width) {
        const widthPx = `${width}px`;

        // Update header cell
        const headerCell = this.container.querySelector(`.snap-grid-header-cell[data-field="${field}"]`);
        if (headerCell) {
            headerCell.style.width = widthPx;
        }

        // Update all data cells for this column
        const dataCells = this.container.querySelectorAll(`.snap-grid-cell[data-field="${field}"]`);
        dataCells.forEach(cell => {
            cell.style.width = widthPx;
        });

        // For pinned columns, only update the pinned sections without full render
        if (this.isColumnPinned(field)) {
            this.updatePinnedColumnWidth(field, widthPx);
        }
    }

    /**
     * Update pinned column width without full render for better performance
     */
    updatePinnedColumnWidth(field, widthPx) {
        // Update pinned left header
        const pinnedLeftHeader = this.elements.pinnedLeftHeader?.querySelector(`.snap-grid-header-cell[data-field="${field}"]`);
        if (pinnedLeftHeader) {
            pinnedLeftHeader.style.width = widthPx;
        }

        // Update pinned right header
        const pinnedRightHeader = this.elements.pinnedRightHeader?.querySelector(`.snap-grid-header-cell[data-field="${field}"]`);
        if (pinnedRightHeader) {
            pinnedRightHeader.style.width = widthPx;
        }

        // Update pinned left data cells
        const pinnedLeftCells = this.elements.pinnedLeft?.querySelectorAll(`.snap-grid-cell[data-field="${field}"]`);
        pinnedLeftCells?.forEach(cell => {
            cell.style.width = widthPx;
        });

        // Update pinned right data cells
        const pinnedRightCells = this.elements.pinnedRight?.querySelectorAll(`.snap-grid-cell[data-field="${field}"]`);
        pinnedRightCells?.forEach(cell => {
            cell.style.width = widthPx;
        });
    }

    /**
     * Hide column
     */
    hideColumn(field) {
        this.state.hiddenColumns.add(field);
        this.render();
        this.emit('columnHidden', { field });
    }

    /**
     * Show column
     */
    showColumn(field) {
        this.state.hiddenColumns.delete(field);
        this.render();
        this.emit('columnShown', { field });
    }

    /**
     * Select row by index
     * @param {number} rowIndex - Index of the row to select
     * @param {boolean} multiSelect - Whether to allow multiple selections
     * @emits selectionChanged - { selectedRowKeys: string[], selectedData: any[] }
     */
    selectRow(rowIndex, multiSelect = false) {
        const rowData = this.state.displayData[rowIndex];
        if (!rowData || rowData.__isGroupHeader) return;

        const rowKey = this.getRowId(rowData);

        if (!multiSelect) {
            this.state.selectedRowKeys.clear();
        }

        if (this.state.selectedRowKeys.has(rowKey)) {
            this.state.selectedRowKeys.delete(rowKey);
        } else {
            this.state.selectedRowKeys.add(rowKey);
        }

        this.render();
        this.emitSelectionChanged();
    }



    /**
     * Select all visible rows
     * @emits selectionChanged - { selectedRowKeys: string[], selectedData: any[] }
     */
    selectAll() {
        this.state.selectedRowKeys.clear();
        this.state.displayData.forEach(rowData => {
            if (!rowData.__isGroupHeader) {
                const rowKey = this.getRowId(rowData);
                this.state.selectedRowKeys.add(rowKey);
            }
        });

        this.render();
        this.emitSelectionChanged();
    }

    /**
     * Clear all selections
     * @emits selectionChanged - { selectedRowKeys: string[], selectedData: any[] }
     */
    clearSelection() {
        this.state.selectedRowKeys.clear();
        this.render();
        this.emitSelectionChanged();
    }

    /**
     * Get selected data using stable keys
     * @returns {any[]} Array of selected row data objects
     */
    getSelectedData() {
        const selectedData = [];
        this.state.selectedRowKeys.forEach(key => {
            // Find row in original data by key
            const rowData = this.state.data.find(row => this.getRowId(row) === key);
            if (rowData) {
                selectedData.push(rowData);
            }
        });
        return selectedData;
    }

    /**
     * Get count of selected rows
     * @returns {number} Number of selected rows
     */
    getSelectionCount() {
        return this.state.selectedRowKeys.size;
    }

    /**
     * Emit standardized selection changed event
     * @private
     */
    emitSelectionChanged() {
        this.emit('selectionChanged', {
            selectedRowKeys: Array.from(this.state.selectedRowKeys),
            selectedData: this.getSelectedData()
        });
    }





    /**
     * Update data
     */
    updateData(newData) {
        this.config.data = newData;
        this.processData();
        this.render();
        this.emit('dataUpdated', { data: newData });
    }

    /**
     * Public resize method for external use
     */
    resize() {
        this.handleResize();
    }

    /**
     * Test method to demonstrate AND/OR filtering
     * This shows how compound filters should work
     */
    testAndOrFiltering() {
        console.log('🧪 Testing AND/OR filtering logic...');

        // Example: Filter for products where (title contains "shirt" AND price > 10) OR (title contains "mug")
        const testFilter = {
            type: 'text',
            operator: 'contains',
            value: 'shirt',
            secondOperator: 'contains',
            secondValue: 'mug',
            logicOperator: 'OR'
        };

        const testValue = 'Cool Shirt Design';
        const result = this.applyFilter(testValue, testFilter);

        console.log('🧪 Test result:', {
            testValue,
            testFilter,
            result,
            explanation: 'Should be true because "Cool Shirt Design" contains "shirt"'
        });

        // Test AND logic
        const testFilterAnd = {
            type: 'text',
            operator: 'contains',
            value: 'shirt',
            secondOperator: 'contains',
            secondValue: 'cool',
            logicOperator: 'AND'
        };

        const resultAnd = this.applyFilter(testValue, testFilterAnd);

        console.log('🧪 AND Test result:', {
            testValue,
            testFilter: testFilterAnd,
            result: resultAnd,
            explanation: 'Should be true because "Cool Shirt Design" contains both "shirt" AND "cool"'
        });

        return { orTest: result, andTest: resultAnd };
    }

    /**
     * Test method to demonstrate mixed filtering (dropdown + text + checkboxes)
     */
    testMixedFiltering() {
        console.log('🧪 Testing mixed filtering (dropdown + text + checkboxes)...');

        // Test case: Text filter + Checkbox filter
        const mixedFilter = {
            type: 'text',
            operator: 'contains',
            value: 'shirt',
            checkedValues: ['Amazon US', 'Amazon UK'] // Only these marketplaces should show
        };

        // Test different values
        const testCases = [
            { value: 'Cool Shirt', marketplace: 'Amazon US', shouldPass: true },
            { value: 'Cool Shirt', marketplace: 'Amazon CA', shouldPass: false }, // Not in checked list
            { value: 'Cool Mug', marketplace: 'Amazon US', shouldPass: false }, // Doesn't contain 'shirt'
            { value: 'Cool Mug', marketplace: 'Amazon CA', shouldPass: false }  // Neither condition met
        ];

        testCases.forEach((testCase, index) => {
            // For mixed filtering, we need to test both the text value and checkbox value
            const textResult = this.applyTextFilter(testCase.value, mixedFilter.operator, mixedFilter.value);
            const checkboxResult = mixedFilter.checkedValues && mixedFilter.checkedValues.includes(testCase.marketplace);
            const finalResult = textResult && checkboxResult;

            console.log(`🧪 Test case ${index + 1}:`, {
                value: testCase.value,
                marketplace: testCase.marketplace,
                textResult,
                checkboxResult,
                finalResult,
                expected: testCase.shouldPass,
                passed: finalResult === testCase.shouldPass ? '✅' : '❌'
            });
        });

        console.log('🧪 Mixed filtering test complete!');
    }

    /**
     * Test method for the specific scenario: text filter + unchecked checkbox
     */
    testTextFilterWithUncheckScenario() {
        console.log('🧪 Testing text filter + uncheck scenario...');

        // Scenario: Filter for "next" in text, then uncheck "Next Level" in checkbox
        const scenarioFilter = {
            type: 'text',
            operator: 'contains',
            value: 'next',
            checkedValues: [] // "Next Level" is unchecked (not in the array)
        };

        const testValue = 'Next Level';

        // This should fail because:
        // 1. Text filter passes: "Next Level" contains "next" ✅
        // 2. Checkbox filter fails: "Next Level" is not in checkedValues ❌
        // 3. Final result: false (both must pass for mixed filtering)

        const result = this.applyFilter(testValue, scenarioFilter);

        console.log('🧪 Text + Uncheck scenario:', {
            testValue,
            textFilterPasses: testValue.toLowerCase().includes('next'),
            checkboxFilterPasses: scenarioFilter.checkedValues.includes(testValue),
            finalResult: result,
            expected: false,
            testPassed: result === false ? '✅' : '❌'
        });

        return result;
    }

    /**
     * REFACTORED: Comprehensive filter testing method
     */
    testFilteringSystem() {
        console.log('🧪 TESTING COMPLETE FILTERING SYSTEM...');

        const testResults = [];

        // Test 1: Empty checkedValues (Select All unchecked) should hide all
        const emptyFilter = {
            type: 'text',
            operator: 'pleaseSelect',
            value: '',
            checkedValues: []
        };

        const test1 = this.applyFilter('Alternative', emptyFilter);
        testResults.push({
            test: 'Empty checkedValues (Select All unchecked)',
            expected: false,
            actual: test1,
            passed: test1 === false,
            explanation: 'Empty array should hide all rows'
        });

        // Test 2: Single value in checkedValues should show only that value
        const singleFilter = {
            type: 'text',
            operator: 'pleaseSelect',
            value: '',
            checkedValues: ['Alternative']
        };

        const test2a = this.applyFilter('Alternative', singleFilter);
        const test2b = this.applyFilter('Other Brand', singleFilter);
        testResults.push({
            test: 'Single value filter - matching value',
            expected: true,
            actual: test2a,
            passed: test2a === true,
            explanation: 'Value in checkedValues should pass'
        });
        testResults.push({
            test: 'Single value filter - non-matching value',
            expected: false,
            actual: test2b,
            passed: test2b === false,
            explanation: 'Value not in checkedValues should fail'
        });

        // Test 3: Multiple values in checkedValues
        const multiFilter = {
            type: 'text',
            operator: 'pleaseSelect',
            value: '',
            checkedValues: ['Alternative', 'Next Level']
        };

        const test3a = this.applyFilter('Alternative', multiFilter);
        const test3b = this.applyFilter('Next Level', multiFilter);
        const test3c = this.applyFilter('Other Brand', multiFilter);
        testResults.push({
            test: 'Multi value filter - first value',
            expected: true,
            actual: test3a,
            passed: test3a === true
        });
        testResults.push({
            test: 'Multi value filter - second value',
            expected: true,
            actual: test3b,
            passed: test3b === true
        });
        testResults.push({
            test: 'Multi value filter - excluded value',
            expected: false,
            actual: test3c,
            passed: test3c === false
        });

        // Print results
        console.log('🧪 FILTER TEST RESULTS:');
        testResults.forEach((result, index) => {
            const status = result.passed ? '✅ PASS' : '❌ FAIL';
            console.log(`${index + 1}. ${status} - ${result.test}`);
            console.log(`   Expected: ${result.expected}, Got: ${result.actual}`);
            if (result.explanation) {
                console.log(`   ${result.explanation}`);
            }
        });

        const passedCount = testResults.filter(r => r.passed).length;
        console.log(`🧪 SUMMARY: ${passedCount}/${testResults.length} tests passed`);

        return testResults;
    }

    /**
     * Export data to CSV
     */
    exportCSV(options = {}) {
        const {
            selectedOnly = false,
            visibleOnly = true,
            filename = 'grid-export.csv'
        } = options;

        // Determine which columns to include
        let columns = visibleOnly
            ? this.config.columns.filter(col => !this.state.hiddenColumns.has(col.field))
            : this.config.columns;

        // Sort columns by order if defined
        if (this.state.columnOrder && this.state.columnOrder.length > 0) {
            columns = columns.sort((a, b) => {
                const aIndex = this.state.columnOrder.indexOf(a.field);
                const bIndex = this.state.columnOrder.indexOf(b.field);
                return aIndex - bIndex;
            });
        }

        // Determine which rows to include - always exclude group headers
        let rows;
        if (selectedOnly) {
            rows = this.getSelectedData().filter(row => !row.__isGroupHeader);
        } else {
            rows = visibleOnly
                ? this.state.displayData.filter(row => !row.__isGroupHeader)
                : this.state.data.filter(row => !row.__isGroupHeader);
        }

        // Build CSV content
        const csvContent = this.buildCSVContent(columns, rows);

        // Create and trigger download
        this.downloadCSV(csvContent, filename);

        this.emit('csvExported', {
            filename,
            rowCount: rows.length,
            columnCount: columns.length
        });
    }

    /**
     * Build CSV content from columns and rows
     */
    buildCSVContent(columns, rows) {
        const lines = [];

        // Header row
        const headers = columns.map(col => this.escapeCSVValue(col.headerName || col.field));
        lines.push(headers.join(','));

        // Data rows
        rows.forEach(row => {
            const values = columns.map(col => {
                const value = this.getCellValue(row, col);
                let displayValue = value;

                // Format values for CSV
                if (col.type === 'currency') {
                    displayValue = this.formatCurrency(value, col.currencyFormat);
                } else if (col.type === 'number') {
                    displayValue = this.formatNumber(value, col.numberFormat);
                } else if (col.type === 'percentage') {
                    displayValue = this.formatPercentage(value, col.percentageFormat);
                } else if (col.type === 'date') {
                    displayValue = this.formatDate(value, col.dateFormat);
                } else if (col.type === 'boolean') {
                    displayValue = Boolean(value) ? 'Yes' : 'No';
                }

                return this.escapeCSVValue(displayValue);
            });
            lines.push(values.join(','));
        });

        return lines.join('\n');
    }

    /**
     * Escape CSV value
     */
    escapeCSVValue(value) {
        if (value == null) return '';

        const str = String(value);

        // If value contains comma, newline, or quote, wrap in quotes and escape quotes
        if (str.includes(',') || str.includes('\n') || str.includes('"')) {
            return '"' + str.replace(/"/g, '""') + '"';
        }

        return str;
    }

    /**
     * Download CSV content as file
     */
    downloadCSV(content, filename) {
        const blob = new Blob(['\uFEFF' + content], {
            type: 'text/csv;charset=utf-8;'
        }); // \uFEFF is BOM for UTF-8

        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up object URL
        setTimeout(() => URL.revokeObjectURL(url), 100);
    }

    /**
     * Add event listener
     */
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }

    /**
     * Remove event listener
     */
    off(event, handler) {
        if (this.eventHandlers.has(event)) {
            const handlers = this.eventHandlers.get(event);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    /**
     * Emit event
     */
    emit(event, data = {}) {
        if (this.eventHandlers.has(event)) {
            this.eventHandlers.get(event).forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in event handler for ${event}:`, error);
                }
            });
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        this.elements.overlay.innerHTML = `
            <div class="snap-grid-error">
                <div class="error-icon">⚠️</div>
                <div class="error-message">${message}</div>
                <button class="error-close" onclick="this.parentElement.parentElement.style.display='none'">×</button>
            </div>
        `;
        this.elements.overlay.style.display = 'flex';
    }

    /**
     * Render footer
     */
    renderFooter() {
        // Always show the stats footer, regardless of pagination setting
        this.elements.footer.style.display = 'block';
        
        if (!this.config.pagination) {
            // If pagination is disabled, just show the stats footer (already created in createGridStructure)
            return;
        }

        // Get total before pagination
        const totalRows = this.state.sortedData.length;
        const pageSize = this.config.pageSize;
        const totalPages = Math.ceil(totalRows / pageSize);
        const currentPage = this.state.pageIndex + 1;
        const startRow = this.state.pageIndex * pageSize + 1;
        const endRow = Math.min(startRow + pageSize - 1, totalRows);

        this.elements.footer.innerHTML = `
            <div class="snap-grid-pagination">
                <span class="pagination-info">
                    Showing ${startRow}-${endRow} of ${totalRows} rows
                </span>
                <div class="pagination-controls">
                    <button class="pagination-btn" data-action="first" ${this.state.pageIndex === 0 ? 'disabled' : ''}>First</button>
                    <button class="pagination-btn" data-action="prev" ${this.state.pageIndex === 0 ? 'disabled' : ''}>Previous</button>
                    <span class="pagination-pages">Page ${currentPage} of ${totalPages}</span>
                    <button class="pagination-btn" data-action="next" ${this.state.pageIndex >= totalPages - 1 ? 'disabled' : ''}>Next</button>
                    <button class="pagination-btn" data-action="last" ${this.state.pageIndex >= totalPages - 1 ? 'disabled' : ''}>Last</button>
                </div>
            </div>
        `;

        // Add event listeners for pagination buttons
        this.elements.footer.querySelectorAll('.pagination-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.getAttribute('data-action');
                this.handlePaginationAction(action);
            });
        });
    }

    /**
     * Handle pagination button clicks
     */
    handlePaginationAction(action) {
        const totalRows = this.state.sortedData.length;
        const totalPages = Math.ceil(totalRows / this.config.pageSize);

        switch (action) {
            case 'first':
                this.state.pageIndex = 0;
                break;
            case 'prev':
                this.state.pageIndex = Math.max(0, this.state.pageIndex - 1);
                break;
            case 'next':
                this.state.pageIndex = Math.min(totalPages - 1, this.state.pageIndex + 1);
                break;
            case 'last':
                this.state.pageIndex = totalPages - 1;
                break;
        }

        this.processData();
        this.render();
        this.emit('pageChanged', { pageIndex: this.state.pageIndex });
    }

    /**
     * Destroy grid and cleanup
     */
    destroy() {
        // Remove event listeners
        this.boundHandlers.forEach((handler, event) => {
            if (event === 'scroll' && this.elements?.viewport) {
                this.elements.viewport.removeEventListener('scroll', handler);
            } else if (event === 'pinnedScroll') {
                if (this.elements?.pinnedLeft) {
                    this.elements.pinnedLeft.removeEventListener('scroll', handler);
                }
                if (this.elements?.pinnedRight) {
                    this.elements.pinnedRight.removeEventListener('scroll', handler);
                }
            } else if (event === 'resize') {
                window.removeEventListener('resize', handler);
            } else if (this.container) {
                this.container.removeEventListener(event, handler);
            }
        });

        // Clear all active dialogs and menus
        this.activeDialogs.forEach(dialog => {
            if (dialog.parentNode) {
                dialog.remove();
            }
        });
        this.activeDialogs.clear();

        // Hide specific dialogs/menus with null checks
        try {
            this.hideColumnMenu();
        } catch (e) {
            console.warn('Error hiding column menu:', e);
        }

        try {
            this.hideFilterDialog();
        } catch (e) {
            console.warn('Error hiding filter dialog:', e);
        }

        try {
            this.hideColumnChooser();
        } catch (e) {
            console.warn('Error hiding column chooser:', e);
        }



        // Disconnect performance observer if exists
        if (this.performanceObserver) {
            this.performanceObserver.disconnect();
            this.performanceObserver = null;
        }

        // Clear references with null checks
        if (this.eventHandlers) {
            this.eventHandlers.clear();
        }
        if (this.boundHandlers) {
            this.boundHandlers.clear();
        }

        // Clean up DOM elements
        if (this.elements?.overlay) {
            this.elements.overlay.innerHTML = '';
        }

        this.elements = null;
        this._measureCtx = null;
        this._cachedFonts = null;

        // Remove grid classes but preserve other classes
        if (this.container) {
            this.container.classList.remove('snap-grid', this.config?.theme || 'default');
            this.container.innerHTML = '';
        }

        this.emit('destroyed');
    }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SnapGrid;
} else if (typeof window !== 'undefined') {
    window.SnapGrid = SnapGrid;
}
